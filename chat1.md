# 背景
- 我重新设计了Dashboard，并已经创建了 Dashub 实体及对应的数据表；
- 所有汇总数据都存放到 meta_dashubs 表中，可以按天划分数据，必须填写day字段；
- 统计数据由后端定时任务负责计算并更新到数据库；


## Dashub 实体对应的SQL：
**该表按类型记录所有统计汇总数据**

```sql
CREATE TABLE IF NOT EXISTS "meta_dashubs" (
  id integer PRIMARY KEY AUTOINCREMENT,
  created_at datetime,
  updated_at datetime,
  deleted_at datetime,
  organize integer, -- 组织ID，通常是用户ID，表示这个数据归属于哪个组织
  day text, -- 统计日期，是这样的字串：'2026-01-01'，表示统计数据的日期
  kind integer, -- 统计数据的类型，如：日活，广告展示数、点击数，需要在代码中定义对应的ID
  target integer DEFAULT(0), -- 统计目标，如：当前统计数据如果属于APP则填APP id、广告则埴广告的ID
  nums real DEFAULT(0) -- 对应数值
);
```

# 任务

调整 Dashboard 实现思路：
- 先定义出所有需要展示的项目，并为每个项目定义出数值型ID；
- 再为每个类型生成一周左右的mock数据，并生成sql文件，由我来负责导入数据库；
- 修改 Dashboard 页面，通过图表展示（图表参考 `demo` 项目）；
- 同一类型但不同周期的数据，放在同一个图表中展示，如：日活、周活、月活；
- 优先使用图标展示，但没有日期维度的数据使用Card展示，如：总用户数、总展示数、总点击数等；




开发一个符系统一致风格的，精美的，使用加密货币充值的页面（只需实现单页页面，不需要开发Golang代码）