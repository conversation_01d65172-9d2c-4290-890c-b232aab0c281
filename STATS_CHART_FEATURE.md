# 统计图表功能实现文档

## 功能概述

为广告计划的"计划名称"添加了点击打开统计图表功能，用户点击计划名称后会在弹出层中展示数据统计图表。

## 实现特点

✅ **独立性和通用性**: 新功能以独立组件方式开发，可在其他页面重复使用
✅ **最小侵入**: 对原有代码的修改最小化，仅在必要位置添加功能
✅ **弹出层展示**: 使用 Element Plus Dialog 组件实现弹出层
✅ **多类型支持**: 支持 APP、广告、广告计划三种数据类型
✅ **图表化展示**: 使用 ECharts 实现数据可视化

## 文件结构

```
web/src/
├── components/
│   └── StatsChart/
│       └── StatsChart.vue          # 统计图表组件（新增）
├── view/
│   ├── meta/
│   │   └── campaign/
│   │       └── campaign.vue        # 广告计划页面（修改）
│   └── test/
│       └── StatsChartTest.vue      # 测试页面（新增）
└── router/
    └── index.js                    # 路由配置（添加测试路由）
```

## 核心组件：StatsChart.vue

### 组件参数
- `modelValue`: 控制弹出层显示/隐藏
- `targetId`: 统计目标ID
- `targetType`: 目标类型（1:APP，2:广告，3:广告计划）

### 图表类型
1. **默认图表**: 广告展示数、点击数（所有类型都有）
2. **APP类型**: 额外显示日活用户数图表
3. **广告计划类型**: 显示计划及关联广告的合并数据

### 数据获取逻辑
- 使用 `getDashubList` API 获取统计数据
- 根据 `kind` 字段区分不同的统计类型：
  - `2001`: 广告展示数 (DASHUB_AD_IMPRESSIONS)
  - `2002`: 广告点击数 (DASHUB_AD_CLICKS)
  - `1001`: 日活用户数 (DASHUB_DAILY_ACTIVE_USERS)

### 广告计划特殊处理
1. 先加载广告计划自身的统计数据
2. 通过 `findCampaign` API 获取关联的广告ID列表
3. 为每个关联广告加载统计数据
4. 在同一图表中以不同颜色和线型展示多条数据曲线

## 修改的文件

### 1. campaign.vue
```vue
<!-- 修改计划名称列，添加点击事件 -->
<el-table-column sortable align="left" label="计划名称" prop="name" width="120">
    <template #default="scope">
        <el-button type="text" @click="openStatsChart(scope.row)" class="campaign-name-link">
            {{ scope.row.name }}
        </el-button>
    </template>
</el-table-column>

<!-- 添加统计图表组件 -->
<StatsChart
  v-model="statsChartVisible"
  :target-id="statsChartTargetId"
  :target-type="statsChartTargetType"
/>
```

### 2. 添加的方法和数据
```javascript
// 统计图表相关
const statsChartVisible = ref(false)
const statsChartTargetId = ref(0)
const statsChartTargetType = ref(3) // 默认为广告计划类型

// 打开统计图表
const openStatsChart = (row) => {
  statsChartTargetId.value = row.ID
  statsChartTargetType.value = 3 // 广告计划类型
  statsChartVisible.value = true
}
```

## 数据库结构

### meta_dashubs 表
```sql
CREATE TABLE meta_dashubs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  day VARCHAR(50) NOT NULL COMMENT '统计日期',
  kind INT NOT NULL COMMENT '统计数据类型',
  target_type INT DEFAULT 0 COMMENT '统计目标类型',
  target INT NOT NULL COMMENT '统计目标ID',
  nums FLOAT DEFAULT 0 COMMENT '统计数值',
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

### 统计类型常量（来自 dashub_constants.go）
- `1001`: 日活用户数
- `2001`: 广告展示数
- `2002`: 广告点击数

### 目标类型
- `1`: APP
- `2`: 广告
- `3`: 广告计划

## 测试数据

已插入测试数据用于验证功能：
```sql
-- 广告计划统计数据 (target_type=3, target=1)
INSERT INTO meta_dashubs (day, kind, target_type, target, nums) VALUES
('2025-06-15', 2001, 3, 1, 1500), -- 展示数
('2025-06-16', 2001, 3, 1, 1800),
('2025-06-17', 2001, 3, 1, 2100),
('2025-06-15', 2002, 3, 1, 150),  -- 点击数
('2025-06-16', 2002, 3, 1, 180),
('2025-06-17', 2002, 3, 1, 210);

-- 广告统计数据 (target_type=2, target=1)
INSERT INTO meta_dashubs (day, kind, target_type, target, nums) VALUES
('2025-06-15', 2001, 2, 1, 800),  -- 展示数
('2025-06-16', 2001, 2, 1, 900),
('2025-06-17', 2001, 2, 1, 1000),
('2025-06-15', 2002, 2, 1, 80),   -- 点击数
('2025-06-16', 2002, 2, 1, 90),
('2025-06-17', 2002, 2, 1, 100);
```

## 使用方法

1. **在广告计划页面**: 点击任意计划名称即可打开统计图表
2. **测试页面**: 访问 `http://localhost:8082/#/test/stats-chart` 进行功能测试
3. **其他页面**: 导入 `StatsChart` 组件并传入相应参数即可使用

## 技术栈

- **前端框架**: Vue 3 + Element Plus
- **图表库**: ECharts 5.5.1
- **HTTP 客户端**: Axios
- **后端 API**: Gin + GORM

## 扩展性

该组件设计具有良好的扩展性：
1. 可轻松添加新的图表类型
2. 支持新的统计数据类型
3. 可在任意页面中重复使用
4. 支持自定义图表样式和配置

## 注意事项

1. 确保后端 API 正常运行
2. 数据库中需要有相应的统计数据
3. 图表组件会自动处理数据为空的情况
4. 支持响应式设计，适配不同屏幕尺寸
