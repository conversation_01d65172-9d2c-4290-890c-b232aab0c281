-- Dash<PERSON> Mock Data for Role 8881, User 3
-- Generated at: 2025-06-10 14:29:12

-- 清理用户 3 的旧数据（可选）
-- DELETE FROM meta_dashubs WHERE organize = 3;

-- 插入mock数据
INSERT INTO meta_dashubs (created_at, updated_at, organize, day, kind, target, nums) VALUES
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-04', 1001, 0, 5918.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-04', 1002, 0, 8877.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-04', 1003, 0, 11836.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-04', 1004, 0, 10000.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-04', 1005, 0, 84.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-04', 1006, 0, 5918.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-04', 2001, 0, 60959.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-04', 2002, 0, 1105.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-04', 2004, 0, 73.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-04', 3005, 0, 1502.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-04', 3006, 0, 10514.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-04', 3007, 0, 45060.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-04', 3009, 0, 40000.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-05', 1001, 0, 5848.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-05', 1002, 0, 8772.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-05', 1003, 0, 11696.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-05', 1004, 0, 10050.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-05', 1005, 0, 60.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-05', 1006, 0, 5848.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-05', 2001, 0, 57357.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-05', 2002, 0, 672.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-05', 2004, 0, 74.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-05', 3005, 0, 1949.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-05', 3006, 0, 13643.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-05', 3007, 0, 58470.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-05', 3009, 0, 40800.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-06', 1001, 0, 5157.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-06', 1002, 0, 7735.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-06', 1003, 0, 10314.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-06', 1004, 0, 10100.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-06', 1005, 0, 83.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-06', 1006, 0, 5157.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-06', 2001, 0, 57410.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-06', 2002, 0, 979.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-06', 2004, 0, 63.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-06', 3005, 0, 1590.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-06', 3006, 0, 11130.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-06', 3007, 0, 47700.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-06', 3009, 0, 41600.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-07', 1001, 0, 5886.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-07', 1002, 0, 8829.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-07', 1003, 0, 11772.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-07', 1004, 0, 10150.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-07', 1005, 0, 56.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-07', 1006, 0, 5886.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-07', 2001, 0, 59723.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-07', 2002, 0, 724.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-07', 2004, 0, 71.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-07', 3005, 0, 1796.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-07', 3006, 0, 12572.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-07', 3007, 0, 53880.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-07', 3009, 0, 42400.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-08', 1001, 0, 5590.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-08', 1002, 0, 8385.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-08', 1003, 0, 11180.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-08', 1004, 0, 10200.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-08', 1005, 0, 52.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-08', 1006, 0, 5590.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-08', 2001, 0, 62787.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-08', 2002, 0, 1065.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-08', 2004, 0, 99.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-08', 3005, 0, 810.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-08', 3006, 0, 5670.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-08', 3007, 0, 24300.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-08', 3009, 0, 43200.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-09', 1001, 0, 5700.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-09', 1002, 0, 8550.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-09', 1003, 0, 11400.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-09', 1004, 0, 10250.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-09', 1005, 0, 51.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-09', 1006, 0, 5700.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-09', 2001, 0, 55222.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-09', 2002, 0, 946.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-09', 2004, 0, 56.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-09', 3005, 0, 2010.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-09', 3006, 0, 14070.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-09', 3007, 0, 60300.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-09', 3009, 0, 44000.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-10', 1001, 0, 5916.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-10', 1002, 0, 8874.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-10', 1003, 0, 11832.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-10', 1004, 0, 10300.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-10', 1005, 0, 72.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-10', 1006, 0, 5916.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-10', 2001, 0, 66316.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-10', 2002, 0, 667.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-10', 2004, 0, 44.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-10', 3005, 0, 1987.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-10', 3006, 0, 13909.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-10', 3007, 0, 59610.00),
('2025-06-10 14:29:12', '2025-06-10 14:29:12', 3, '2025-06-10', 3009, 0, 44800.00);
