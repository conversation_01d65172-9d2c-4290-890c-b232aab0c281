# 广告SDK接口文档

## 概述

本文档描述了广告SDK的两个核心接口：获取广告列表和上报广告事件。这些接口支持匿名访问，无需授权。

## 接口列表

### 1. 获取广告列表

**接口地址：** `POST /sdk/ads`

**功能描述：** 根据应用包名、广告位ID和广告类型ID获取符合条件的广告列表

**请求参数：**
```json
{
  "package_id": "com.example.app",      // 应用包名（必填）
  "ad_position_id": 1,                  // 广告位ID（必填）
  "ad_type_id": 1,                      // 广告类型ID（必填）
  "device_info": {                      // 设备信息（可选）
    "device_id": "device123",
    "platform": "android",
    "os_version": "11.0",
    "app_version": "1.0.0",
    "network_type": "wifi",
    "country": "CN",
    "province": "Beijing",
    "city": "Beijing",
    "screen_width": 1080,
    "screen_height": 1920
  }
}
```

**响应数据：**
```json
{
  "code": 0,
  "msg": "获取成功",
  "data": {
    "ads": [
      {
        "id": 1,
        "campaign_id": 1,
        "name": "广告名称",
        "title": "广告标题",
        "description": "广告描述",
        "media_url": "https://example.com/ad.jpg",
        "click_url": "https://example.com/click",
        "ad_type_id": 1,
        "ad_action_id": 1,
        "ad_position_id": 1,
        "bid_amount": 0.5,
        "bid_type": "CPC",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

**实现特性：**
- 自动检测今日首次请求，更新应用日活统计
- 根据投放计划条件过滤广告
- 按出价排序，价高者优先
- 使用加权随机算法选择3个广告展示
- 支持Redis缓存状态管理

### 2. 上报广告事件

**接口地址：** `POST /sdk/report`

**功能描述：** 批量上报广告展示、点击等事件

**请求参数：**
```json
{
  "events": [
    {
      "package_id": "com.example.app",   // 应用包名（必填）
      "campaign_id": 1,                  // 投放计划ID（必填）
      "ad_id": 1,                        // 广告ID（必填）
      "action_type": 1,                  // 行为类型：1-展示，2-点击（必填）
      "ad_position_id": 1,               // 广告位ID（必填）
      "user_id": "user123",              // 用户ID（可选）
      "event_time": "2024-01-01T00:00:00Z", // 事件时间（可选，默认当前时间）
      "device_info": {                   // 设备信息（可选）
        "device_id": "device123",
        "platform": "android",
        "country": "CN",
        "province": "Beijing",
        "city": "Beijing"
      }
    }
  ]
}
```

**响应数据：**
```json
{
  "code": 0,
  "msg": "上报成功",
  "data": {
    "success": true,
    "message": "所有事件处理成功",
    "processed_count": 1,
    "failed_events": []
  }
}
```

**失败响应示例：**
```json
{
  "code": 7,
  "msg": "部分事件上报失败",
  "data": {
    "success": false,
    "message": "部分事件处理失败，成功: 1, 失败: 1",
    "processed_count": 1,
    "failed_events": [
      {
        "index": 1,
        "reason": "广告不存在",
        "ad_id": 999,
        "campaign_id": 999
      }
    ]
  }
}
```

**实现特性：**
- 支持批量事件上报
- 多维度统计更新（APP、广告、投放计划）
- 实时计费扣费（CPC按次扣费，CPM按千次展示扣费）
- 自动账户状态检查和暂停逻辑
- 预算限制检查（总预算、日预算）
- 生成详细的消费记录

## 计费规则

### CPC（按点击计费）
- 仅在点击事件时扣费
- 扣费金额 = 投放计划出价金额

### CPM（按千次展示计费）
- 仅在展示事件时扣费
- 扣费金额 = 投放计划出价金额 / 1000

### 自动暂停机制
1. **账户余额不足**：暂停该用户所有正在进行的广告计划
2. **总预算耗尽**：暂停该投放计划
3. **日预算耗尽**：临时标记暂停，次日自动恢复

## 缓存策略

使用Redis缓存以下状态：
- `daily_active:{date}:{app_id}` - 日活标记
- `campaign_paused:{campaign_id}` - 计划暂停状态
- `campaign_daily_paused:{campaign_id}:{date}` - 日预算暂停状态
- `dashub_stat:{kind}:{target}:{date}` - 统计数据增量

## 错误码说明

- `0` - 成功
- `7` - 失败

## 测试方法

可以使用提供的测试脚本：
```bash
go run test_sdk.go
```

## AppId逻辑说明

### 核心原则

广告投放计划的目标APP设置决定了广告的投放范围：
- **未设置target_apps**：计划适用于所有APP
- **设置了target_apps**：计划仅适用于指定的APP列表

### 获取广告列表时的AppId逻辑

1. **不提供package_id**：
   - 获取所有未设置target_apps的投放计划的广告
   - 不会更新日活统计
   - 适用于跨APP的广告展示场景

2. **提供package_id**：
   - 验证APP是否存在且启用广告
   - 更新该APP的日活统计
   - 获取以下投放计划的广告：
     - 未设置target_apps的计划（适用于所有APP）
     - 设置了target_apps且包含当前APP的计划

### 投放计划的目标APP过滤逻辑

```
if 投放计划.target_apps == null or 投放计划.target_apps == []:
    return true  // 适用于所有APP
else if 请求.package_id == null:
    return false // 请求未指定APP，但计划有APP限制
else:
    return 请求的APP在投放计划的target_apps列表中
```

### 事件上报时的AppId逻辑

- 事件上报时package_id是必填的，因为需要知道事件来源于哪个APP
- 系统会验证APP是否存在
- 统计数据会按APP维度进行更新
- 消费记录会关联到具体的APP

## 注意事项

1. 所有接口均为POST请求
2. Content-Type必须为application/json
3. 接口支持匿名访问，无需认证
4. 建议在生产环境中添加适当的限流和监控
5. 设备信息虽然可选，但建议提供以便更好的广告投放和统计
6. package_id在获取广告时可选，在事件上报时必填
