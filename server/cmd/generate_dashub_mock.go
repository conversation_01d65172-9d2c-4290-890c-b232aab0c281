package main

import (
	"fmt"
	"os"
	"path/filepath"

	"github.com/flipped-aurora/gin-vue-admin/server/utils"
)

func main() {
	// 生成7天的mock数据
	generator := utils.NewDashubMockGenerator(7)
	
	// 创建输出目录
	outputDir := "mock_data"
	if err := os.Mkdir<PERSON>ll(outputDir, 0755); err != nil {
		fmt.Printf("创建输出目录失败: %v\n", err)
		return
	}
	
	// 生成全量数据SQL文件
	allDataSQL := generator.GenerateSQLFile()
	allDataFile := filepath.Join(outputDir, "dashub_mock_all.sql")
	if err := os.WriteFile(allDataFile, []byte(allDataSQL), 0644); err != nil {
		fmt.Printf("写入全量数据文件失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 全量数据SQL文件已生成: %s\n", allDataFile)
	
	// 为不同角色生成数据
	roles := map[string]struct {
		AuthorityID uint
		UserID      uint
		Name        string
	}{
		"admin": {
			AuthorityID: 888,
			UserID:      1,
			Name:        "系统管理员",
		},
		"advertiser": {
			AuthorityID: 9528,
			UserID:      2,
			Name:        "广告主",
		},
		"developer": {
			AuthorityID: 8881,
			UserID:      3,
			Name:        "APP开发者",
		},
	}
	
	for roleKey, role := range roles {
		roleSQL := generator.GenerateRoleSQLFile(role.AuthorityID, role.UserID)
		roleFile := filepath.Join(outputDir, fmt.Sprintf("dashub_mock_%s.sql", roleKey))
		if err := os.WriteFile(roleFile, []byte(roleSQL), 0644); err != nil {
			fmt.Printf("写入%s数据文件失败: %v\n", role.Name, err)
			continue
		}
		fmt.Printf("✅ %s数据SQL文件已生成: %s\n", role.Name, roleFile)
	}
	
	// 生成合并的SQL文件
	combinedSQL := generateCombinedSQL(generator)
	combinedFile := filepath.Join(outputDir, "dashub_mock_combined.sql")
	if err := os.WriteFile(combinedFile, []byte(combinedSQL), 0644); err != nil {
		fmt.Printf("写入合并数据文件失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 合并数据SQL文件已生成: %s\n", combinedFile)
	
	fmt.Println("\n🎉 所有mock数据文件生成完成！")
	fmt.Println("\n📋 使用说明：")
	fmt.Println("1. dashub_mock_all.sql - 包含所有类型的数据（organize=0）")
	fmt.Println("2. dashub_mock_admin.sql - 系统管理员数据（用户ID=1）")
	fmt.Println("3. dashub_mock_advertiser.sql - 广告主数据（用户ID=2）")
	fmt.Println("4. dashub_mock_developer.sql - APP开发者数据（用户ID=3）")
	fmt.Println("5. dashub_mock_combined.sql - 包含所有角色的数据")
	fmt.Println("\n💡 建议使用 dashub_mock_combined.sql 来导入完整的测试数据")
}

// generateCombinedSQL 生成包含所有角色数据的合并SQL文件
func generateCombinedSQL(generator *utils.DashubMockGenerator) string {
	sql := "-- Dashub Combined Mock Data\n"
	sql += "-- 包含所有角色的测试数据\n"
	sql += "-- Generated at: " + generator.StartDate.Format("2006-01-02 15:04:05") + "\n\n"
	
	sql += "-- 清理所有旧的mock数据\n"
	sql += "DELETE FROM meta_dashubs WHERE organize IN (0, 1, 2, 3);\n\n"
	
	// 角色配置
	roles := []struct {
		AuthorityID uint
		UserID      uint
		Name        string
	}{
		{888, 1, "系统管理员"},
		{9528, 2, "广告主"},
		{8881, 3, "APP开发者"},
	}
	
	// 为每个角色生成数据
	for _, role := range roles {
		sql += fmt.Sprintf("-- %s (用户ID: %d, 角色ID: %d) 的数据\n", role.Name, role.UserID, role.AuthorityID)
		
		data := generator.GenerateRoleMockData(role.AuthorityID, role.UserID)
		if len(data) == 0 {
			sql += "-- 该角色无可用数据\n\n"
			continue
		}
		
		sql += "INSERT INTO meta_dashubs (created_at, updated_at, organize, day, kind, target, nums) VALUES\n"
		
		for i, item := range data {
			now := generator.StartDate.Format("2006-01-02 15:04:05")
			sql += fmt.Sprintf("('%s', '%s', %d, '%s', %d, %d, %.2f)",
				now, now, *item.Organize, *item.Day, *item.Kind, *item.Target, *item.Nums)
			
			if i < len(data)-1 {
				sql += ",\n"
			} else {
				sql += ";\n\n"
			}
		}
	}
	
	sql += "-- 数据插入完成\n"
	sql += "-- 可以通过以下查询验证数据：\n"
	sql += "-- SELECT organize, COUNT(*) as count FROM meta_dashubs GROUP BY organize;\n"
	
	return sql
}
