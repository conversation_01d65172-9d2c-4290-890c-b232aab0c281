package main

import (
	"bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	meta "github.com/flipped-aurora/gin-vue-admin/server/api/v1/handler"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
	"gomisc/common/decoder"
	"io"

	"net/http"
	"os"
	"strconv"
)

var (
	uid     = 14
	version = 120
	//pid     = "com.metasteam.cn"
	pid    = "com.movies.qd.kktv"
	dcoder = decoder.HDecoder{}
	//endpoint = "http://api.tikoktv.com/v1/request"
	//endpoint = "http://api.youkutv.xyz/v1/request"
	//endpoint = "http://127.0.0.1:8899/v1/request"
	//endpoint = "http://127.0.0.1:8899/v1/request"
	endpoint = "https://api.amountads.com/v1/request"

	sendreq = `{"channel":"MV-ZB","code":100205,"data":"","did":"20242369d6a1a30088a4f8d5f75e4776f","fakeString":"F","packageName":"com.movies.qd.cn.debug","pid":"com.metasteam.cn","session":"","sign":0,"uid":14,"uri":"","version":249}`
)

func main() {
	//testLogin()
	//testRaw()
	//testAuth()
	// testAdRequest()
	var tid = 0
	if len(os.Args) > 1 {
		tid, _ = strconv.Atoi(os.Args[1])
	}

	switch tid {
	case 999:
		testAdsysV2()
		break

	default:
		//testHttpGet(tid)
		//os.Args[2]
		data, err := httpSend(tid, nil)
		if err != nil {
			fmt.Println(err)
		}

		fmt.Println(string(data))
	}
}

func testAdsysV2() {

	req := request.GetAdsRequest{
		AdPositionId: 0,
		AdTypeId:     0,
	}

	data, err := httpSend(meta.AD_LIST, req)
	if err != nil {
		fmt.Println(err)
	}

	fmt.Println(string(data))
}

func testHttpGet(code int) {
	data, err := httpSend(code, nil)
	if err != nil {
		fmt.Println(err)
	}

	fmt.Println(string(data))
}

func httpSend(code int, raw any) ([]byte, error) {
	var data []byte = nil

	switch raw.(type) {
	case []byte:
		data = raw.([]byte)
		break
	case string:
		data = []byte(raw.(string))
		break
	default:
		data, _ = json.Marshal(raw)
	}

	postw := meta.RootRequest{
		Code:    code,
		Pid:     pid,
		Uid:     int32(uid),
		Data:    data,
		Version: version,
	}

	data, _ = json.Marshal(postw)

	return httpSendRaw(code, data)
}

func httpSendRaw(code int, data []byte) ([]byte, error) {
	// 创建一个4字节的buffer，用于存储整数值
	flag := make([]byte, 4)
	binary.BigEndian.PutUint32(flag, uint32(code))

	// 加密数据
	encryptedData := dcoder.Encrypt(data)

	// 将flag和加密后的数据合并
	body := append(flag, encryptedData...)

	// 创建HTTP请求
	req, err := http.NewRequest("POST", endpoint, bytes.NewBuffer(body))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/octet-stream") // 根据实际情况设置Content-Type

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	for k, vs := range resp.Header {
		fmt.Println("header:", k, vs[0])
	}

	// 读取响应体
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 检查是否需要解密
	if dcoder.IsSupport(responseBody) {
		responseBody = dcoder.Decrypt(responseBody)
	}

	return responseBody, nil
}
