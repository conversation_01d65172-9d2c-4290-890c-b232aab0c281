
















# 任务

开发广告SDK接口

## 全局要求
- 可以匿名访问（无需授权），请求地址 /sdk/xxx；
- 使用redis缓存状态数据（如今天第一次广告请求状态、广告投放计划暂停状态等等）；



## 接口及实现

### 获取广告列表
- 请求地址：/sdk/ads
- 请求方式：POST
- 请求参数：当前包名，广告位ID，广告类型ID
- 响应数据：广告列表
- 实现方式：
    * 今天第一次广告请求需要为APP的日活+1；
    * 先根据请求参数结合广告投放计划(meta.Campaign)的各种条件过滤出符合条件的投放计划；
    * 再根据广告投放计划的出价（没有出价则自动计算默认出价）排序；
    * 再读取每个计划关联的广告列表，根据广告类型、广告位过滤出符合条件的广告；
    * 根据各个广告的价格，用加权平均法选出需要展示的广告（目标是保证价高者优先展示，但价低者也有小量展示机会）；
    * 返回3个展示的广告


### 上报广告事件
- 请求地址：/sdk/report
- 请求方式：POST
- 请求参数：广告事件（当前包名，广告投放计划ID，广告ID，行为类型ID（展示、点击），设备信息）
- 响应数据：bool
- 实现方式：
    * 请求参数以列表形式上报，每条记录表示一次事件，可以合并多次一起上报；
    * 根据广告事件对APP进行以下维度的加一统计：总展示/点击数、今天展示/点击数；
    * 根据广告事件对广告进行以下维度的加一统计：总展示/点击数、今天展示/点击数；
    * 根据广告事件对广告投放计划进行以下维度的加一统计：总展示/点击数、今天展示/点击数；
    * 根据事件按出价实时扣费：
        - CPC 按次扣费；
        - CPM 不需每次扣费，按每千次展示扣费；
        - 包月广告按月收费；
    * 如果发生扣费后：
        - 帐户欠费、或不足以支持下一次扣费，暂停帐号内所有正在进行的广告计划；
        - 已经消费完广告投放计划总预算，设置该广告投放计划为暂停状态；
        - 已经消费完广告投放计划当日预算，保持该广告投放计划的状态，但临时标记以防止此计划被再次展示；
        - 最后生一条扣费记录 `ConsumeRecord`