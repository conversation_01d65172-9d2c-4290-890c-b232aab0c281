package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AdTypeRouter struct {}

// InitAdTypeRouter 初始化 广告类型管理 路由信息
func (s *AdTypeRouter) InitAdTypeRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	adtypeRouter := Router.Group("adtype").Use(middleware.OperationRecord())
	adtypeRouterWithoutRecord := Router.Group("adtype")
	adtypeRouterWithoutAuth := PublicRouter.Group("adtype")
	{
		adtypeRouter.POST("createAdType", adtypeApi.CreateAdType)   // 新建广告类型管理
		adtypeRouter.DELETE("deleteAdType", adtypeApi.DeleteAdType) // 删除广告类型管理
		adtypeRouter.DELETE("deleteAdTypeByIds", adtypeApi.DeleteAdTypeByIds) // 批量删除广告类型管理
		adtypeRouter.PUT("updateAdType", adtypeApi.UpdateAdType)    // 更新广告类型管理
	}
	{
		adtypeRouterWithoutRecord.GET("findAdType", adtypeApi.FindAdType)        // 根据ID获取广告类型管理
		adtypeRouterWithoutRecord.GET("getAdTypeList", adtypeApi.GetAdTypeList)  // 获取广告类型管理列表
	}
	{
	    adtypeRouterWithoutAuth.GET("getAdTypePublic", adtypeApi.GetAdTypePublic)  // 广告类型管理开放接口
	}
}
