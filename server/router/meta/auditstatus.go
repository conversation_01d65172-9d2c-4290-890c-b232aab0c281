package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AuditStatusRouter struct {}

// InitAuditStatusRouter 初始化 审核状态配置 路由信息
func (s *AuditStatusRouter) InitAuditStatusRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	auditstatusRouter := Router.Group("auditstatus").Use(middleware.OperationRecord())
	auditstatusRouterWithoutRecord := Router.Group("auditstatus")
	auditstatusRouterWithoutAuth := PublicRouter.Group("auditstatus")
	{
		auditstatusRouter.POST("createAuditStatus", auditstatusApi.CreateAuditStatus)   // 新建审核状态配置
		auditstatusRouter.DELETE("deleteAuditStatus", auditstatusApi.DeleteAuditStatus) // 删除审核状态配置
		auditstatusRouter.DELETE("deleteAuditStatusByIds", auditstatusApi.DeleteAuditStatusByIds) // 批量删除审核状态配置
		auditstatusRouter.PUT("updateAuditStatus", auditstatusApi.UpdateAuditStatus)    // 更新审核状态配置
	}
	{
		auditstatusRouterWithoutRecord.GET("findAuditStatus", auditstatusApi.FindAuditStatus)        // 根据ID获取审核状态配置
		auditstatusRouterWithoutRecord.GET("getAuditStatusList", auditstatusApi.GetAuditStatusList)  // 获取审核状态配置列表
	}
	{
	    auditstatusRouterWithoutAuth.GET("getAuditStatusPublic", auditstatusApi.GetAuditStatusPublic)  // 审核状态配置开放接口
	}
}
