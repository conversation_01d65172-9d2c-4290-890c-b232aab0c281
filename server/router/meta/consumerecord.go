package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type ConsumeRecordRouter struct {}

// InitConsumeRecordRouter 初始化 消费记录 路由信息
func (s *ConsumeRecordRouter) InitConsumeRecordRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	consumerecordRouter := Router.Group("consumerecord").Use(middleware.OperationRecord())
	consumerecordRouterWithoutRecord := Router.Group("consumerecord")
	consumerecordRouterWithoutAuth := PublicRouter.Group("consumerecord")
	{
		consumerecordRouter.POST("createConsumeRecord", consumerecordApi.CreateConsumeRecord)   // 新建消费记录
		consumerecordRouter.DELETE("deleteConsumeRecord", consumerecordApi.DeleteConsumeRecord) // 删除消费记录
		consumerecordRouter.DELETE("deleteConsumeRecordByIds", consumerecordApi.DeleteConsumeRecordByIds) // 批量删除消费记录
		consumerecordRouter.PUT("updateConsumeRecord", consumerecordApi.UpdateConsumeRecord)    // 更新消费记录
	}
	{
		consumerecordRouterWithoutRecord.GET("findConsumeRecord", consumerecordApi.FindConsumeRecord)        // 根据ID获取消费记录
		consumerecordRouterWithoutRecord.GET("getConsumeRecordList", consumerecordApi.GetConsumeRecordList)  // 获取消费记录列表
	}
	{
	    consumerecordRouterWithoutAuth.GET("getConsumeRecordDataSource", consumerecordApi.GetConsumeRecordDataSource)  // 获取消费记录数据源
	    consumerecordRouterWithoutAuth.GET("getConsumeRecordPublic", consumerecordApi.GetConsumeRecordPublic)  // 消费记录开放接口
	}
}
