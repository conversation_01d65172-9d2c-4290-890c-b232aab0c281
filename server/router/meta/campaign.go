package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type CampaignRouter struct{}

// InitCampaignRouter 初始化 广告投放 路由信息
func (s *CampaignRouter) InitCampaignRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	campaignRouter := Router.Group("campaign").Use(middleware.OperationRecord())
	campaignRouterWithoutRecord := Router.Group("campaign")
	campaignRouterWithoutAuth := PublicRouter.Group("campaign")
	{
		campaignRouter.POST("createCampaign", campaignApi.CreateCampaign)             // 新建广告投放
		campaignRouter.DELETE("deleteCampaign", campaignApi.DeleteCampaign)           // 删除广告投放
		campaignRouter.DELETE("deleteCampaignByIds", campaignApi.DeleteCampaignByIds) // 批量删除广告投放
		campaignRouter.PUT("updateCampaign", campaignApi.UpdateCampaign)              // 更新广告投放
		campaignRouter.POST("pauseCampaign", campaignApi.PauseCampaign)               // 暂停广告投放
	}
	{
		campaignRouterWithoutRecord.GET("findCampaign", campaignApi.FindCampaign)       // 根据ID获取广告投放
		campaignRouterWithoutRecord.GET("getCampaignList", campaignApi.GetCampaignList) // 获取广告投放列表
	}
	{
		campaignRouterWithoutAuth.GET("getCampaignDataSource", campaignApi.GetCampaignDataSource) // 获取广告投放数据源
		campaignRouterWithoutAuth.GET("getCampaignPublic", campaignApi.GetCampaignPublic)         // 广告投放开放接口
	}
}
