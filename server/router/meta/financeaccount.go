package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type FinanceAccountRouter struct {}

// InitFinanceAccountRouter 初始化 财务账户 路由信息
func (s *FinanceAccountRouter) InitFinanceAccountRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	financeaccountRouter := Router.Group("financeaccount").Use(middleware.OperationRecord())
	financeaccountRouterWithoutRecord := Router.Group("financeaccount")
	financeaccountRouterWithoutAuth := PublicRouter.Group("financeaccount")
	{
		financeaccountRouter.POST("createFinanceAccount", financeaccountApi.CreateFinanceAccount)   // 新建财务账户
		financeaccountRouter.DELETE("deleteFinanceAccount", financeaccountApi.DeleteFinanceAccount) // 删除财务账户
		financeaccountRouter.DELETE("deleteFinanceAccountByIds", financeaccountApi.DeleteFinanceAccountByIds) // 批量删除财务账户
		financeaccountRouter.PUT("updateFinanceAccount", financeaccountApi.UpdateFinanceAccount)    // 更新财务账户
	}
	{
		financeaccountRouterWithoutRecord.GET("findFinanceAccount", financeaccountApi.FindFinanceAccount)        // 根据ID获取财务账户
		financeaccountRouterWithoutRecord.GET("getFinanceAccountList", financeaccountApi.GetFinanceAccountList)  // 获取财务账户列表
	}
	{
	    financeaccountRouterWithoutAuth.GET("getFinanceAccountDataSource", financeaccountApi.GetFinanceAccountDataSource)  // 获取财务账户数据源
	    financeaccountRouterWithoutAuth.GET("getFinanceAccountPublic", financeaccountApi.GetFinanceAccountPublic)  // 财务账户开放接口
	}
}
