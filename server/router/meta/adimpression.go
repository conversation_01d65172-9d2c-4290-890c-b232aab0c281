package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AdImpressionRouter struct {}

// InitAdImpressionRouter 初始化 广告展示记录 路由信息
func (s *AdImpressionRouter) InitAdImpressionRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	adimpressionRouter := Router.Group("adimpression").Use(middleware.OperationRecord())
	adimpressionRouterWithoutRecord := Router.Group("adimpression")
	adimpressionRouterWithoutAuth := PublicRouter.Group("adimpression")
	{
		adimpressionRouter.POST("createAdImpression", adimpressionApi.CreateAdImpression)   // 新建广告展示记录
		adimpressionRouter.DELETE("deleteAdImpression", adimpressionApi.DeleteAdImpression) // 删除广告展示记录
		adimpressionRouter.DELETE("deleteAdImpressionByIds", adimpressionApi.DeleteAdImpressionByIds) // 批量删除广告展示记录
		adimpressionRouter.PUT("updateAdImpression", adimpressionApi.UpdateAdImpression)    // 更新广告展示记录
	}
	{
		adimpressionRouterWithoutRecord.GET("findAdImpression", adimpressionApi.FindAdImpression)        // 根据ID获取广告展示记录
		adimpressionRouterWithoutRecord.GET("getAdImpressionList", adimpressionApi.GetAdImpressionList)  // 获取广告展示记录列表
	}
	{
	    adimpressionRouterWithoutAuth.GET("getAdImpressionDataSource", adimpressionApi.GetAdImpressionDataSource)  // 获取广告展示记录数据源
	    adimpressionRouterWithoutAuth.GET("getAdImpressionPublic", adimpressionApi.GetAdImpressionPublic)  // 广告展示记录开放接口
	}
}
