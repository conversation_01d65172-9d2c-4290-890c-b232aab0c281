package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AdPositionRouter struct {}

// InitAdPositionRouter 初始化 广告位置管理 路由信息
func (s *AdPositionRouter) InitAdPositionRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	adpositionRouter := Router.Group("adposition").Use(middleware.OperationRecord())
	adpositionRouterWithoutRecord := Router.Group("adposition")
	adpositionRouterWithoutAuth := PublicRouter.Group("adposition")
	{
		adpositionRouter.POST("createAdPosition", adpositionApi.CreateAdPosition)   // 新建广告位置管理
		adpositionRouter.DELETE("deleteAdPosition", adpositionApi.DeleteAdPosition) // 删除广告位置管理
		adpositionRouter.DELETE("deleteAdPositionByIds", adpositionApi.DeleteAdPositionByIds) // 批量删除广告位置管理
		adpositionRouter.PUT("updateAdPosition", adpositionApi.UpdateAdPosition)    // 更新广告位置管理
	}
	{
		adpositionRouterWithoutRecord.GET("findAdPosition", adpositionApi.FindAdPosition)        // 根据ID获取广告位置管理
		adpositionRouterWithoutRecord.GET("getAdPositionList", adpositionApi.GetAdPositionList)  // 获取广告位置管理列表
	}
	{
	    adpositionRouterWithoutAuth.GET("getAdPositionPublic", adpositionApi.GetAdPositionPublic)  // 广告位置管理开放接口
	}
}
