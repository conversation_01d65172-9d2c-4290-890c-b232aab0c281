package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AdRouter struct{}

// InitAdRouter 初始化 广告管理 路由信息
func (s *AdRouter) InitAdRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	adRouter := Router.Group("ad").Use(middleware.OperationRecord())
	adRouterWithoutRecord := Router.Group("ad")
	adRouterWithoutAuth := PublicRouter.Group("ad")
	{
		adRouter.POST("createAd", adApi.CreateAd)                         // 新建广告管理
		adRouter.POST("createAdWithCampaign", adApi.CreateAdWithCampaign) // 广告向导 - 自动创建广告和投放计划
		adRouter.DELETE("deleteAd", adApi.DeleteAd)                       // 删除广告管理
		adRouter.DELETE("deleteAdByIds", adApi.DeleteAdByIds)             // 批量删除广告管理
		adRouter.PUT("updateAd", adApi.UpdateAd)                          // 更新广告管理
	}
	{
		adRouterWithoutRecord.GET("findAd", adApi.FindAd)       // 根据ID获取广告管理
		adRouterWithoutRecord.GET("getAdList", adApi.GetAdList) // 获取广告管理列表
	}
	{
		adRouterWithoutAuth.GET("getAdDataSource", adApi.GetAdDataSource) // 获取广告管理数据源
		adRouterWithoutAuth.GET("getAdPublic", adApi.GetAdPublic)         // 广告管理开放接口
	}
}
