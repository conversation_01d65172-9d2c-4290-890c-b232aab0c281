package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AdPriceRouter struct {}

// InitAdPriceRouter 初始化 广告价格管理 路由信息
func (s *AdPriceRouter) InitAdPriceRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	adpriceRouter := Router.Group("adprice").Use(middleware.OperationRecord())
	adpriceRouterWithoutRecord := Router.Group("adprice")
	adpriceRouterWithoutAuth := PublicRouter.Group("adprice")
	{
		adpriceRouter.POST("createAdPrice", adpriceApi.CreateAdPrice)   // 新建广告价格管理
		adpriceRouter.DELETE("deleteAdPrice", adpriceApi.DeleteAdPrice) // 删除广告价格管理
		adpriceRouter.DELETE("deleteAdPriceByIds", adpriceApi.DeleteAdPriceByIds) // 批量删除广告价格管理
		adpriceRouter.PUT("updateAdPrice", adpriceApi.UpdateAdPrice)    // 更新广告价格管理
	}
	{
		adpriceRouterWithoutRecord.GET("findAdPrice", adpriceApi.FindAdPrice)        // 根据ID获取广告价格管理
		adpriceRouterWithoutRecord.GET("getAdPriceList", adpriceApi.GetAdPriceList)  // 获取广告价格管理列表
	}
	{
	    adpriceRouterWithoutAuth.GET("getAdPriceDataSource", adpriceApi.GetAdPriceDataSource)  // 获取广告价格管理数据源
	    adpriceRouterWithoutAuth.GET("getAdPricePublic", adpriceApi.GetAdPricePublic)  // 广告价格管理开放接口
	}
}
