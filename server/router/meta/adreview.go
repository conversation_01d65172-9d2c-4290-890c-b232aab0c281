package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AdReviewRouter struct {}

// InitAdReviewRouter 初始化 广告审核 路由信息
func (s *AdReviewRouter) InitAdReviewRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	adreviewRouter := Router.Group("adreview").Use(middleware.OperationRecord())
	adreviewRouterWithoutRecord := Router.Group("adreview")
	adreviewRouterWithoutAuth := PublicRouter.Group("adreview")
	{
		adreviewRouter.POST("createAdReview", adreviewApi.CreateAdReview)   // 新建广告审核
		adreviewRouter.DELETE("deleteAdReview", adreviewApi.DeleteAdReview) // 删除广告审核
		adreviewRouter.DELETE("deleteAdReviewByIds", adreviewApi.DeleteAdReviewByIds) // 批量删除广告审核
		adreviewRouter.PUT("updateAdReview", adreviewApi.UpdateAdReview)    // 更新广告审核
	}
	{
		adreviewRouterWithoutRecord.GET("findAdReview", adreviewApi.FindAdReview)        // 根据ID获取广告审核
		adreviewRouterWithoutRecord.GET("getAdReviewList", adreviewApi.GetAdReviewList)  // 获取广告审核列表
	}
	{
	    adreviewRouterWithoutAuth.GET("getAdReviewDataSource", adreviewApi.GetAdReviewDataSource)  // 获取广告审核数据源
	    adreviewRouterWithoutAuth.GET("getAdReviewPublic", adreviewApi.GetAdReviewPublic)  // 广告审核开放接口
	}
}
