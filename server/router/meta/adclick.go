package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AdClickRouter struct {}

// InitAdClickRouter 初始化 广告点击记录 路由信息
func (s *AdClickRouter) InitAdClickRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	adclickRouter := Router.Group("adclick").Use(middleware.OperationRecord())
	adclickRouterWithoutRecord := Router.Group("adclick")
	adclickRouterWithoutAuth := PublicRouter.Group("adclick")
	{
		adclickRouter.POST("createAdClick", adclickApi.CreateAdClick)   // 新建广告点击记录
		adclickRouter.DELETE("deleteAdClick", adclickApi.DeleteAdClick) // 删除广告点击记录
		adclickRouter.DELETE("deleteAdClickByIds", adclickApi.DeleteAdClickByIds) // 批量删除广告点击记录
		adclickRouter.PUT("updateAdClick", adclickApi.UpdateAdClick)    // 更新广告点击记录
	}
	{
		adclickRouterWithoutRecord.GET("findAdClick", adclickApi.FindAdClick)        // 根据ID获取广告点击记录
		adclickRouterWithoutRecord.GET("getAdClickList", adclickApi.GetAdClickList)  // 获取广告点击记录列表
	}
	{
	    adclickRouterWithoutAuth.GET("getAdClickDataSource", adclickApi.GetAdClickDataSource)  // 获取广告点击记录数据源
	    adclickRouterWithoutAuth.GET("getAdClickPublic", adclickApi.GetAdClickPublic)  // 广告点击记录开放接口
	}
}
