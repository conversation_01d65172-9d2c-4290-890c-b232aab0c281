package meta

import (
	meta "github.com/flipped-aurora/gin-vue-admin/server/api/v1/handler"
	"github.com/gin-gonic/gin"
)

type SdkRouter struct{}

// InitSdkRouter 初始化 SDK 路由信息
func (s *SdkRouter) InitSdkRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	// SDK接口使用公开路由，无需授权
	handler := meta.NewMetaHandler()
	mediafileRouterWithoutAuth := PublicRouter.Group("v1")
	mediafileRouterWithoutAuth.POST("request", handler.MetaHandler)
}
