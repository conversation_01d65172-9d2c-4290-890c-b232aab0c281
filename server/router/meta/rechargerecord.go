package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type RechargeRecordRouter struct {}

// InitRechargeRecordRouter 初始化 充值记录 路由信息
func (s *RechargeRecordRouter) InitRechargeRecordRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	rechargerecordRouter := Router.Group("rechargerecord").Use(middleware.OperationRecord())
	rechargerecordRouterWithoutRecord := Router.Group("rechargerecord")
	rechargerecordRouterWithoutAuth := PublicRouter.Group("rechargerecord")
	{
		rechargerecordRouter.POST("createRechargeRecord", rechargerecordApi.CreateRechargeRecord)   // 新建充值记录
		rechargerecordRouter.DELETE("deleteRechargeRecord", rechargerecordApi.DeleteRechargeRecord) // 删除充值记录
		rechargerecordRouter.DELETE("deleteRechargeRecordByIds", rechargerecordApi.DeleteRechargeRecordByIds) // 批量删除充值记录
		rechargerecordRouter.PUT("updateRechargeRecord", rechargerecordApi.UpdateRechargeRecord)    // 更新充值记录
	}
	{
		rechargerecordRouterWithoutRecord.GET("findRechargeRecord", rechargerecordApi.FindRechargeRecord)        // 根据ID获取充值记录
		rechargerecordRouterWithoutRecord.GET("getRechargeRecordList", rechargerecordApi.GetRechargeRecordList)  // 获取充值记录列表
	}
	{
	    rechargerecordRouterWithoutAuth.GET("getRechargeRecordDataSource", rechargerecordApi.GetRechargeRecordDataSource)  // 获取充值记录数据源
	    rechargerecordRouterWithoutAuth.GET("getRechargeRecordPublic", rechargerecordApi.GetRechargeRecordPublic)  // 充值记录开放接口
	}
}
