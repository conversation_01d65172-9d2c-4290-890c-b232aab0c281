package meta

import (
	"github.com/gin-gonic/gin"
)

type DashubDashboardRouter struct{}

// InitDashubDashboardRouter 初始化 Dashub Dashboard 路由信息
func (s *DashubDashboardRouter) InitDashubDashboardRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	dashubDashboardRouterWithoutRecord := Router.Group("dashub-dashboard")
	dashubDashboardRouterWithoutAuth := PublicRouter.Group("dashub-dashboard")

	{
		// 不需要记录操作的路由
		dashubDashboardRouterWithoutRecord.GET("data", dashubDashboardApi.GetDashubDashboardData)                    // 获取Dashboard数据
		dashubDashboardRouterWithoutRecord.GET("multi-period-chart", dashubDashboardApi.GetMultiPeriodChart)       // 获取多周期图表数据
		dashubDashboardRouterWithoutRecord.GET("chart-data", dashubDashboardApi.GetChartData)                      // 获取单个图表数据
		dashubDashboardRouterWithoutRecord.GET("item-configs", dashubDashboardApi.GetDashubItemConfigs)            // 获取统计项目配置
		dashubDashboardRouterWithoutRecord.GET("role-permissions", dashubDashboardApi.GetRolePermissions)          // 获取角色权限
		dashubDashboardRouterWithoutRecord.GET("stats", dashubDashboardApi.GetDashubStats)                         // 获取统计信息
	}
	{
		// 不需要鉴权的路由
		// 暂时没有公开接口
		_ = dashubDashboardRouterWithoutAuth
	}
}
