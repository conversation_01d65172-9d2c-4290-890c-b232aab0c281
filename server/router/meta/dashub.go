package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type DashubRouter struct {}

// InitDashubRouter 初始化 统计汇总 路由信息
func (s *DashubRouter) InitDashubRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	dashubRouter := Router.Group("dashub").Use(middleware.OperationRecord())
	dashubRouterWithoutRecord := Router.Group("dashub")
	dashubRouterWithoutAuth := PublicRouter.Group("dashub")
	{
		dashubRouter.POST("createDashub", dashubApi.CreateDashub)   // 新建统计汇总
		dashubRouter.DELETE("deleteDashub", dashubApi.DeleteDashub) // 删除统计汇总
		dashubRouter.DELETE("deleteDashubByIds", dashubApi.DeleteDashubByIds) // 批量删除统计汇总
		dashubRouter.PUT("updateDashub", dashubApi.UpdateDashub)    // 更新统计汇总
	}
	{
		dashubRouterWithoutRecord.GET("findDashub", dashubApi.FindDashub)        // 根据ID获取统计汇总
		dashubRouterWithoutRecord.GET("getDashubList", dashubApi.GetDashubList)  // 获取统计汇总列表
	}
	{
	    dashubRouterWithoutAuth.GET("getDashubPublic", dashubApi.GetDashubPublic)  // 统计汇总开放接口
	}
}
