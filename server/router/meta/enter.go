package meta

import api "github.com/flipped-aurora/gin-vue-admin/server/api/v1"

type RouterGroup struct {
	ConsumeRecordRouter
	AdReviewRouter
	DeviceDataRouter
	CampaignRouter
	FinanceAccountRouter
	AdStatisticsRouter
	AdActionRouter
	AdPositionRouter
	AdClickRouter
	AdImpressionRouter
	RechargeRecordRouter
	AdRouter
	MediaFileRouter
	AdPriceRouter
	AppRouter
	AdTypeRouter
	AuditStatusRouter
	DashboardRouter
	DashubRouter
	DashubDashboardRouter
	SdkRouter
}

var (
	consumerecordApi   = api.ApiGroupApp.MetaApiGroup.ConsumeRecordApi
	adreviewApi        = api.ApiGroupApp.MetaApiGroup.AdReviewApi
	devicedataApi      = api.ApiGroupApp.MetaApiGroup.DeviceDataApi
	campaignApi        = api.ApiGroupApp.MetaApiGroup.CampaignApi
	financeaccountApi  = api.ApiGroupApp.MetaApiGroup.FinanceAccountApi
	adstatisticsApi    = api.ApiGroupApp.MetaApiGroup.AdStatisticsApi
	adactionApi        = api.ApiGroupApp.MetaApiGroup.AdActionApi
	adpositionApi      = api.ApiGroupApp.MetaApiGroup.AdPositionApi
	adclickApi         = api.ApiGroupApp.MetaApiGroup.AdClickApi
	adimpressionApi    = api.ApiGroupApp.MetaApiGroup.AdImpressionApi
	rechargerecordApi  = api.ApiGroupApp.MetaApiGroup.RechargeRecordApi
	adApi              = api.ApiGroupApp.MetaApiGroup.AdApi
	mediafileApi       = api.ApiGroupApp.MetaApiGroup.MediaFileApi
	adpriceApi         = api.ApiGroupApp.MetaApiGroup.AdPriceApi
	appApi             = api.ApiGroupApp.MetaApiGroup.AppApi
	adtypeApi          = api.ApiGroupApp.MetaApiGroup.AdTypeApi
	auditstatusApi     = api.ApiGroupApp.MetaApiGroup.AuditStatusApi
	dashboardApi       = api.ApiGroupApp.MetaApiGroup.DashboardApi
	dashubApi          = api.ApiGroupApp.MetaApiGroup.DashubApi
	dashubDashboardApi = api.ApiGroupApp.MetaApiGroup.DashubDashboardApi
	sdkApi             = api.ApiGroupApp.MetaApiGroup.SdkApi
)
