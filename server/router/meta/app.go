package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AppRouter struct {}

// InitAppRouter 初始化 应用管理 路由信息
func (s *AppRouter) InitAppRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	appRouter := Router.Group("app").Use(middleware.OperationRecord())
	appRouterWithoutRecord := Router.Group("app")
	appRouterWithoutAuth := PublicRouter.Group("app")
	{
		appRouter.POST("createApp", appApi.CreateApp)   // 新建应用管理
		appRouter.DELETE("deleteApp", appApi.DeleteApp) // 删除应用管理
		appRouter.DELETE("deleteAppByIds", appApi.DeleteAppByIds) // 批量删除应用管理
		appRouter.PUT("updateApp", appApi.UpdateApp)    // 更新应用管理
	}
	{
		appRouterWithoutRecord.GET("findApp", appApi.FindApp)        // 根据ID获取应用管理
		appRouterWithoutRecord.GET("getAppList", appApi.GetAppList)  // 获取应用管理列表
	}
	{
	    appRouterWithoutAuth.GET("getAppDataSource", appApi.GetAppDataSource)  // 获取应用管理数据源
	    appRouterWithoutAuth.GET("getAppPublic", appApi.GetAppPublic)  // 应用管理开放接口
	}
}
