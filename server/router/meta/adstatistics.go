package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AdStatisticsRouter struct {}

// InitAdStatisticsRouter 初始化 广告统计 路由信息
func (s *AdStatisticsRouter) InitAdStatisticsRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	adstatisticsRouter := Router.Group("adstatistics").Use(middleware.OperationRecord())
	adstatisticsRouterWithoutRecord := Router.Group("adstatistics")
	adstatisticsRouterWithoutAuth := PublicRouter.Group("adstatistics")
	{
		adstatisticsRouter.POST("createAdStatistics", adstatisticsApi.CreateAdStatistics)   // 新建广告统计
		adstatisticsRouter.DELETE("deleteAdStatistics", adstatisticsApi.DeleteAdStatistics) // 删除广告统计
		adstatisticsRouter.DELETE("deleteAdStatisticsByIds", adstatisticsApi.DeleteAdStatisticsByIds) // 批量删除广告统计
		adstatisticsRouter.PUT("updateAdStatistics", adstatisticsApi.UpdateAdStatistics)    // 更新广告统计
	}
	{
		adstatisticsRouterWithoutRecord.GET("findAdStatistics", adstatisticsApi.FindAdStatistics)        // 根据ID获取广告统计
		adstatisticsRouterWithoutRecord.GET("getAdStatisticsList", adstatisticsApi.GetAdStatisticsList)  // 获取广告统计列表
	}
	{
	    adstatisticsRouterWithoutAuth.GET("getAdStatisticsDataSource", adstatisticsApi.GetAdStatisticsDataSource)  // 获取广告统计数据源
	    adstatisticsRouterWithoutAuth.GET("getAdStatisticsPublic", adstatisticsApi.GetAdStatisticsPublic)  // 广告统计开放接口
	}
}
