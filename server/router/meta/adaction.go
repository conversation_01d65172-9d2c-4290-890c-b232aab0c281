package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type AdActionRouter struct {}

// InitAdActionRouter 初始化 广告行为管理 路由信息
func (s *AdActionRouter) InitAdActionRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	adactionRouter := Router.Group("adaction").Use(middleware.OperationRecord())
	adactionRouterWithoutRecord := Router.Group("adaction")
	adactionRouterWithoutAuth := PublicRouter.Group("adaction")
	{
		adactionRouter.POST("createAdAction", adactionApi.CreateAdAction)   // 新建广告行为管理
		adactionRouter.DELETE("deleteAdAction", adactionApi.DeleteAdAction) // 删除广告行为管理
		adactionRouter.DELETE("deleteAdActionByIds", adactionApi.DeleteAdActionByIds) // 批量删除广告行为管理
		adactionRouter.PUT("updateAdAction", adactionApi.UpdateAdAction)    // 更新广告行为管理
	}
	{
		adactionRouterWithoutRecord.GET("findAdAction", adactionApi.FindAdAction)        // 根据ID获取广告行为管理
		adactionRouterWithoutRecord.GET("getAdActionList", adactionApi.GetAdActionList)  // 获取广告行为管理列表
	}
	{
	    adactionRouterWithoutAuth.GET("getAdActionPublic", adactionApi.GetAdActionPublic)  // 广告行为管理开放接口
	}
}
