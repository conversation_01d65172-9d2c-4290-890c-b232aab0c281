package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type MediaFileRouter struct {}

// InitMediaFileRouter 初始化 媒体文件 路由信息
func (s *MediaFileRouter) InitMediaFileRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	mediafileRouter := Router.Group("mediafile").Use(middleware.OperationRecord())
	mediafileRouterWithoutRecord := Router.Group("mediafile")
	mediafileRouterWithoutAuth := PublicRouter.Group("mediafile")
	{
		mediafileRouter.POST("createMediaFile", mediafileApi.CreateMediaFile)   // 新建媒体文件
		mediafileRouter.DELETE("deleteMediaFile", mediafileApi.DeleteMediaFile) // 删除媒体文件
		mediafileRouter.DELETE("deleteMediaFileByIds", mediafileApi.DeleteMediaFileByIds) // 批量删除媒体文件
		mediafileRouter.PUT("updateMediaFile", mediafileApi.UpdateMediaFile)    // 更新媒体文件
	}
	{
		mediafileRouterWithoutRecord.GET("findMediaFile", mediafileApi.FindMediaFile)        // 根据ID获取媒体文件
		mediafileRouterWithoutRecord.GET("getMediaFileList", mediafileApi.GetMediaFileList)  // 获取媒体文件列表
	}
	{
	    mediafileRouterWithoutAuth.GET("getMediaFileDataSource", mediafileApi.GetMediaFileDataSource)  // 获取媒体文件数据源
	    mediafileRouterWithoutAuth.GET("getMediaFilePublic", mediafileApi.GetMediaFilePublic)  // 媒体文件开放接口
	}
}
