package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type DeviceDataRouter struct {}

// InitDeviceDataRouter 初始化 设备数据 路由信息
func (s *DeviceDataRouter) InitDeviceDataRouter(Router *gin.RouterGroup,PublicRouter *gin.RouterGroup) {
	devicedataRouter := Router.Group("devicedata").Use(middleware.OperationRecord())
	devicedataRouterWithoutRecord := Router.Group("devicedata")
	devicedataRouterWithoutAuth := PublicRouter.Group("devicedata")
	{
		devicedataRouter.POST("createDeviceData", devicedataApi.CreateDeviceData)   // 新建设备数据
		devicedataRouter.DELETE("deleteDeviceData", devicedataApi.DeleteDeviceData) // 删除设备数据
		devicedataRouter.DELETE("deleteDeviceDataByIds", devicedataApi.DeleteDeviceDataByIds) // 批量删除设备数据
		devicedataRouter.PUT("updateDeviceData", devicedataApi.UpdateDeviceData)    // 更新设备数据
	}
	{
		devicedataRouterWithoutRecord.GET("findDeviceData", devicedataApi.FindDeviceData)        // 根据ID获取设备数据
		devicedataRouterWithoutRecord.GET("getDeviceDataList", devicedataApi.GetDeviceDataList)  // 获取设备数据列表
	}
	{
	    devicedataRouterWithoutAuth.GET("getDeviceDataDataSource", devicedataApi.GetDeviceDataDataSource)  // 获取设备数据数据源
	    devicedataRouterWithoutAuth.GET("getDeviceDataPublic", devicedataApi.GetDeviceDataPublic)  // 设备数据开放接口
	}
}
