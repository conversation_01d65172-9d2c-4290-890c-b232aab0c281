package handler

import (
	"encoding/json"
	"github.com/gin-gonic/gin"
	"strings"
)

type Success[T any] struct {
	Code int `json:"code"` // 业务码
	Data T   `json:"data"` // 泛型数据
}

type ResultSuccess struct {
	Code int         `json:"code"` // 业务码
	Data interface{} `json:"data"` // 泛型数据
}

type RootRequest struct {
	Uri         string            `json:"uri,omitempty"`             //请求的URI非必填
	Code        int               `json:"code,omitempty"`            //请求代码，跟URI二选一
	Session     string            `json:"session,omitempty"`         //session id
	Sign        string            `json:"sign,omitempty"`            //请求签名
	MsgId       int32             `json:"msgid,omitempty"`           //消息id（流水号）
	Pid         string            `json:"pid,omitempty"`             //产品id字串
	Uid         int32             `json:"uid,omitempty"`             //用户id
	Did         string            `json:"did,omitempty"`             //设备号
	Version     int               `json:"version,omitempty"`         //版本号
	PackageName string            `json:"packageName,omitempty"`     //包名
	FakeString  string            `json:"fakeString,omitempty"`      //是否破解版
	Channel     string            `json:"channel,omitempty"`         //渠道名
	Data        json.RawMessage   `json:"data" swaggertype:"string"` //请求参数，不同类型参数不同
	Device      map[string]string `json:"device,omitempty"`          //key-value格式的设备信息
}

type Handler struct {
	uid     int32
	pid     string
	did     string
	ctx     *gin.Context
	request *RootRequest
}

func Reqvar[T any](t *Handler, obj T) *T {
	if err := json.Unmarshal(t.request.Data, &obj); err != nil {
		t.Error(err)
	}
	return &obj
}

type QVEvent struct {
	QPage
	EventId int32 `json:"eventId"` //事件ID
	Time    int64 `json:"ts"`      //Unix时间
}

type QFav struct {
	Source   int32  `json:"source"`   // 来源ID
	ObjectId string `json:"objectId"` //收藏数据ID
}

type QPage struct {
	Page  int `json:"page"`  // 页码
	Limit int `json:"limit"` // 每页数量
}

type AdQuery struct {
	Package string     `json:"pid"`     // APP包名
	Channel string     `json:"cid"`     // 渠道id
	Version int        `json:"vcode"`   // 版本号
	Device  string     `json:"did"`     // 设备编号
	Report  []AdReport `json:"reports"` // 广告情况上报[上报接口用]
}

type AdReport struct {
	Adid      string `json:"adid"` // 广告编号
	Kind      int    `json:"type"` // 类型:0展示，1点击，2..
	Timestamp int    `json:"ts"`   // 记录时间
}

type QueryId struct {
	Id int32 `json:"id"`
}

// RawQuery 数据库查询
type RawQuery struct {
	DB    string                 `json:"db"`    // 库和表格式db.table
	ID    string                 `json:"id"`    // 查询的ID
	Limit int                    `json:"limit"` // 查询大小
	Page  int                    `json:"page"`  // 分页数
	Query map[string]any         `json:"query"` // 查询列表
	Raw   map[string]interface{} `json:"raw"`   // 原始查询
	db    string
	table string
}

// AuthQuery 鉴权参数
type AuthQuery struct {
	DB    string                 `json:"db"`    // 库和表格式db.table
	ID    string                 `json:"id"`    // 查询的ID
	Limit int                    `json:"limit"` // 查询大小
	Page  int                    `json:"page"`  // 分页数
	Data  map[string]interface{} `json:"data"`  // 参数
	db    string
	table string
}

func (this *RawQuery) Init() {
	t := strings.Split(this.DB, ".")
	this.db = t[0]
	this.table = t[1]
}

func (this *QPage) Offset() int {
	return (this.Page - 1) * this.Limit
}
