package handler

import (
	"encoding/binary"
	"encoding/json"
	"fmt"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/gin-gonic/gin"
	"net/http"
	"runtime"

	"gomisc/common/decoder"
)

var (
	dcoder     = decoder.HDecoder{}
	sdkService = service.ServiceGroupApp.MetaServiceGroup.SdkService
)

func NewMetaHandler() *Handler {
	return &Handler{}
}

// MetaHandler 统一请求接口
// @Summary 统一请求接口
// @Description 所有请求通过这一接口发出，分别指定code/uri来区别不同接口
// @Tags root
// @Accept json
// @Produce json
// @Param query body RootRequest true "查询参数"
// @Success 200 {object} Success[any]
// @Failure 400
// @Router /v1/request [post]
func (this *Handler) MetaHandler(ctx *gin.Context) {
	// 获取请求体数据
	var data, _ = ctx.GetRawData()
	if 3 > len(data) {
		this.SendNotFound()
		return
	}
	this.ctx = ctx
	var w = new(RootRequest)
	//var handler = this.clone(ctx)
	var apiCode = binary.BigEndian.Uint32(data)

	data = dcoder.Decrypt(data[4:])

	fmt.Println("========>", apiCode, string(data))

	_ = json.Unmarshal(data, w)

	//handler.pid = w.Pid
	//handler.uid = w.Uid
	//handler.did = w.Did
	//handler.request = w

	switch apiCode {
	case AD_LIST:
		var req metaReq.GetAdsRequest
		err := json.Unmarshal(w.Data, &req)
		if err != nil {
			this.Error(err)
			return
		}
		req.PackageId = w.Pid
		ads, err := sdkService.GetAds(ctx.Request.Context(), &req)
		if err != nil {
			this.Error(err)
			return
		}
		this.Ok(ads)
		break
	case AD_REPORT:
		var req metaReq.AdEventRequest
		err := json.Unmarshal(w.Data, &req)
		if err != nil {
			this.Error(err)
			return
		}
		req.PackageId = w.Pid
		result, err := sdkService.ReportEvents(ctx.Request.Context(), &req)
		if err != nil {
			this.Error(err)
			return
		}
		this.Ok(result)
		break

	default:
		err := fmt.Errorf("unknown code: %d", apiCode)
		this.Error(err)
	}
}
func (this *Handler) clone(ctx *gin.Context) *Handler {
	return &Handler{
		ctx: ctx,
	}
}

func printStackTrace(err any) {
	fmt.Println("=======printStackTrace=======>", err)
	buf := make([]byte, 1024)
	n := runtime.Stack(buf, false)
	fmt.Printf("Stack trace:\n%s", buf[:n])
}

func (this *Handler) Error(err interface{}, codes ...int) {
	var eerr error
	var c = http.StatusBadRequest
	if len(codes) > 0 {
		c = codes[0]
	}
	if t, ok := err.(error); ok {
		eerr = t

	} else if t1, ok1 := err.(string); ok1 {
		eerr = fmt.Errorf(t1)
	}

	printStackTrace(err)

	this.ctx.AbortWithError(c, eerr)
}
func (this *Handler) SendNotFound() {
	err := fmt.Errorf("not found")
	this.ctx.AbortWithError(http.StatusNotFound, err)
}
func (this *Handler) Ok(obj interface{}) {
	result := ResultSuccess{Code: 0, Data: obj}
	data, _ := json.Marshal(result)
	ss := dcoder.Encrypt(data)
	this.ctx.Data(200, "application/octet-stream", ss)
}
func (this *Handler) OkCache(key string, data interface{}) bool {
	//TODO 缓存数据
	if nil == data {
		//读取模式
	} else {
		//写入模式
		this.Ok(data)
		return true
	}
	return false
}
