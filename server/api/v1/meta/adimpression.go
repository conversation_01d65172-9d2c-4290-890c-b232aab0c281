package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AdImpressionApi struct{}

// CreateAdImpression 创建广告展示记录
// @Tags AdImpression
// @Summary 创建广告展示记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdImpression true "创建广告展示记录"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /adimpression/createAdImpression [post]
func (adimpressionApi *AdImpressionApi) CreateAdImpression(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var adimpression meta.AdImpression
	err := c.ShouldBindJSON(&adimpression)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adimpressionService.CreateAdImpression(ctx, &adimpression)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteAdImpression 删除广告展示记录
// @Tags AdImpression
// @Summary 删除广告展示记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdImpression true "删除广告展示记录"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /adimpression/deleteAdImpression [delete]
func (adimpressionApi *AdImpressionApi) DeleteAdImpression(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := adimpressionService.DeleteAdImpression(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAdImpressionByIds 批量删除广告展示记录
// @Tags AdImpression
// @Summary 批量删除广告展示记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /adimpression/deleteAdImpressionByIds [delete]
func (adimpressionApi *AdImpressionApi) DeleteAdImpressionByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := adimpressionService.DeleteAdImpressionByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateAdImpression 更新广告展示记录
// @Tags AdImpression
// @Summary 更新广告展示记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdImpression true "更新广告展示记录"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /adimpression/updateAdImpression [put]
func (adimpressionApi *AdImpressionApi) UpdateAdImpression(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var adimpression meta.AdImpression
	err := c.ShouldBindJSON(&adimpression)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adimpressionService.UpdateAdImpression(ctx, adimpression)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindAdImpression 用id查询广告展示记录
// @Tags AdImpression
// @Summary 用id查询广告展示记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询广告展示记录"
// @Success 200 {object} response.Response{data=meta.AdImpression,msg=string} "查询成功"
// @Router /adimpression/findAdImpression [get]
func (adimpressionApi *AdImpressionApi) FindAdImpression(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	readimpression, err := adimpressionService.GetAdImpression(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(readimpression, c)
}

// GetAdImpressionList 分页获取广告展示记录列表
// @Tags AdImpression
// @Summary 分页获取广告展示记录列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdImpressionSearch true "分页获取广告展示记录列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /adimpression/getAdImpressionList [get]
func (adimpressionApi *AdImpressionApi) GetAdImpressionList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo metaReq.AdImpressionSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if 1 > len(pageInfo.Sort) {
		pageInfo.Sort = "ID"
		pageInfo.Order = "descending"
	}
	list, total, err := adimpressionService.GetAdImpressionInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetAdImpressionDataSource 获取AdImpression的数据源
// @Tags AdImpression
// @Summary 获取AdImpression的数据源
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /adimpression/getAdImpressionDataSource [get]
func (adimpressionApi *AdImpressionApi) GetAdImpressionDataSource(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口为获取数据源定义的数据
	dataSource, err := adimpressionService.GetAdImpressionDataSource(ctx)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(dataSource, c)
}

// GetAdImpressionPublic 不需要鉴权的广告展示记录接口
// @Tags AdImpression
// @Summary 不需要鉴权的广告展示记录接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adimpression/getAdImpressionPublic [get]
func (adimpressionApi *AdImpressionApi) GetAdImpressionPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	adimpressionService.GetAdImpressionPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的广告展示记录接口信息",
	}, "获取成功", c)
}
