package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AdClickApi struct{}

// CreateAdClick 创建广告点击记录
// @Tags AdClick
// @Summary 创建广告点击记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdClick true "创建广告点击记录"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /adclick/createAdClick [post]
func (adclickApi *AdClickApi) CreateAdClick(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var adclick meta.AdClick
	err := c.ShouldBindJSON(&adclick)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adclickService.CreateAdClick(ctx, &adclick)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteAdClick 删除广告点击记录
// @Tags AdClick
// @Summary 删除广告点击记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdClick true "删除广告点击记录"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /adclick/deleteAdClick [delete]
func (adclickApi *AdClickApi) DeleteAdClick(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := adclickService.DeleteAdClick(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAdClickByIds 批量删除广告点击记录
// @Tags AdClick
// @Summary 批量删除广告点击记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /adclick/deleteAdClickByIds [delete]
func (adclickApi *AdClickApi) DeleteAdClickByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := adclickService.DeleteAdClickByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateAdClick 更新广告点击记录
// @Tags AdClick
// @Summary 更新广告点击记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdClick true "更新广告点击记录"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /adclick/updateAdClick [put]
func (adclickApi *AdClickApi) UpdateAdClick(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var adclick meta.AdClick
	err := c.ShouldBindJSON(&adclick)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adclickService.UpdateAdClick(ctx, adclick)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindAdClick 用id查询广告点击记录
// @Tags AdClick
// @Summary 用id查询广告点击记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询广告点击记录"
// @Success 200 {object} response.Response{data=meta.AdClick,msg=string} "查询成功"
// @Router /adclick/findAdClick [get]
func (adclickApi *AdClickApi) FindAdClick(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	readclick, err := adclickService.GetAdClick(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(readclick, c)
}

// GetAdClickList 分页获取广告点击记录列表
// @Tags AdClick
// @Summary 分页获取广告点击记录列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdClickSearch true "分页获取广告点击记录列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /adclick/getAdClickList [get]
func (adclickApi *AdClickApi) GetAdClickList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo metaReq.AdClickSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if 1 > len(pageInfo.Sort) {
		pageInfo.Sort = "ID"
		pageInfo.Order = "descending"
	}
	list, total, err := adclickService.GetAdClickInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetAdClickDataSource 获取AdClick的数据源
// @Tags AdClick
// @Summary 获取AdClick的数据源
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /adclick/getAdClickDataSource [get]
func (adclickApi *AdClickApi) GetAdClickDataSource(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口为获取数据源定义的数据
	dataSource, err := adclickService.GetAdClickDataSource(ctx)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(dataSource, c)
}

// GetAdClickPublic 不需要鉴权的广告点击记录接口
// @Tags AdClick
// @Summary 不需要鉴权的广告点击记录接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adclick/getAdClickPublic [get]
func (adclickApi *AdClickApi) GetAdClickPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	adclickService.GetAdClickPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的广告点击记录接口信息",
	}, "获取成功", c)
}
