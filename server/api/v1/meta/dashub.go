package meta

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type DashubApi struct {}



// CreateDashub 创建统计汇总
// @Tags Dashub
// @Summary 创建统计汇总
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.Dashub true "创建统计汇总"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /dashub/createDashub [post]
func (dashubApi *DashubApi) CreateDashub(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var dashub meta.Dashub
	err := c.ShouldBind<PERSON>(&dashub)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = dashubService.CreateDashub(ctx,&dashub)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteDashub 删除统计汇总
// @Tags Dashub
// @Summary 删除统计汇总
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.Dashub true "删除统计汇总"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /dashub/deleteDashub [delete]
func (dashubApi *DashubApi) DeleteDashub(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ID := c.Query("ID")
	err := dashubService.DeleteDashub(ctx,ID)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteDashubByIds 批量删除统计汇总
// @Tags Dashub
// @Summary 批量删除统计汇总
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /dashub/deleteDashubByIds [delete]
func (dashubApi *DashubApi) DeleteDashubByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := dashubService.DeleteDashubByIds(ctx,IDs)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateDashub 更新统计汇总
// @Tags Dashub
// @Summary 更新统计汇总
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.Dashub true "更新统计汇总"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /dashub/updateDashub [put]
func (dashubApi *DashubApi) UpdateDashub(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var dashub meta.Dashub
	err := c.ShouldBindJSON(&dashub)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = dashubService.UpdateDashub(ctx,dashub)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindDashub 用id查询统计汇总
// @Tags Dashub
// @Summary 用id查询统计汇总
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询统计汇总"
// @Success 200 {object} response.Response{data=meta.Dashub,msg=string} "查询成功"
// @Router /dashub/findDashub [get]
func (dashubApi *DashubApi) FindDashub(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ID := c.Query("ID")
	redashub, err := dashubService.GetDashub(ctx,ID)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(redashub, c)
}
// GetDashubList 分页获取统计汇总列表
// @Tags Dashub
// @Summary 分页获取统计汇总列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.DashubSearch true "分页获取统计汇总列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /dashub/getDashubList [get]
func (dashubApi *DashubApi) GetDashubList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo metaReq.DashubSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := dashubService.GetDashubInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}

// GetDashubPublic 不需要鉴权的统计汇总接口
// @Tags Dashub
// @Summary 不需要鉴权的统计汇总接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dashub/getDashubPublic [get]
func (dashubApi *DashubApi) GetDashubPublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    dashubService.GetDashubPublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的统计汇总接口信息",
    }, "获取成功", c)
}
