package meta

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type AdPositionApi struct {}



// CreateAdPosition 创建广告位置管理
// @Tags AdPosition
// @Summary 创建广告位置管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdPosition true "创建广告位置管理"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /adposition/createAdPosition [post]
func (adpositionApi *AdPositionApi) CreateAdPosition(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var adposition meta.AdPosition
	err := c.ShouldBindJSON(&adposition)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adpositionService.CreateAdPosition(ctx,&adposition)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteAdPosition 删除广告位置管理
// @Tags AdPosition
// @Summary 删除广告位置管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdPosition true "删除广告位置管理"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /adposition/deleteAdPosition [delete]
func (adpositionApi *AdPositionApi) DeleteAdPosition(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	err := adpositionService.DeleteAdPosition(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAdPositionByIds 批量删除广告位置管理
// @Tags AdPosition
// @Summary 批量删除广告位置管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /adposition/deleteAdPositionByIds [delete]
func (adpositionApi *AdPositionApi) DeleteAdPositionByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := adpositionService.DeleteAdPositionByIds(ctx,ids)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateAdPosition 更新广告位置管理
// @Tags AdPosition
// @Summary 更新广告位置管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdPosition true "更新广告位置管理"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /adposition/updateAdPosition [put]
func (adpositionApi *AdPositionApi) UpdateAdPosition(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var adposition meta.AdPosition
	err := c.ShouldBindJSON(&adposition)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adpositionService.UpdateAdPosition(ctx,adposition)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindAdPosition 用id查询广告位置管理
// @Tags AdPosition
// @Summary 用id查询广告位置管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询广告位置管理"
// @Success 200 {object} response.Response{data=meta.AdPosition,msg=string} "查询成功"
// @Router /adposition/findAdPosition [get]
func (adpositionApi *AdPositionApi) FindAdPosition(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	readposition, err := adpositionService.GetAdPosition(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(readposition, c)
}
// GetAdPositionList 分页获取广告位置管理列表
// @Tags AdPosition
// @Summary 分页获取广告位置管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdPositionSearch true "分页获取广告位置管理列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /adposition/getAdPositionList [get]
func (adpositionApi *AdPositionApi) GetAdPositionList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo metaReq.AdPositionSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := adpositionService.GetAdPositionInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}

// GetAdPositionPublic 不需要鉴权的广告位置管理接口
// @Tags AdPosition
// @Summary 不需要鉴权的广告位置管理接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adposition/getAdPositionPublic [get]
func (adpositionApi *AdPositionApi) GetAdPositionPublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    adpositionService.GetAdPositionPublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的广告位置管理接口信息",
    }, "获取成功", c)
}
