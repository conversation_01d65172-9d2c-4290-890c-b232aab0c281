package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ConsumeRecordApi struct{}

// CreateConsumeRecord 创建消费记录
// @Tags ConsumeRecord
// @Summary 创建消费记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.ConsumeRecord true "创建消费记录"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /consumerecord/createConsumeRecord [post]
func (consumerecordApi *ConsumeRecordApi) CreateConsumeRecord(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var consumerecord meta.ConsumeRecord
	err := c.ShouldBindJSON(&consumerecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = consumerecordService.CreateConsumeRecord(ctx, &consumerecord)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteConsumeRecord 删除消费记录
// @Tags ConsumeRecord
// @Summary 删除消费记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.ConsumeRecord true "删除消费记录"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /consumerecord/deleteConsumeRecord [delete]
func (consumerecordApi *ConsumeRecordApi) DeleteConsumeRecord(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := consumerecordService.DeleteConsumeRecord(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteConsumeRecordByIds 批量删除消费记录
// @Tags ConsumeRecord
// @Summary 批量删除消费记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /consumerecord/deleteConsumeRecordByIds [delete]
func (consumerecordApi *ConsumeRecordApi) DeleteConsumeRecordByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := consumerecordService.DeleteConsumeRecordByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateConsumeRecord 更新消费记录
// @Tags ConsumeRecord
// @Summary 更新消费记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.ConsumeRecord true "更新消费记录"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /consumerecord/updateConsumeRecord [put]
func (consumerecordApi *ConsumeRecordApi) UpdateConsumeRecord(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var consumerecord meta.ConsumeRecord
	err := c.ShouldBindJSON(&consumerecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = consumerecordService.UpdateConsumeRecord(ctx, consumerecord)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindConsumeRecord 用id查询消费记录
// @Tags ConsumeRecord
// @Summary 用id查询消费记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询消费记录"
// @Success 200 {object} response.Response{data=meta.ConsumeRecord,msg=string} "查询成功"
// @Router /consumerecord/findConsumeRecord [get]
func (consumerecordApi *ConsumeRecordApi) FindConsumeRecord(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	reconsumerecord, err := consumerecordService.GetConsumeRecord(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reconsumerecord, c)
}

// GetConsumeRecordList 分页获取消费记录列表
// @Tags ConsumeRecord
// @Summary 分页获取消费记录列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.ConsumeRecordSearch true "分页获取消费记录列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /consumerecord/getConsumeRecordList [get]
func (consumerecordApi *ConsumeRecordApi) GetConsumeRecordList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo metaReq.ConsumeRecordSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if 1 > len(pageInfo.Sort) {
		pageInfo.Sort = "ID"
		pageInfo.Order = "descending"
	}
	list, total, err := consumerecordService.GetConsumeRecordInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetConsumeRecordDataSource 获取ConsumeRecord的数据源
// @Tags ConsumeRecord
// @Summary 获取ConsumeRecord的数据源
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /consumerecord/getConsumeRecordDataSource [get]
func (consumerecordApi *ConsumeRecordApi) GetConsumeRecordDataSource(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口为获取数据源定义的数据
	dataSource, err := consumerecordService.GetConsumeRecordDataSource(ctx)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(dataSource, c)
}

// GetConsumeRecordPublic 不需要鉴权的消费记录接口
// @Tags ConsumeRecord
// @Summary 不需要鉴权的消费记录接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /consumerecord/getConsumeRecordPublic [get]
func (consumerecordApi *ConsumeRecordApi) GetConsumeRecordPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	consumerecordService.GetConsumeRecordPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的消费记录接口信息",
	}, "获取成功", c)
}
