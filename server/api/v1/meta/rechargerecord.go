package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type RechargeRecordApi struct{}

// CreateRechargeRecord 创建充值记录
// @Tags RechargeRecord
// @Summary 创建充值记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.RechargeRecord true "创建充值记录"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /rechargerecord/createRechargeRecord [post]
func (rechargerecordApi *RechargeRecordApi) CreateRechargeRecord(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var rechargerecord meta.RechargeRecord
	err := c.ShouldBindJSON(&rechargerecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = rechargerecordService.CreateRechargeRecord(ctx, &rechargerecord)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteRechargeRecord 删除充值记录
// @Tags RechargeRecord
// @Summary 删除充值记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.RechargeRecord true "删除充值记录"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /rechargerecord/deleteRechargeRecord [delete]
func (rechargerecordApi *RechargeRecordApi) DeleteRechargeRecord(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := rechargerecordService.DeleteRechargeRecord(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteRechargeRecordByIds 批量删除充值记录
// @Tags RechargeRecord
// @Summary 批量删除充值记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /rechargerecord/deleteRechargeRecordByIds [delete]
func (rechargerecordApi *RechargeRecordApi) DeleteRechargeRecordByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := rechargerecordService.DeleteRechargeRecordByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateRechargeRecord 更新充值记录
// @Tags RechargeRecord
// @Summary 更新充值记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.RechargeRecord true "更新充值记录"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /rechargerecord/updateRechargeRecord [put]
func (rechargerecordApi *RechargeRecordApi) UpdateRechargeRecord(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var rechargerecord meta.RechargeRecord
	err := c.ShouldBindJSON(&rechargerecord)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = rechargerecordService.UpdateRechargeRecord(ctx, rechargerecord)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindRechargeRecord 用id查询充值记录
// @Tags RechargeRecord
// @Summary 用id查询充值记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询充值记录"
// @Success 200 {object} response.Response{data=meta.RechargeRecord,msg=string} "查询成功"
// @Router /rechargerecord/findRechargeRecord [get]
func (rechargerecordApi *RechargeRecordApi) FindRechargeRecord(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	rerechargerecord, err := rechargerecordService.GetRechargeRecord(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(rerechargerecord, c)
}

// GetRechargeRecordList 分页获取充值记录列表
// @Tags RechargeRecord
// @Summary 分页获取充值记录列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.RechargeRecordSearch true "分页获取充值记录列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /rechargerecord/getRechargeRecordList [get]
func (rechargerecordApi *RechargeRecordApi) GetRechargeRecordList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo metaReq.RechargeRecordSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if 1 > len(pageInfo.Sort) {
		pageInfo.Sort = "ID"
		pageInfo.Order = "descending"
	}
	list, total, err := rechargerecordService.GetRechargeRecordInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetRechargeRecordDataSource 获取RechargeRecord的数据源
// @Tags RechargeRecord
// @Summary 获取RechargeRecord的数据源
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /rechargerecord/getRechargeRecordDataSource [get]
func (rechargerecordApi *RechargeRecordApi) GetRechargeRecordDataSource(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口为获取数据源定义的数据
	dataSource, err := rechargerecordService.GetRechargeRecordDataSource(ctx)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(dataSource, c)
}

// GetRechargeRecordPublic 不需要鉴权的充值记录接口
// @Tags RechargeRecord
// @Summary 不需要鉴权的充值记录接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /rechargerecord/getRechargeRecordPublic [get]
func (rechargerecordApi *RechargeRecordApi) GetRechargeRecordPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	rechargerecordService.GetRechargeRecordPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的充值记录接口信息",
	}, "获取成功", c)
}
