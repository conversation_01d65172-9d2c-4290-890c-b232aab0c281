package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AdStatisticsApi struct{}

// CreateAdStatistics 创建广告统计
// @Tags AdStatistics
// @Summary 创建广告统计
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdStatistics true "创建广告统计"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /adstatistics/createAdStatistics [post]
func (adstatisticsApi *AdStatisticsApi) CreateAdStatistics(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var adstatistics meta.AdStatistics
	err := c.ShouldBindJSON(&adstatistics)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adstatisticsService.CreateAdStatistics(ctx, &adstatistics)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteAdStatistics 删除广告统计
// @Tags AdStatistics
// @Summary 删除广告统计
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdStatistics true "删除广告统计"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /adstatistics/deleteAdStatistics [delete]
func (adstatisticsApi *AdStatisticsApi) DeleteAdStatistics(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := adstatisticsService.DeleteAdStatistics(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAdStatisticsByIds 批量删除广告统计
// @Tags AdStatistics
// @Summary 批量删除广告统计
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /adstatistics/deleteAdStatisticsByIds [delete]
func (adstatisticsApi *AdStatisticsApi) DeleteAdStatisticsByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := adstatisticsService.DeleteAdStatisticsByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateAdStatistics 更新广告统计
// @Tags AdStatistics
// @Summary 更新广告统计
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdStatistics true "更新广告统计"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /adstatistics/updateAdStatistics [put]
func (adstatisticsApi *AdStatisticsApi) UpdateAdStatistics(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var adstatistics meta.AdStatistics
	err := c.ShouldBindJSON(&adstatistics)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adstatisticsService.UpdateAdStatistics(ctx, adstatistics)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindAdStatistics 用id查询广告统计
// @Tags AdStatistics
// @Summary 用id查询广告统计
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询广告统计"
// @Success 200 {object} response.Response{data=meta.AdStatistics,msg=string} "查询成功"
// @Router /adstatistics/findAdStatistics [get]
func (adstatisticsApi *AdStatisticsApi) FindAdStatistics(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	readstatistics, err := adstatisticsService.GetAdStatistics(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(readstatistics, c)
}

// GetAdStatisticsList 分页获取广告统计列表
// @Tags AdStatistics
// @Summary 分页获取广告统计列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdStatisticsSearch true "分页获取广告统计列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /adstatistics/getAdStatisticsList [get]
func (adstatisticsApi *AdStatisticsApi) GetAdStatisticsList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo metaReq.AdStatisticsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if 1 > len(pageInfo.Sort) {
		pageInfo.Sort = "ID"
		pageInfo.Order = "descending"
	}
	list, total, err := adstatisticsService.GetAdStatisticsInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetAdStatisticsDataSource 获取AdStatistics的数据源
// @Tags AdStatistics
// @Summary 获取AdStatistics的数据源
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /adstatistics/getAdStatisticsDataSource [get]
func (adstatisticsApi *AdStatisticsApi) GetAdStatisticsDataSource(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口为获取数据源定义的数据
	dataSource, err := adstatisticsService.GetAdStatisticsDataSource(ctx)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(dataSource, c)
}

// GetAdStatisticsPublic 不需要鉴权的广告统计接口
// @Tags AdStatistics
// @Summary 不需要鉴权的广告统计接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adstatistics/getAdStatisticsPublic [get]
func (adstatisticsApi *AdStatisticsApi) GetAdStatisticsPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	adstatisticsService.GetAdStatisticsPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的广告统计接口信息",
	}, "获取成功", c)
}
