package meta

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type DeviceDataApi struct {}



// CreateDeviceData 创建设备数据
// @Tags DeviceData
// @Summary 创建设备数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.DeviceData true "创建设备数据"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /devicedata/createDeviceData [post]
func (devicedataApi *DeviceDataApi) CreateDeviceData(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var devicedata meta.DeviceData
	err := c.ShouldBindJSON(&devicedata)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = devicedataService.CreateDeviceData(ctx,&devicedata)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteDeviceData 删除设备数据
// @Tags DeviceData
// @Summary 删除设备数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.DeviceData true "删除设备数据"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /devicedata/deleteDeviceData [delete]
func (devicedataApi *DeviceDataApi) DeleteDeviceData(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ID := c.Query("ID")
	err := devicedataService.DeleteDeviceData(ctx,ID)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteDeviceDataByIds 批量删除设备数据
// @Tags DeviceData
// @Summary 批量删除设备数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /devicedata/deleteDeviceDataByIds [delete]
func (devicedataApi *DeviceDataApi) DeleteDeviceDataByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := devicedataService.DeleteDeviceDataByIds(ctx,IDs)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateDeviceData 更新设备数据
// @Tags DeviceData
// @Summary 更新设备数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.DeviceData true "更新设备数据"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /devicedata/updateDeviceData [put]
func (devicedataApi *DeviceDataApi) UpdateDeviceData(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var devicedata meta.DeviceData
	err := c.ShouldBindJSON(&devicedata)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = devicedataService.UpdateDeviceData(ctx,devicedata)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindDeviceData 用id查询设备数据
// @Tags DeviceData
// @Summary 用id查询设备数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询设备数据"
// @Success 200 {object} response.Response{data=meta.DeviceData,msg=string} "查询成功"
// @Router /devicedata/findDeviceData [get]
func (devicedataApi *DeviceDataApi) FindDeviceData(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ID := c.Query("ID")
	redevicedata, err := devicedataService.GetDeviceData(ctx,ID)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(redevicedata, c)
}
// GetDeviceDataList 分页获取设备数据列表
// @Tags DeviceData
// @Summary 分页获取设备数据列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.DeviceDataSearch true "分页获取设备数据列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /devicedata/getDeviceDataList [get]
func (devicedataApi *DeviceDataApi) GetDeviceDataList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo metaReq.DeviceDataSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := devicedataService.GetDeviceDataInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}
// GetDeviceDataDataSource 获取DeviceData的数据源
// @Tags DeviceData
// @Summary 获取DeviceData的数据源
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /devicedata/getDeviceDataDataSource [get]
func (devicedataApi *DeviceDataApi) GetDeviceDataDataSource(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口为获取数据源定义的数据
    dataSource, err := devicedataService.GetDeviceDataDataSource(ctx)
    if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
   		response.FailWithMessage("查询失败:" + err.Error(), c)
   		return
    }
   response.OkWithData(dataSource, c)
}

// GetDeviceDataPublic 不需要鉴权的设备数据接口
// @Tags DeviceData
// @Summary 不需要鉴权的设备数据接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /devicedata/getDeviceDataPublic [get]
func (devicedataApi *DeviceDataApi) GetDeviceDataPublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    devicedataService.GetDeviceDataPublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的设备数据接口信息",
    }, "获取成功", c)
}
