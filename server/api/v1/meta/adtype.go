package meta

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type AdTypeApi struct {}



// CreateAdType 创建广告类型管理
// @Tags AdType
// @Summary 创建广告类型管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdType true "创建广告类型管理"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /adtype/createAdType [post]
func (adtypeApi *AdTypeApi) CreateAdType(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var adtype meta.AdType
	err := c.ShouldBindJSON(&adtype)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adtypeService.CreateAdType(ctx,&adtype)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteAdType 删除广告类型管理
// @Tags AdType
// @Summary 删除广告类型管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdType true "删除广告类型管理"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /adtype/deleteAdType [delete]
func (adtypeApi *AdTypeApi) DeleteAdType(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	err := adtypeService.DeleteAdType(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAdTypeByIds 批量删除广告类型管理
// @Tags AdType
// @Summary 批量删除广告类型管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /adtype/deleteAdTypeByIds [delete]
func (adtypeApi *AdTypeApi) DeleteAdTypeByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := adtypeService.DeleteAdTypeByIds(ctx,ids)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateAdType 更新广告类型管理
// @Tags AdType
// @Summary 更新广告类型管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdType true "更新广告类型管理"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /adtype/updateAdType [put]
func (adtypeApi *AdTypeApi) UpdateAdType(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var adtype meta.AdType
	err := c.ShouldBindJSON(&adtype)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adtypeService.UpdateAdType(ctx,adtype)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindAdType 用id查询广告类型管理
// @Tags AdType
// @Summary 用id查询广告类型管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询广告类型管理"
// @Success 200 {object} response.Response{data=meta.AdType,msg=string} "查询成功"
// @Router /adtype/findAdType [get]
func (adtypeApi *AdTypeApi) FindAdType(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	readtype, err := adtypeService.GetAdType(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(readtype, c)
}
// GetAdTypeList 分页获取广告类型管理列表
// @Tags AdType
// @Summary 分页获取广告类型管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdTypeSearch true "分页获取广告类型管理列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /adtype/getAdTypeList [get]
func (adtypeApi *AdTypeApi) GetAdTypeList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo metaReq.AdTypeSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := adtypeService.GetAdTypeInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}

// GetAdTypePublic 不需要鉴权的广告类型管理接口
// @Tags AdType
// @Summary 不需要鉴权的广告类型管理接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adtype/getAdTypePublic [get]
func (adtypeApi *AdTypeApi) GetAdTypePublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    adtypeService.GetAdTypePublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的广告类型管理接口信息",
    }, "获取成功", c)
}
