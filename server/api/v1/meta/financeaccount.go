package meta

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type FinanceAccountApi struct {}



// CreateFinanceAccount 创建财务账户
// @Tags FinanceAccount
// @Summary 创建财务账户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.FinanceAccount true "创建财务账户"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /financeaccount/createFinanceAccount [post]
func (financeaccountApi *FinanceAccountApi) CreateFinanceAccount(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var financeaccount meta.FinanceAccount
	err := c.ShouldBindJSON(&financeaccount)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = financeaccountService.CreateFinanceAccount(ctx,&financeaccount)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteFinanceAccount 删除财务账户
// @Tags FinanceAccount
// @Summary 删除财务账户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.FinanceAccount true "删除财务账户"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /financeaccount/deleteFinanceAccount [delete]
func (financeaccountApi *FinanceAccountApi) DeleteFinanceAccount(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ID := c.Query("ID")
	err := financeaccountService.DeleteFinanceAccount(ctx,ID)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteFinanceAccountByIds 批量删除财务账户
// @Tags FinanceAccount
// @Summary 批量删除财务账户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /financeaccount/deleteFinanceAccountByIds [delete]
func (financeaccountApi *FinanceAccountApi) DeleteFinanceAccountByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := financeaccountService.DeleteFinanceAccountByIds(ctx,IDs)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateFinanceAccount 更新财务账户
// @Tags FinanceAccount
// @Summary 更新财务账户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.FinanceAccount true "更新财务账户"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /financeaccount/updateFinanceAccount [put]
func (financeaccountApi *FinanceAccountApi) UpdateFinanceAccount(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var financeaccount meta.FinanceAccount
	err := c.ShouldBindJSON(&financeaccount)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = financeaccountService.UpdateFinanceAccount(ctx,financeaccount)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindFinanceAccount 用id查询财务账户
// @Tags FinanceAccount
// @Summary 用id查询财务账户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询财务账户"
// @Success 200 {object} response.Response{data=meta.FinanceAccount,msg=string} "查询成功"
// @Router /financeaccount/findFinanceAccount [get]
func (financeaccountApi *FinanceAccountApi) FindFinanceAccount(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ID := c.Query("ID")
	refinanceaccount, err := financeaccountService.GetFinanceAccount(ctx,ID)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(refinanceaccount, c)
}
// GetFinanceAccountList 分页获取财务账户列表
// @Tags FinanceAccount
// @Summary 分页获取财务账户列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.FinanceAccountSearch true "分页获取财务账户列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /financeaccount/getFinanceAccountList [get]
func (financeaccountApi *FinanceAccountApi) GetFinanceAccountList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo metaReq.FinanceAccountSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := financeaccountService.GetFinanceAccountInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}
// GetFinanceAccountDataSource 获取FinanceAccount的数据源
// @Tags FinanceAccount
// @Summary 获取FinanceAccount的数据源
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /financeaccount/getFinanceAccountDataSource [get]
func (financeaccountApi *FinanceAccountApi) GetFinanceAccountDataSource(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口为获取数据源定义的数据
    dataSource, err := financeaccountService.GetFinanceAccountDataSource(ctx)
    if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
   		response.FailWithMessage("查询失败:" + err.Error(), c)
   		return
    }
   response.OkWithData(dataSource, c)
}

// GetFinanceAccountPublic 不需要鉴权的财务账户接口
// @Tags FinanceAccount
// @Summary 不需要鉴权的财务账户接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /financeaccount/getFinanceAccountPublic [get]
func (financeaccountApi *FinanceAccountApi) GetFinanceAccountPublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    financeaccountService.GetFinanceAccountPublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的财务账户接口信息",
    }, "获取成功", c)
}
