package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type SdkApi struct{}

// GetAds 获取广告列表
// @Tags SDK
// @Summary 获取广告列表
// @Description 根据包名、广告位ID、广告类型ID获取符合条件的广告列表
// @Accept json
// @Produce json
// @Param data body metaReq.GetAdsRequest true "请求参数"
// @Success 200 {object} response.Response{data=response.GetAdsResponse,msg=string} "获取成功"
// @Router /sdk/ads [post]
func (s *SdkApi) GetAds(c *gin.Context) {
	var req metaReq.GetAdsRequest
	err := c.ShouldBindJSO<PERSON>(&req)
	if err != nil {
		c.JSO<PERSON>(200, gin.H{"code": 7, "data": map[string]interface{}{}, "msg": err.Error()})
		return
	}

	ads, err := sdkService.GetAds(c.Request.Context(), &req)
	if err != nil {
		global.GVA_LOG.Error("获取广告列表失败!", zap.Error(err))
		c.JSON(200, gin.H{"code": 7, "data": map[string]interface{}{}, "msg": "获取广告列表失败: " + err.Error()})
		return
	}

	c.JSON(200, gin.H{"code": 0, "data": ads, "msg": "获取成功"})
}

// ReportEvents 上报广告事件
// @Tags SDK
// @Summary 上报广告事件
// @Description 批量上报广告展示、点击等事件
// @Accept json
// @Produce json
// @Param data body metaReq.AdEventRequest true "事件数据"
// @Success 200 {object} response.Response{data=response.AdEventResponse,msg=string} "上报成功"
// @Router /sdk/report [post]
func (s *SdkApi) ReportEvents(c *gin.Context) {
	var req metaReq.AdEventRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		c.JSON(200, gin.H{"code": 7, "data": map[string]interface{}{}, "msg": err.Error()})
		return
	}

	result, err := sdkService.ReportEvents(c.Request.Context(), &req)
	if err != nil {
		global.GVA_LOG.Error("上报广告事件失败!", zap.Error(err))
		c.JSON(200, gin.H{"code": 7, "data": map[string]interface{}{}, "msg": "上报广告事件失败: " + err.Error()})
		return
	}

	if result.Success {
		c.JSON(200, gin.H{"code": 0, "data": result, "msg": "上报成功"})
	} else {
		c.JSON(200, gin.H{"code": 7, "data": result, "msg": "部分事件上报失败"})
	}
}
