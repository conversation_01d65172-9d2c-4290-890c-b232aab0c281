package meta

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type AuditStatusApi struct {}



// CreateAuditStatus 创建审核状态配置
// @Tags AuditStatus
// @Summary 创建审核状态配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AuditStatus true "创建审核状态配置"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /auditstatus/createAuditStatus [post]
func (auditstatusApi *AuditStatusApi) CreateAuditStatus(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var auditstatus meta.AuditStatus
	err := c.ShouldBindJSON(&auditstatus)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = auditstatusService.CreateAuditStatus(ctx,&auditstatus)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteAuditStatus 删除审核状态配置
// @Tags AuditStatus
// @Summary 删除审核状态配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AuditStatus true "删除审核状态配置"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /auditstatus/deleteAuditStatus [delete]
func (auditstatusApi *AuditStatusApi) DeleteAuditStatus(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	err := auditstatusService.DeleteAuditStatus(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAuditStatusByIds 批量删除审核状态配置
// @Tags AuditStatus
// @Summary 批量删除审核状态配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /auditstatus/deleteAuditStatusByIds [delete]
func (auditstatusApi *AuditStatusApi) DeleteAuditStatusByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := auditstatusService.DeleteAuditStatusByIds(ctx,ids)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateAuditStatus 更新审核状态配置
// @Tags AuditStatus
// @Summary 更新审核状态配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AuditStatus true "更新审核状态配置"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /auditstatus/updateAuditStatus [put]
func (auditstatusApi *AuditStatusApi) UpdateAuditStatus(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var auditstatus meta.AuditStatus
	err := c.ShouldBindJSON(&auditstatus)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = auditstatusService.UpdateAuditStatus(ctx,auditstatus)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindAuditStatus 用id查询审核状态配置
// @Tags AuditStatus
// @Summary 用id查询审核状态配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询审核状态配置"
// @Success 200 {object} response.Response{data=meta.AuditStatus,msg=string} "查询成功"
// @Router /auditstatus/findAuditStatus [get]
func (auditstatusApi *AuditStatusApi) FindAuditStatus(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	reauditstatus, err := auditstatusService.GetAuditStatus(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(reauditstatus, c)
}
// GetAuditStatusList 分页获取审核状态配置列表
// @Tags AuditStatus
// @Summary 分页获取审核状态配置列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AuditStatusSearch true "分页获取审核状态配置列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /auditstatus/getAuditStatusList [get]
func (auditstatusApi *AuditStatusApi) GetAuditStatusList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo metaReq.AuditStatusSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := auditstatusService.GetAuditStatusInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}

// GetAuditStatusPublic 不需要鉴权的审核状态配置接口
// @Tags AuditStatus
// @Summary 不需要鉴权的审核状态配置接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /auditstatus/getAuditStatusPublic [get]
func (auditstatusApi *AuditStatusApi) GetAuditStatusPublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    auditstatusService.GetAuditStatusPublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的审核状态配置接口信息",
    }, "获取成功", c)
}
