package meta

import "github.com/flipped-aurora/gin-vue-admin/server/service"

type ApiGroup struct {
	ConsumeRecordApi
	AdReviewApi
	DeviceDataApi
	CampaignApi
	FinanceAccountApi
	AdStatisticsApi
	AdActionApi
	AdPositionApi
	AdClickApi
	AdImpressionApi
	RechargeRecordApi
	AdApi
	MediaFileApi
	AdPriceApi
	AppApi
	AdTypeApi
	AuditStatusApi
	DashboardApi
	DashubApi
	DashubDashboardApi
	SdkApi
}

var (
	consumerecordService  = service.ServiceGroupApp.MetaServiceGroup.ConsumeRecordService
	adreviewService       = service.ServiceGroupApp.MetaServiceGroup.AdReviewService
	devicedataService     = service.ServiceGroupApp.MetaServiceGroup.DeviceDataService
	campaignService       = service.ServiceGroupApp.MetaServiceGroup.CampaignService
	financeaccountService = service.ServiceGroupApp.MetaServiceGroup.FinanceAccountService
	adstatisticsService   = service.ServiceGroupApp.MetaServiceGroup.AdStatisticsService
	adactionService       = service.ServiceGroupApp.MetaServiceGroup.AdActionService
	adpositionService     = service.ServiceGroupApp.MetaServiceGroup.AdPositionService
	adclickService        = service.ServiceGroupApp.MetaServiceGroup.AdClickService
	adimpressionService   = service.ServiceGroupApp.MetaServiceGroup.AdImpressionService
	rechargerecordService = service.ServiceGroupApp.MetaServiceGroup.RechargeRecordService
	adService             = service.ServiceGroupApp.MetaServiceGroup.AdService
	mediafileService      = service.ServiceGroupApp.MetaServiceGroup.MediaFileService
	adpriceService        = service.ServiceGroupApp.MetaServiceGroup.AdPriceService
	appService            = service.ServiceGroupApp.MetaServiceGroup.AppService
	adtypeService         = service.ServiceGroupApp.MetaServiceGroup.AdTypeService
	auditstatusService    = service.ServiceGroupApp.MetaServiceGroup.AuditStatusService
	dashubService         = service.ServiceGroupApp.MetaServiceGroup.DashubService
	sdkService            = service.ServiceGroupApp.MetaServiceGroup.SdkService
)
