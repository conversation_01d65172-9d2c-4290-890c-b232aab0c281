package meta

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type AdActionApi struct {}



// CreateAdAction 创建广告行为管理
// @Tags AdAction
// @Summary 创建广告行为管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdAction true "创建广告行为管理"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /adaction/createAdAction [post]
func (adactionApi *AdActionApi) CreateAdAction(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var adaction meta.AdAction
	err := c.ShouldBindJSON(&adaction)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adactionService.CreateAdAction(ctx,&adaction)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteAdAction 删除广告行为管理
// @Tags AdAction
// @Summary 删除广告行为管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdAction true "删除广告行为管理"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /adaction/deleteAdAction [delete]
func (adactionApi *AdActionApi) DeleteAdAction(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	err := adactionService.DeleteAdAction(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAdActionByIds 批量删除广告行为管理
// @Tags AdAction
// @Summary 批量删除广告行为管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /adaction/deleteAdActionByIds [delete]
func (adactionApi *AdActionApi) DeleteAdActionByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := adactionService.DeleteAdActionByIds(ctx,ids)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateAdAction 更新广告行为管理
// @Tags AdAction
// @Summary 更新广告行为管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdAction true "更新广告行为管理"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /adaction/updateAdAction [put]
func (adactionApi *AdActionApi) UpdateAdAction(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var adaction meta.AdAction
	err := c.ShouldBindJSON(&adaction)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adactionService.UpdateAdAction(ctx,adaction)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindAdAction 用id查询广告行为管理
// @Tags AdAction
// @Summary 用id查询广告行为管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询广告行为管理"
// @Success 200 {object} response.Response{data=meta.AdAction,msg=string} "查询成功"
// @Router /adaction/findAdAction [get]
func (adactionApi *AdActionApi) FindAdAction(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	readaction, err := adactionService.GetAdAction(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(readaction, c)
}
// GetAdActionList 分页获取广告行为管理列表
// @Tags AdAction
// @Summary 分页获取广告行为管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdActionSearch true "分页获取广告行为管理列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /adaction/getAdActionList [get]
func (adactionApi *AdActionApi) GetAdActionList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo metaReq.AdActionSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := adactionService.GetAdActionInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}

// GetAdActionPublic 不需要鉴权的广告行为管理接口
// @Tags AdAction
// @Summary 不需要鉴权的广告行为管理接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adaction/getAdActionPublic [get]
func (adactionApi *AdActionApi) GetAdActionPublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    adactionService.GetAdActionPublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的广告行为管理接口信息",
    }, "获取成功", c)
}
