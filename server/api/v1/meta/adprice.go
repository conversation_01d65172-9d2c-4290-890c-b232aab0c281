package meta

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type AdPriceApi struct {}



// CreateAdPrice 创建广告价格管理
// @Tags AdPrice
// @Summary 创建广告价格管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdPrice true "创建广告价格管理"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /adprice/createAdPrice [post]
func (adpriceApi *AdPriceApi) CreateAdPrice(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var adprice meta.AdPrice
	err := c.ShouldBindJSON(&adprice)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adpriceService.CreateAdPrice(ctx,&adprice)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteAdPrice 删除广告价格管理
// @Tags AdPrice
// @Summary 删除广告价格管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdPrice true "删除广告价格管理"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /adprice/deleteAdPrice [delete]
func (adpriceApi *AdPriceApi) DeleteAdPrice(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	err := adpriceService.DeleteAdPrice(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAdPriceByIds 批量删除广告价格管理
// @Tags AdPrice
// @Summary 批量删除广告价格管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /adprice/deleteAdPriceByIds [delete]
func (adpriceApi *AdPriceApi) DeleteAdPriceByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ids := c.QueryArray("ids[]")
	err := adpriceService.DeleteAdPriceByIds(ctx,ids)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateAdPrice 更新广告价格管理
// @Tags AdPrice
// @Summary 更新广告价格管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdPrice true "更新广告价格管理"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /adprice/updateAdPrice [put]
func (adpriceApi *AdPriceApi) UpdateAdPrice(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var adprice meta.AdPrice
	err := c.ShouldBindJSON(&adprice)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adpriceService.UpdateAdPrice(ctx,adprice)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindAdPrice 用id查询广告价格管理
// @Tags AdPrice
// @Summary 用id查询广告价格管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param id query int true "用id查询广告价格管理"
// @Success 200 {object} response.Response{data=meta.AdPrice,msg=string} "查询成功"
// @Router /adprice/findAdPrice [get]
func (adpriceApi *AdPriceApi) FindAdPrice(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	id := c.Query("id")
	readprice, err := adpriceService.GetAdPrice(ctx,id)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(readprice, c)
}
// GetAdPriceList 分页获取广告价格管理列表
// @Tags AdPrice
// @Summary 分页获取广告价格管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdPriceSearch true "分页获取广告价格管理列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /adprice/getAdPriceList [get]
func (adpriceApi *AdPriceApi) GetAdPriceList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo metaReq.AdPriceSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := adpriceService.GetAdPriceInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}
// GetAdPriceDataSource 获取AdPrice的数据源
// @Tags AdPrice
// @Summary 获取AdPrice的数据源
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /adprice/getAdPriceDataSource [get]
func (adpriceApi *AdPriceApi) GetAdPriceDataSource(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口为获取数据源定义的数据
    dataSource, err := adpriceService.GetAdPriceDataSource(ctx)
    if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
   		response.FailWithMessage("查询失败:" + err.Error(), c)
   		return
    }
   response.OkWithData(dataSource, c)
}

// GetAdPricePublic 不需要鉴权的广告价格管理接口
// @Tags AdPrice
// @Summary 不需要鉴权的广告价格管理接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adprice/getAdPricePublic [get]
func (adpriceApi *AdPriceApi) GetAdPricePublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    adpriceService.GetAdPricePublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的广告价格管理接口信息",
    }, "获取成功", c)
}
