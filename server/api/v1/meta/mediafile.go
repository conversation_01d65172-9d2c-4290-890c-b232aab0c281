package meta

import (
	
	"github.com/flipped-aurora/gin-vue-admin/server/global"
    "github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
    "github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
    "github.com/gin-gonic/gin"
    "go.uber.org/zap"
)

type MediaFileApi struct {}



// CreateMediaFile 创建媒体文件
// @Tags MediaFile
// @Summary 创建媒体文件
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.MediaFile true "创建媒体文件"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /mediafile/createMediaFile [post]
func (mediafileApi *MediaFileApi) CreateMediaFile(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var mediafile meta.MediaFile
	err := c.ShouldBindJSON(&mediafile)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = mediafileService.CreateMediaFile(ctx,&mediafile)
	if err != nil {
        global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:" + err.Error(), c)
		return
	}
    response.OkWithMessage("创建成功", c)
}

// DeleteMediaFile 删除媒体文件
// @Tags MediaFile
// @Summary 删除媒体文件
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.MediaFile true "删除媒体文件"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /mediafile/deleteMediaFile [delete]
func (mediafileApi *MediaFileApi) DeleteMediaFile(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ID := c.Query("ID")
	err := mediafileService.DeleteMediaFile(ctx,ID)
	if err != nil {
        global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteMediaFileByIds 批量删除媒体文件
// @Tags MediaFile
// @Summary 批量删除媒体文件
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /mediafile/deleteMediaFileByIds [delete]
func (mediafileApi *MediaFileApi) DeleteMediaFileByIds(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := mediafileService.DeleteMediaFileByIds(ctx,IDs)
	if err != nil {
        global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateMediaFile 更新媒体文件
// @Tags MediaFile
// @Summary 更新媒体文件
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.MediaFile true "更新媒体文件"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /mediafile/updateMediaFile [put]
func (mediafileApi *MediaFileApi) UpdateMediaFile(c *gin.Context) {
    // 从ctx获取标准context进行业务行为
    ctx := c.Request.Context()

	var mediafile meta.MediaFile
	err := c.ShouldBindJSON(&mediafile)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = mediafileService.UpdateMediaFile(ctx,mediafile)
	if err != nil {
        global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:" + err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindMediaFile 用id查询媒体文件
// @Tags MediaFile
// @Summary 用id查询媒体文件
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询媒体文件"
// @Success 200 {object} response.Response{data=meta.MediaFile,msg=string} "查询成功"
// @Router /mediafile/findMediaFile [get]
func (mediafileApi *MediaFileApi) FindMediaFile(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	ID := c.Query("ID")
	remediafile, err := mediafileService.GetMediaFile(ctx,ID)
	if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:" + err.Error(), c)
		return
	}
	response.OkWithData(remediafile, c)
}
// GetMediaFileList 分页获取媒体文件列表
// @Tags MediaFile
// @Summary 分页获取媒体文件列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.MediaFileSearch true "分页获取媒体文件列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /mediafile/getMediaFileList [get]
func (mediafileApi *MediaFileApi) GetMediaFileList(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

	var pageInfo metaReq.MediaFileSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := mediafileService.GetMediaFileInfoList(ctx,pageInfo)
	if err != nil {
	    global.GVA_LOG.Error("获取失败!", zap.Error(err))
        response.FailWithMessage("获取失败:" + err.Error(), c)
        return
    }
    response.OkWithDetailed(response.PageResult{
        List:     list,
        Total:    total,
        Page:     pageInfo.Page,
        PageSize: pageInfo.PageSize,
    }, "获取成功", c)
}
// GetMediaFileDataSource 获取MediaFile的数据源
// @Tags MediaFile
// @Summary 获取MediaFile的数据源
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /mediafile/getMediaFileDataSource [get]
func (mediafileApi *MediaFileApi) GetMediaFileDataSource(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口为获取数据源定义的数据
    dataSource, err := mediafileService.GetMediaFileDataSource(ctx)
    if err != nil {
        global.GVA_LOG.Error("查询失败!", zap.Error(err))
   		response.FailWithMessage("查询失败:" + err.Error(), c)
   		return
    }
   response.OkWithData(dataSource, c)
}

// GetMediaFilePublic 不需要鉴权的媒体文件接口
// @Tags MediaFile
// @Summary 不需要鉴权的媒体文件接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /mediafile/getMediaFilePublic [get]
func (mediafileApi *MediaFileApi) GetMediaFilePublic(c *gin.Context) {
    // 创建业务用Context
    ctx := c.Request.Context()

    // 此接口不需要鉴权
    // 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
    mediafileService.GetMediaFilePublic(ctx)
    response.OkWithDetailed(gin.H{
       "info": "不需要鉴权的媒体文件接口信息",
    }, "获取成功", c)
}
