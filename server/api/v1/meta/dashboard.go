package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/service"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DashboardApi struct{}

// GetDashboardData
// @Tags Dashboard
// @Summary 获取Dashboard数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dashboard/data [get]
func (dashboardApi *DashboardApi) GetDashboardData(c *gin.Context) {
	// 获取当前用户信息
	claims, err := utils.GetClaims(c)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	userID := claims.BaseClaims.ID
	authorityID := claims.AuthorityId

	// 调用服务获取Dashboard数据
	dashboardData, err := service.ServiceGroupApp.MetaServiceGroup.DashboardService.GetDashboardData(userID, authorityID)
	if err != nil {
		global.GVA_LOG.Error("获取Dashboard数据失败!", zap.Error(err))
		response.FailWithMessage("获取Dashboard数据失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(dashboardData, "获取成功", c)
}
