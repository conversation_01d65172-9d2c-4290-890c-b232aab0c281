package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CampaignApi struct{}

// CreateCampaign 创建广告投放
// @Tags Campaign
// @Summary 创建广告投放
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.Campaign true "创建广告投放"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /campaign/createCampaign [post]
func (campaignApi *CampaignApi) CreateCampaign(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var campaign meta.Campaign
	err := c.ShouldBindJSON(&campaign)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// TODO 默认为上线状态
	cStatus := "running"
	campaign.Status = &cStatus

	err = campaignService.CreateCampaign(ctx, &campaign)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteCampaign 删除广告投放
// @Tags Campaign
// @Summary 删除广告投放
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.Campaign true "删除广告投放"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /campaign/deleteCampaign [delete]
func (campaignApi *CampaignApi) DeleteCampaign(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := campaignService.DeleteCampaign(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteCampaignByIds 批量删除广告投放
// @Tags Campaign
// @Summary 批量删除广告投放
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /campaign/deleteCampaignByIds [delete]
func (campaignApi *CampaignApi) DeleteCampaignByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := campaignService.DeleteCampaignByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateCampaign 更新广告投放
// @Tags Campaign
// @Summary 更新广告投放
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.Campaign true "更新广告投放"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /campaign/updateCampaign [put]
func (campaignApi *CampaignApi) UpdateCampaign(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var campaign meta.Campaign
	err := c.ShouldBindJSON(&campaign)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = campaignService.UpdateCampaign(ctx, campaign)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// PauseCampaign 暂停广告投放
// @Tags Campaign
// @Summary 暂停广告投放
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.Campaign true "暂停广告投放"
// @Success 200 {object} response.Response{msg=string} "暂停成功"
// @Router /campaign/pauseCampaign [post]
func (campaignApi *CampaignApi) PauseCampaign(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var campaignids metaReq.CampaignPause
	err := c.ShouldBindJSON(&campaignids)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = campaignService.PauseCampaign(ctx, campaignids)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindCampaign 用id查询广告投放
// @Tags Campaign
// @Summary 用id查询广告投放
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询广告投放"
// @Success 200 {object} response.Response{data=meta.Campaign,msg=string} "查询成功"
// @Router /campaign/findCampaign [get]
func (campaignApi *CampaignApi) FindCampaign(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	recampaign, err := campaignService.GetCampaign(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(recampaign, c)
}

// GetCampaignList 分页获取广告投放列表
// @Tags Campaign
// @Summary 分页获取广告投放列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.CampaignSearch true "分页获取广告投放列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /campaign/getCampaignList [get]
func (campaignApi *CampaignApi) GetCampaignList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo metaReq.CampaignSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if 1 > len(pageInfo.Sort) {
		pageInfo.Sort = "ID"
		pageInfo.Order = "descending"
	}
	list, total, err := campaignService.GetCampaignInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetCampaignDataSource 获取Campaign的数据源
// @Tags Campaign
// @Summary 获取Campaign的数据源
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /campaign/getCampaignDataSource [get]
func (campaignApi *CampaignApi) GetCampaignDataSource(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口为获取数据源定义的数据
	dataSource, err := campaignService.GetCampaignDataSource(ctx)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(dataSource, c)
}

// GetCampaignPublic 不需要鉴权的广告投放接口
// @Tags Campaign
// @Summary 不需要鉴权的广告投放接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /campaign/getCampaignPublic [get]
func (campaignApi *CampaignApi) GetCampaignPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	campaignService.GetCampaignPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的广告投放接口信息",
	}, "获取成功", c)
}
