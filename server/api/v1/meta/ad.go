package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AdApi struct{}

// CreateAd 创建广告管理
// @Tags Ad
// @Summary 创建广告管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.Ad true "创建广告管理"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /ad/createAd [post]
func (adApi *AdApi) CreateAd(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var ad meta.Ad
	err := c.ShouldBindJSON(&ad)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// TODO 默认为上线状态
	adstatus := 5
	ad.Status = &adstatus

	err = adService.CreateAd(ctx, &ad)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteAd 删除广告管理
// @Tags Ad
// @Summary 删除广告管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.Ad true "删除广告管理"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /ad/deleteAd [delete]
func (adApi *AdApi) DeleteAd(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := adService.DeleteAd(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAdByIds 批量删除广告管理
// @Tags Ad
// @Summary 批量删除广告管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /ad/deleteAdByIds [delete]
func (adApi *AdApi) DeleteAdByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := adService.DeleteAdByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateAd 更新广告管理
// @Tags Ad
// @Summary 更新广告管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.Ad true "更新广告管理"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /ad/updateAd [put]
func (adApi *AdApi) UpdateAd(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var ad meta.Ad
	err := c.ShouldBindJSON(&ad)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adService.UpdateAd(ctx, ad)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindAd 用id查询广告管理
// @Tags Ad
// @Summary 用id查询广告管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询广告管理"
// @Success 200 {object} response.Response{data=meta.Ad,msg=string} "查询成功"
// @Router /ad/findAd [get]
func (adApi *AdApi) FindAd(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	read, err := adService.GetAd(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(read, c)
}

// GetAdList 分页获取广告管理列表
// @Tags Ad
// @Summary 分页获取广告管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdSearch true "分页获取广告管理列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /ad/getAdList [get]
func (adApi *AdApi) GetAdList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo metaReq.AdSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if 1 > len(pageInfo.Sort) {
		pageInfo.Sort = "ID"
		pageInfo.Order = "descending"
	}
	list, total, err := adService.GetAdInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetAdDataSource 获取Ad的数据源
// @Tags Ad
// @Summary 获取Ad的数据源
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /ad/getAdDataSource [get]
func (adApi *AdApi) GetAdDataSource(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口为获取数据源定义的数据
	dataSource, err := adService.GetAdDataSource(ctx)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(dataSource, c)
}

// GetAdPublic 不需要鉴权的广告管理接口
// @Tags Ad
// @Summary 不需要鉴权的广告管理接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /ad/getAdPublic [get]
func (adApi *AdApi) GetAdPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	adService.GetAdPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的广告管理接口信息",
	}, "获取成功", c)
}

// CreateAdWithCampaign 广告向导 - 自动创建广告和投放计划
// @Tags Ad
// @Summary 广告向导 - 自动创建广告和投放计划
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body metaReq.AdWizardRequest true "广告向导请求"
// @Success 200 {object} response.Response{data=metaReq.AdWizardResponse,msg=string} "创建成功"
// @Router /ad/createAdWithCampaign [post]
func (adApi *AdApi) CreateAdWithCampaign(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var req metaReq.AdWizardRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 获取当前用户ID
	userId := utils.GetUserID(c)
	if userId == 0 {
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	result, err := adService.CreateAdWithCampaign(ctx, &req, int(userId))
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}
