package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AppApi struct{}

// CreateApp 创建应用管理
// @Tags App
// @Summary 创建应用管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.App true "创建应用管理"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /app/createApp [post]
func (appApi *AppApi) CreateApp(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var app meta.App
	err := c.ShouldBindJSON(&app)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = appService.CreateApp(ctx, &app)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteApp 删除应用管理
// @Tags App
// @Summary 删除应用管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.App true "删除应用管理"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /app/deleteApp [delete]
func (appApi *AppApi) DeleteApp(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := appService.DeleteApp(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAppByIds 批量删除应用管理
// @Tags App
// @Summary 批量删除应用管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /app/deleteAppByIds [delete]
func (appApi *AppApi) DeleteAppByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := appService.DeleteAppByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateApp 更新应用管理
// @Tags App
// @Summary 更新应用管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.App true "更新应用管理"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /app/updateApp [put]
func (appApi *AppApi) UpdateApp(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var app meta.App
	err := c.ShouldBindJSON(&app)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = appService.UpdateApp(ctx, app)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindApp 用id查询应用管理
// @Tags App
// @Summary 用id查询应用管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询应用管理"
// @Success 200 {object} response.Response{data=meta.App,msg=string} "查询成功"
// @Router /app/findApp [get]
func (appApi *AppApi) FindApp(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	reapp, err := appService.GetApp(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reapp, c)
}

// GetAppList 分页获取应用管理列表
// @Tags App
// @Summary 分页获取应用管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AppSearch true "分页获取应用管理列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /app/getAppList [get]
func (appApi *AppApi) GetAppList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo metaReq.AppSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if 1 > len(pageInfo.Sort) {
		pageInfo.Sort = "ID"
		pageInfo.Order = "descending"
	}
	list, total, err := appService.GetAppInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetAppDataSource 获取App的数据源
// @Tags App
// @Summary 获取App的数据源
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /app/getAppDataSource [get]
func (appApi *AppApi) GetAppDataSource(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口为获取数据源定义的数据
	dataSource, err := appService.GetAppDataSource(ctx)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(dataSource, c)
}

// GetAppPublic 不需要鉴权的应用管理接口
// @Tags App
// @Summary 不需要鉴权的应用管理接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /app/getAppPublic [get]
func (appApi *AppApi) GetAppPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	appService.GetAppPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的应用管理接口信息",
	}, "获取成功", c)
}
