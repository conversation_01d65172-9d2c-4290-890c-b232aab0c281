package meta

import (
	"strconv"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaService "github.com/flipped-aurora/gin-vue-admin/server/service/meta"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type DashubDashboardApi struct{}

var dashubDashboardService = metaService.DashubDashboardService{}

// GetDashubDashboardData 获取Dashboard数据（基于Dashub表）
// @Tags DashubDashboard
// @Summary 获取Dashboard数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=metaService.DashubDashboardResponse,msg=string} "获取成功"
// @Router /dashub-dashboard/data [get]
func (api *DashubDashboardApi) GetDashubDashboardData(c *gin.Context) {
	// 获取用户信息
	claims, err := utils.GetClaims(c)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	userID := claims.BaseClaims.ID
	authorityID := claims.AuthorityId

	// 获取Dashboard数据
	data, err := dashubDashboardService.GetDashubDashboardData(userID, authorityID)
	if err != nil {
		global.GVA_LOG.Error("获取Dashboard数据失败!", zap.Error(err))
		response.FailWithMessage("获取Dashboard数据失败: "+err.Error(), c)
		return
	}

	response.OkWithData(data, c)
}

// GetMultiPeriodChart 获取多周期图表数据
// @Tags DashubDashboard
// @Summary 获取多周期图表数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param kinds query string true "统计类型ID列表，用逗号分隔，如：1001,1002,1003"
// @Success 200 {object} response.Response{data=interface{},msg=string} "获取成功"
// @Router /dashub-dashboard/multi-period-chart [get]
func (api *DashubDashboardApi) GetMultiPeriodChart(c *gin.Context) {
	// 获取用户信息
	claims, err := utils.GetClaims(c)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	userID := claims.BaseClaims.ID
	authorityID := claims.AuthorityId

	// 获取kinds参数
	kindsStr := c.Query("kinds")
	if kindsStr == "" {
		response.FailWithMessage("kinds参数不能为空", c)
		return
	}

	// 解析kinds参数
	kindsStrList := strings.Split(kindsStr, ",")
	var kinds []int
	for _, kindStr := range kindsStrList {
		kind, err := strconv.Atoi(kindStr)
		if err != nil {
			response.FailWithMessage("kinds参数格式错误", c)
			return
		}
		kinds = append(kinds, kind)
	}

	// 获取图表数据
	data, err := dashubDashboardService.GetMultiPeriodChartData(kinds, userID, authorityID)
	if err != nil {
		global.GVA_LOG.Error("获取多周期图表数据失败!", zap.Error(err))
		response.FailWithMessage("获取多周期图表数据失败: "+err.Error(), c)
		return
	}

	response.OkWithData(data, c)
}

// GetChartData 获取单个图表数据
// @Tags DashubDashboard
// @Summary 获取单个图表数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param kind query int true "统计类型ID"
// @Success 200 {object} response.Response{data=interface{},msg=string} "获取成功"
// @Router /dashub-dashboard/chart-data [get]
func (api *DashubDashboardApi) GetChartData(c *gin.Context) {
	// 获取用户信息
	claims, err := utils.GetClaims(c)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	userID := claims.BaseClaims.ID
	authorityID := claims.AuthorityId

	// 获取kind参数
	kindStr := c.Query("kind")
	if kindStr == "" {
		response.FailWithMessage("kind参数不能为空", c)
		return
	}

	kind, err := strconv.Atoi(kindStr)
	if err != nil {
		response.FailWithMessage("kind参数格式错误", c)
		return
	}

	// 获取图表数据
	data, err := dashubDashboardService.GetChartData(kind, userID, authorityID)
	if err != nil {
		global.GVA_LOG.Error("获取图表数据失败!", zap.Error(err))
		response.FailWithMessage("获取图表数据失败: "+err.Error(), c)
		return
	}

	response.OkWithData(data, c)
}

// GetDashubItemConfigs 获取所有统计项目配置
// @Tags DashubDashboard
// @Summary 获取所有统计项目配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[int]meta.DashubItemConfig,msg=string} "获取成功"
// @Router /dashub-dashboard/item-configs [get]
func (api *DashubDashboardApi) GetDashubItemConfigs(c *gin.Context) {
	// 获取用户信息
	claims, err := utils.GetClaims(c)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	authorityID := claims.AuthorityId

	// 获取用户权限对应的项目配置
	allowedKinds, exists := meta.GetRolePermissions(authorityID)
	if !exists {
		response.FailWithMessage("未找到角色权限配置", c)
		return
	}

	// 构造返回数据
	configs := make(map[int]meta.DashubItemConfig)
	for _, kind := range allowedKinds {
		config, exists := meta.GetDashubItemConfig(kind)
		if exists {
			configs[kind] = config
		}
	}

	response.OkWithData(configs, c)
}

// GetRolePermissions 获取角色权限
// @Tags DashubDashboard
// @Summary 获取角色权限
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=[]int,msg=string} "获取成功"
// @Router /dashub-dashboard/role-permissions [get]
func (api *DashubDashboardApi) GetRolePermissions(c *gin.Context) {
	// 获取用户信息
	claims, err := utils.GetClaims(c)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	authorityID := claims.AuthorityId

	// 获取用户权限
	permissions, exists := meta.GetRolePermissions(authorityID)
	if !exists {
		response.FailWithMessage("未找到角色权限配置", c)
		return
	}

	response.OkWithData(permissions, c)
}

// GetDashubStats 获取Dashub统计信息
// @Tags DashubDashboard
// @Summary 获取Dashub统计信息
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]interface{},msg=string} "获取成功"
// @Router /dashub-dashboard/stats [get]
func (api *DashubDashboardApi) GetDashubStats(c *gin.Context) {
	// 获取用户信息
	claims, err := utils.GetClaims(c)
	if err != nil {
		global.GVA_LOG.Error("获取用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取用户信息失败", c)
		return
	}

	userID := claims.BaseClaims.ID
	authorityID := claims.AuthorityId

	// 统计信息
	stats := make(map[string]interface{})

	// 统计总记录数
	var totalCount int64
	query := global.GVA_DB.Model(&meta.Dashub{})
	if authorityID != 888 { // 非管理员只能看自己的数据
		query = query.Where("organize = ?", userID)
	}
	query.Count(&totalCount)
	stats["totalRecords"] = totalCount

	// 统计数据类型数量
	var kindCount int64
	kindQuery := global.GVA_DB.Model(&meta.Dashub{}).Select("DISTINCT kind")
	if authorityID != 888 {
		kindQuery = kindQuery.Where("organize = ?", userID)
	}
	kindQuery.Count(&kindCount)
	stats["kindCount"] = kindCount

	// 统计日期范围
	var minDate, maxDate string
	dateQuery := global.GVA_DB.Model(&meta.Dashub{})
	if authorityID != 888 {
		dateQuery = dateQuery.Where("organize = ?", userID)
	}
	dateQuery.Select("MIN(day) as min_date, MAX(day) as max_date").
		Scan(&struct {
			MinDate string `json:"min_date"`
			MaxDate string `json:"max_date"`
		}{MinDate: minDate, MaxDate: maxDate})
	stats["dateRange"] = map[string]string{
		"start": minDate,
		"end":   maxDate,
	}

	// 用户权限信息
	permissions, _ := meta.GetRolePermissions(authorityID)
	stats["permissions"] = permissions
	stats["authorityId"] = authorityID
	stats["userId"] = userID

	response.OkWithData(stats, c)
}
