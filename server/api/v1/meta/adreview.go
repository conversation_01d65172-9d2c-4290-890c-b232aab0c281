package meta

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type AdReviewApi struct{}

// CreateAdReview 创建广告审核
// @Tags AdReview
// @Summary 创建广告审核
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdReview true "创建广告审核"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /adreview/createAdReview [post]
func (adreviewApi *AdReviewApi) CreateAdReview(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var adreview meta.AdReview
	err := c.ShouldBindJSON(&adreview)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adreviewService.CreateAdReview(ctx, &adreview)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteAdReview 删除广告审核
// @Tags AdReview
// @Summary 删除广告审核
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdReview true "删除广告审核"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /adreview/deleteAdReview [delete]
func (adreviewApi *AdReviewApi) DeleteAdReview(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := adreviewService.DeleteAdReview(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteAdReviewByIds 批量删除广告审核
// @Tags AdReview
// @Summary 批量删除广告审核
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /adreview/deleteAdReviewByIds [delete]
func (adreviewApi *AdReviewApi) DeleteAdReviewByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := adreviewService.DeleteAdReviewByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateAdReview 更新广告审核
// @Tags AdReview
// @Summary 更新广告审核
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body meta.AdReview true "更新广告审核"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /adreview/updateAdReview [put]
func (adreviewApi *AdReviewApi) UpdateAdReview(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var adreview meta.AdReview
	err := c.ShouldBindJSON(&adreview)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = adreviewService.UpdateAdReview(ctx, adreview)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindAdReview 用id查询广告审核
// @Tags AdReview
// @Summary 用id查询广告审核
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询广告审核"
// @Success 200 {object} response.Response{data=meta.AdReview,msg=string} "查询成功"
// @Router /adreview/findAdReview [get]
func (adreviewApi *AdReviewApi) FindAdReview(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	readreview, err := adreviewService.GetAdReview(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(readreview, c)
}

// GetAdReviewList 分页获取广告审核列表
// @Tags AdReview
// @Summary 分页获取广告审核列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdReviewSearch true "分页获取广告审核列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /adreview/getAdReviewList [get]
func (adreviewApi *AdReviewApi) GetAdReviewList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo metaReq.AdReviewSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	if 1 > len(pageInfo.Sort) {
		pageInfo.Sort = "ID"
		pageInfo.Order = "descending"
	}
	list, total, err := adreviewService.GetAdReviewInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetAdReviewDataSource 获取AdReview的数据源
// @Tags AdReview
// @Summary 获取AdReview的数据源
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "查询成功"
// @Router /adreview/getAdReviewDataSource [get]
func (adreviewApi *AdReviewApi) GetAdReviewDataSource(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口为获取数据源定义的数据
	dataSource, err := adreviewService.GetAdReviewDataSource(ctx)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(dataSource, c)
}

// GetAdReviewPublic 不需要鉴权的广告审核接口
// @Tags AdReview
// @Summary 不需要鉴权的广告审核接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adreview/getAdReviewPublic [get]
func (adreviewApi *AdReviewApi) GetAdReviewPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	adreviewService.GetAdReviewPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的广告审核接口信息",
	}, "获取成功", c)
}
