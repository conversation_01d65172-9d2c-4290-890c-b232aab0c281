package utils

import (
	"fmt"
	"math/rand"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
)

// DashubMockGenerator Dashub数据生成器
type DashubMockGenerator struct {
	StartDate time.Time
	Days      int
}

// NewDashubMockGenerator 创建新的数据生成器
func NewDashubMockGenerator(days int) *DashubMockGenerator {
	// 从7天前开始生成数据
	startDate := time.Now().AddDate(0, 0, -days+1)
	return &DashubMockGenerator{
		StartDate: startDate,
		Days:      days,
	}
}

// GenerateAllMockData 生成所有类型的mock数据
func (g *DashubMockGenerator) GenerateAllMockData() []meta.Dashub {
	var allData []meta.Dashub

	// 为每一天生成数据
	for i := 0; i < g.Days; i++ {
		currentDate := g.StartDate.AddDate(0, 0, i)
		dayStr := currentDate.Format("2006-01-02")

		// 生成用户相关数据
		allData = append(allData, g.generateUserData(dayStr, i)...)

		// 生成广告相关数据
		allData = append(allData, g.generateAdData(dayStr, i)...)

		// 生成财务相关数据
		allData = append(allData, g.generateFinanceData(dayStr, i)...)

		// 生成系统相关数据（只在最后一天生成）
		if i == g.Days-1 {
			allData = append(allData, g.generateSystemData(dayStr)...)
		}
	}

	return allData
}

// generateUserData 生成用户相关数据
func (g *DashubMockGenerator) generateUserData(day string, dayIndex int) []meta.Dashub {
	var data []meta.Dashub

	// 基础用户数据，随着时间增长
	baseUsers := 10000 + dayIndex*50
	dailyActive := baseUsers/2 + rand.Intn(1000)
	weeklyActive := int(float64(dailyActive) * 1.5)
	monthlyActive := int(float64(dailyActive) * 2.0)
	newUsers := 20 + rand.Intn(80)

	data = append(data, []meta.Dashub{
		{Day: &day, Kind: &[]int{meta.DASHUB_DAILY_ACTIVE_USERS}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(dailyActive)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_WEEKLY_ACTIVE_USERS}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(weeklyActive)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_MONTHLY_ACTIVE_USERS}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(monthlyActive)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_TOTAL_USERS}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(baseUsers)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_NEW_USERS}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(newUsers)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_ACTIVE_USERS}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(dailyActive)}[0]},
	}...)

	return data
}

// generateAdData 生成广告相关数据
func (g *DashubMockGenerator) generateAdData(day string, dayIndex int) []meta.Dashub {
	var data []meta.Dashub

	// 广告数据，有一定的随机性
	impressions := 50000 + rand.Intn(20000)
	clicks := impressions/100 + rand.Intn(500) // 点击率约1%
	conversions := clicks/20 + rand.Intn(50)   // 转化率约5%
	activeAds := 100 + rand.Intn(50)
	ctr := float64(clicks) / float64(impressions) * 100
	cvr := float64(conversions) / float64(clicks) * 100

	data = append(data, []meta.Dashub{
		{Day: &day, Kind: &[]int{meta.DASHUB_AD_IMPRESSIONS}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(impressions)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_AD_CLICKS}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(clicks)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_AD_CONVERSIONS}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(conversions)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_ACTIVE_ADS}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(activeAds)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_AD_CTR}[0], Target: &[]int{0}[0], Nums: &[]float64{ctr}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_AD_CVR}[0], Target: &[]int{0}[0], Nums: &[]float64{cvr}[0]},
	}...)

	return data
}

// generateFinanceData 生成财务相关数据
func (g *DashubMockGenerator) generateFinanceData(day string, dayIndex int) []meta.Dashub {
	var data []meta.Dashub

	// 财务数据
	dailyConsume := 1000 + rand.Intn(2000)
	weeklyConsume := dailyConsume * 7
	monthlyConsume := dailyConsume * 30
	dailyRevenue := 800 + rand.Intn(1500)
	weeklyRevenue := dailyRevenue * 7
	monthlyRevenue := dailyRevenue * 30

	// 累计数据随时间增长
	totalRecharge := 50000 + dayIndex*1000
	totalEarning := 40000 + dayIndex*800
	accountBalance := 10000 + rand.Intn(5000)

	data = append(data, []meta.Dashub{
		{Day: &day, Kind: &[]int{meta.DASHUB_DAILY_CONSUME}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(dailyConsume)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_WEEKLY_CONSUME}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(weeklyConsume)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_MONTHLY_CONSUME}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(monthlyConsume)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_DAILY_REVENUE}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(dailyRevenue)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_WEEKLY_REVENUE}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(weeklyRevenue)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_MONTHLY_REVENUE}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(monthlyRevenue)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_TOTAL_RECHARGE}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(totalRecharge)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_TOTAL_EARNING}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(totalEarning)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_ACCOUNT_BALANCE}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(accountBalance)}[0]},
	}...)

	return data
}

// generateSystemData 生成系统相关数据
func (g *DashubMockGenerator) generateSystemData(day string) []meta.Dashub {
	var data []meta.Dashub

	// 系统数据
	appCount := 50 + rand.Intn(20)
	campaignCount := 200 + rand.Intn(100)
	systemStatus := 1 // 1表示正常

	data = append(data, []meta.Dashub{
		{Day: &day, Kind: &[]int{meta.DASHUB_APP_COUNT}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(appCount)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_CAMPAIGN_COUNT}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(campaignCount)}[0]},
		{Day: &day, Kind: &[]int{meta.DASHUB_SYSTEM_STATUS}[0], Target: &[]int{0}[0], Nums: &[]float64{float64(systemStatus)}[0]},
	}...)

	return data
}

// GenerateSQLFile 生成SQL插入文件
func (g *DashubMockGenerator) GenerateSQLFile() string {
	data := g.GenerateAllMockData()

	var sql string
	sql += "-- Dashub Mock Data\n"
	sql += "-- Generated at: " + time.Now().Format("2006-01-02 15:04:05") + "\n\n"

	// 清理旧数据（可选）
	sql += "-- 清理旧的mock数据（可选）\n"
	sql += "-- DELETE FROM meta_dashubs WHERE organize = 0;\n\n"

	sql += "-- 插入mock数据\n"
	sql += "INSERT INTO meta_dashubs (created_at, updated_at, organize, day, kind, target, nums) VALUES\n"

	for i, item := range data {
		now := time.Now().Format("2006-01-02 15:04:05")
		sql += fmt.Sprintf("('%s', '%s', 0, '%s', %d, %d, %.2f)",
			now, now, *item.Day, *item.Kind, *item.Target, *item.Nums)

		if i < len(data)-1 {
			sql += ",\n"
		} else {
			sql += ";\n"
		}
	}

	return sql
}

// GenerateRoleMockData 为特定角色生成mock数据
func (g *DashubMockGenerator) GenerateRoleMockData(authorityID uint, userID uint) []meta.Dashub {
	allData := g.GenerateAllMockData()
	permissions, exists := meta.GetRolePermissions(authorityID)
	if !exists {
		return []meta.Dashub{}
	}

	// 过滤出该角色可以查看的数据
	var filteredData []meta.Dashub
	permissionMap := make(map[int]bool)
	for _, perm := range permissions {
		permissionMap[perm] = true
	}

	for _, item := range allData {
		if permissionMap[*item.Kind] {
			// 创建新的item并设置组织ID为用户ID
			newItem := item
			//organize := int(userID)
			//newItem.Organize = &organize
			filteredData = append(filteredData, newItem)
		}
	}

	return filteredData
}

// GenerateRoleSQLFile 为特定角色生成SQL文件
func (g *DashubMockGenerator) GenerateRoleSQLFile(authorityID uint, userID uint) string {
	data := g.GenerateRoleMockData(authorityID, userID)

	var sql string
	sql += fmt.Sprintf("-- Dashub Mock Data for Role %d, User %d\n", authorityID, userID)
	sql += "-- Generated at: " + time.Now().Format("2006-01-02 15:04:05") + "\n\n"

	if len(data) == 0 {
		sql += "-- No data for this role\n"
		return sql
	}

	sql += fmt.Sprintf("-- 清理用户 %d 的旧数据（可选）\n", userID)
	sql += fmt.Sprintf("-- DELETE FROM meta_dashubs WHERE organize = %d;\n\n", userID)

	sql += "-- 插入mock数据\n"
	sql += "INSERT INTO meta_dashubs (created_at, updated_at, organize, day, kind, target, nums) VALUES\n"

	for i, item := range data {
		now := time.Now().Format("2006-01-02 15:04:05")
		sql += fmt.Sprintf("('%s', '%s', %d, '%s', %d, %d, %.2f)",
			now, now, *item.Day, *item.Kind, *item.Target, *item.Nums)

		if i < len(data)-1 {
			sql += ",\n"
		} else {
			sql += ";\n"
		}
	}

	return sql
}
