
// 自动生成模板Campaign
package meta
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"time"
	"gorm.io/datatypes"
)

// 广告投放 结构体  Campaign
type Campaign struct {
    global.GVA_MODEL
  Name  *string `json:"name" form:"name" gorm:"comment:广告计划名称;column:name;size:100;" binding:"required"`  //计划名称
  UserId  *int `json:"user_id" form:"user_id" gorm:"index;comment:广告主用户ID;column:user_id;"`  //广告主
  Status  *string `json:"status" form:"status" gorm:"default:draft;comment:计划状态：draft-草稿,pending-待启动,running-投放中,paused-已暂停,stopped-已停止,completed-已完成;column:status;size:20;"`  //状态
  AdIds  datatypes.JSON `json:"ad_ids" form:"ad_ids" gorm:"comment:关联的广告ID列表;column:ad_ids;" swaggertype:"array,object" binding:"required"`  //关联广告
  BidType  *string `json:"bid_type" form:"bid_type" gorm:"default:CPC;comment:计费方式：CPC,CPM;column:bid_type;size:10;" binding:"required"`  //计费方式
  TargetApps  datatypes.JSON `json:"target_apps" form:"target_apps" gorm:"comment:投放APP ID列表;column:target_apps;" swaggertype:"array,object"`  //投放目标
  StartTime  *time.Time `json:"start_time" form:"start_time" gorm:"comment:投放开始时间;column:start_time;" binding:"required"`  //开始时间
  EndTime  *time.Time `json:"end_time" form:"end_time" gorm:"comment:投放结束时间;column:end_time;" binding:"required"`  //结束时间
  TotalBudget  *float64 `json:"total_budget" form:"total_budget" gorm:"default:0;comment:总预算金额;column:total_budget;" binding:"required"`  //总预算
  DailyBudget  *float64 `json:"daily_budget" form:"daily_budget" gorm:"default:0;comment:日预算金额;column:daily_budget;"`  //日预算
  BidAmount  *float64 `json:"bid_amount" form:"bid_amount" gorm:"default:0;comment:竞价出价金额;column:bid_amount;" binding:"required"`  //出价金额
  MaxImpressions  *int `json:"max_impressions" form:"max_impressions" gorm:"default:0;comment:最大展示次数限制，0表示不限制;column:max_impressions;"`  //最大展示次数
  MaxClicks  *int `json:"max_clicks" form:"max_clicks" gorm:"default:0;comment:最大点击次数限制，0表示不限制;column:max_clicks;"`  //最大点击次数
}


// TableName 广告投放 Campaign自定义表名 meta_campaigns
func (Campaign) TableName() string {
    return "meta_campaigns"
}





