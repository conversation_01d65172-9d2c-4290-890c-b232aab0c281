
// 自动生成模板AdAction
package meta
import (
)

// 广告行为管理 结构体  AdAction
type AdAction struct {
  ID  *int `json:"id" form:"id" gorm:"primarykey;comment:ID;column:id;size:20;" binding:"required"`  //ID
  Name  *string `json:"name" form:"name" gorm:"comment:广告行为名称;column:name;size:50;" binding:"required"`  //行为名称
  Description  *string `json:"description" form:"description" gorm:"comment:广告行为描述信息;column:description;size:200;"`  //行为描述
}


// TableName 广告行为管理 AdAction自定义表名 meta_ad_actions
func (AdAction) TableName() string {
    return "meta_ad_actions"
}





