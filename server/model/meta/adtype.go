
// 自动生成模板AdType
package meta
import (
)

// 广告类型管理 结构体  AdType
type AdType struct {
  ID  *int `json:"id" form:"id" gorm:"primarykey;comment:ID;column:id;size:20;" binding:"required"`  //ID
  Name  *string `json:"name" form:"name" gorm:"comment:广告类型名称;column:name;size:50;" binding:"required"`  //类型名称
  Description  *string `json:"description" form:"description" gorm:"comment:广告类型描述信息;column:description;size:200;"`  //类型描述
}


// TableName 广告类型管理 AdType自定义表名 meta_ad_types
func (AdType) TableName() string {
    return "meta_ad_types"
}





