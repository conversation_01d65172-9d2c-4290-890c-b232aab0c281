
// 自动生成模板AdReview
package meta
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"time"
)

// 广告审核 结构体  AdReview
type AdReview struct {
    global.GVA_MODEL
  AdId  *int `json:"ad_id" form:"ad_id" gorm:"index;comment:被审核的广告ID;column:ad_id;" binding:"required"`  //广告ID
  ReviewType  *string `json:"review_type" form:"review_type" gorm:"default:manual;comment:审核类型：auto-自动审核,manual-人工审核,appeal-申诉审核;column:review_type;size:20;" binding:"required"`  //审核类型
  ReviewStatus  *string `json:"review_status" form:"review_status" gorm:"default:pending;comment:审核状态：pending-待审核,reviewing-审核中,approved-通过,rejected-拒绝,cancelled-取消;column:review_status;size:20;"`  //审核状态
  ReviewResult  *string `json:"review_result" form:"review_result" gorm:"comment:审核结果：pass-通过,reject-拒绝,modify-需要修改;column:review_result;size:20;"`  //审核结果
  ReviewerId  *int `json:"reviewer_id" form:"reviewer_id" gorm:"comment:审核人用户ID;column:reviewer_id;"`  //审核人
  ReviewNote  *string `json:"review_note" form:"review_note" gorm:"comment:审核备注和意见;column:review_note;size:1000;"`  //审核备注
  RejectReason  *string `json:"reject_reason" form:"reject_reason" gorm:"comment:审核拒绝的具体原因;column:reject_reason;size:500;"`  //拒绝原因
  RiskLevel  *string `json:"risk_level" form:"risk_level" gorm:"default:low;comment:风险等级：low-低风险,medium-中风险,high-高风险;column:risk_level;size:20;"`  //风险等级
  AutoScore  *float64 `json:"auto_score" form:"auto_score" gorm:"default:0;comment:AI自动审核评分（0-100）;column:auto_score;"`  //自动评分
  SubmitTime  *time.Time `json:"submit_time" form:"submit_time" gorm:"comment:提交审核时间;column:submit_time;" binding:"required"`  //提交时间
  StartTime  *time.Time `json:"start_time" form:"start_time" gorm:"comment:开始审核时间;column:start_time;"`  //开始审核时间
  CompleteTime  *time.Time `json:"complete_time" form:"complete_time" gorm:"comment:审核完成时间;column:complete_time;"`  //完成时间
  ProcessingTime  *int `json:"processing_time" form:"processing_time" gorm:"default:0;comment:审核处理时长（秒）;column:processing_time;"`  //处理时长
  Priority  *int `json:"priority" form:"priority" gorm:"default:0;comment:审核优先级，数值越大优先级越高;column:priority;"`  //优先级
}


// TableName 广告审核 AdReview自定义表名 meta_ad_reviews
func (AdReview) TableName() string {
    return "meta_ad_reviews"
}





