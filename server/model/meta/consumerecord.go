
// 自动生成模板ConsumeRecord
package meta
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"time"
)

// 消费记录 结构体  ConsumeRecord
type ConsumeRecord struct {
    global.GVA_MODEL
  UserId  *int `json:"user_id" form:"user_id" gorm:"index;comment:消费用户;column:user_id;"`  //用户
  AdId  *int `json:"ad_id" form:"ad_id" gorm:"index;comment:相关广告;column:ad_id;"`  //关联广告
  CampaignId  *int `json:"campaign_id" form:"campaign_id" gorm:"index;comment:相关广告计划;column:campaign_id;"`  //关联计划
  ConsumeType  *string `json:"consume_type" form:"consume_type" gorm:"default:impression;comment:消费类型：impression-展示,click-点击,conversion-转化;column:consume_type;size:20;" binding:"required"`  //消费类型
  Amount  *float64 `json:"amount" form:"amount" gorm:"default:0;comment:消费金额;column:amount;" binding:"required"`  //消费金额
  Currency  *string `json:"currency" form:"currency" gorm:"default:CNY;comment:货币类型：CNY,USD,EUR等;column:currency;size:10;"`  //货币类型
  BidType  *string `json:"bid_type" form:"bid_type" gorm:"default:CPC;comment:计费方式：CPC,CPM,CPA;column:bid_type;size:10;" binding:"required"`  //计费方式
  BidAmount  *float64 `json:"bid_amount" form:"bid_amount" gorm:"default:0;comment:当时的出价金额;column:bid_amount;"`  //出价金额
  AppId  *int `json:"app_id" form:"app_id" gorm:"index;comment:消费发生的APP ID;column:app_id;"`  //APP ID
  AdPositionId  *int `json:"ad_position_id" form:"ad_position_id" gorm:"index;comment:消费发生的广告位;column:ad_position_id;"`  //广告位
  RelatedRecordId  *int `json:"related_record_id" form:"related_record_id" gorm:"index;comment:关联的展示或点击记录ID;column:related_record_id;"`  //关联记录ID
  ConsumeTime  *time.Time `json:"consume_time" form:"consume_time" gorm:"index;comment:消费发生时间;column:consume_time;" binding:"required"`  //消费时间
  BalanceBefore  *float64 `json:"balance_before" form:"balance_before" gorm:"default:0;comment:消费前账户余额;column:balance_before;"`  //消费前余额
  BalanceAfter  *float64 `json:"balance_after" form:"balance_after" gorm:"default:0;comment:消费后账户余额;column:balance_after;"`  //消费后余额
  Status  *string `json:"status" form:"status" gorm:"default:success;comment:消费状态：success-成功,failed-失败,refunded-已退款;column:status;size:20;"`  //状态
}


// TableName 消费记录 ConsumeRecord自定义表名 meta_consume_records
func (ConsumeRecord) TableName() string {
    return "meta_consume_records"
}





