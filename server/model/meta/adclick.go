
// 自动生成模板AdClick
package meta
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"time"
)

// 广告点击记录 结构体  AdClick
type AdClick struct {
    global.GVA_MODEL
  AdId  *int `json:"ad_id" form:"ad_id" gorm:"index;comment:关联广告;column:ad_id;" binding:"required"`  //关联广告
  CampaignId  *int `json:"campaign_id" form:"campaign_id" gorm:"index;comment:广告计划ID;column:campaign_id;" binding:"required"`  //关联投放计划
  AppId  *int `json:"app_id" form:"app_id" gorm:"index;comment:点击APP的ID;column:app_id;" binding:"required"`  //APP
  AdPositionId  *int `json:"ad_position_id" form:"ad_position_id" gorm:"index;comment:广告位置ID;column:ad_position_id;" binding:"required"`  //广告位ID
  ImpressionId  *int `json:"impression_id" form:"impression_id" gorm:"index;comment:关联的展示记录ID;column:impression_id;"`  //展示记录ID
  UserId  *string `json:"user_id" form:"user_id" gorm:"index;comment:点击广告的用户ID;column:user_id;size:100;"`  //用户ID
  DeviceId  *string `json:"device_id" form:"device_id" gorm:"index;comment:设备唯一标识;column:device_id;size:100;"`  //设备ID
  IpAddress  *string `json:"ip_address" form:"ip_address" gorm:"comment:用户IP地址;column:ip_address;size:45;"`  //IP地址
  UserAgent  *string `json:"user_agent" form:"user_agent" gorm:"comment:用户代理字符串;column:user_agent;size:500;"`  //用户代理
  Country  *string `json:"country" form:"country" gorm:"comment:用户所在国家;column:country;size:50;"`  //国家
  Province  *string `json:"province" form:"province" gorm:"comment:用户所在省份;column:province;size:50;"`  //省份
  City  *string `json:"city" form:"city" gorm:"comment:用户所在城市;column:city;size:50;"`  //城市
  Platform  *string `json:"platform" form:"platform" gorm:"comment:设备平台：android,ios,web;column:platform;size:20;"`  //平台
  ClickTime  *time.Time `json:"click_time" form:"click_time" gorm:"index;comment:广告点击时间;column:click_time;" binding:"required"`  //点击时间
  Cost  *float64 `json:"cost" form:"cost" gorm:"default:0;comment:本次点击的成本;column:cost;"`  //点击成本
  IsValid  *bool `json:"is_valid" form:"is_valid" gorm:"default:true;comment:点击是否有效（反作弊检测）;column:is_valid;"`  //是否有效
}


// TableName 广告点击记录 AdClick自定义表名 meta_ad_clicks
func (AdClick) TableName() string {
    return "meta_ad_clicks"
}





