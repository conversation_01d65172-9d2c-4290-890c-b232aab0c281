
// 自动生成模板AdPrice
package meta
import (
	"time"
)

// 广告价格管理 结构体  AdPrice
type AdPrice struct {
  ID  *int `json:"id" form:"id" gorm:"primarykey;comment:ID;column:id;size:20;" binding:"required"`  //ID
  AdPositionId  *int `json:"ad_position_id" form:"ad_position_id" gorm:"index;comment:广告位置ID;column:ad_position_id;" binding:"required"`  //广告位置
  AdTypeId  *int `json:"ad_type_id" form:"ad_type_id" gorm:"index;comment:广告类型ID;column:ad_type_id;" binding:"required"`  //广告类型
  AdActionId  *int `json:"ad_action_id" form:"ad_action_id" gorm:"index;comment:广告行为ID;column:ad_action_id;" binding:"required"`  //广告行为
  AppId  *int `json:"app_id" form:"app_id" gorm:"index;default:0;comment:指定APP的价格，0表示通用价格;column:app_id;"`  //APP应用
  BidType  *string `json:"bid_type" form:"bid_type" gorm:"default:CPC;comment:计费方式：CPC,CPM,CPA;column:bid_type;size:10;" binding:"required"`  //计费方式
  BasePrice  *float64 `json:"base_price" form:"base_price" gorm:"default:0;comment:基础起始价格;column:base_price;" binding:"required"`  //基础价格
  MinPrice  *float64 `json:"min_price" form:"min_price" gorm:"default:0;comment:最低竞价价格;column:min_price;"`  //最低价格
  MaxPrice  *float64 `json:"max_price" form:"max_price" gorm:"default:0;comment:最高竞价价格;column:max_price;"`  //最高价格
  EffectiveTime  *time.Time `json:"effective_time" form:"effective_time" gorm:"comment:价格生效时间;column:effective_time;"`  //生效时间
  ExpireTime  *time.Time `json:"expire_time" form:"expire_time" gorm:"comment:价格过期时间;column:expire_time;"`  //过期时间
  Status  *bool `json:"status" form:"status" gorm:"default:true;comment:价格状态：true-启用，false-禁用;column:status;"`  //状态
  Remark  *string `json:"remark" form:"remark" gorm:"comment:价格设置备注;column:remark;size:200;"`  //备注
}


// TableName 广告价格管理 AdPrice自定义表名 meta_ad_prices
func (AdPrice) TableName() string {
    return "meta_ad_prices"
}





