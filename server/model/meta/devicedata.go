
// 自动生成模板DeviceData
package meta
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"time"
)

// 设备数据 结构体  DeviceData
type DeviceData struct {
    global.GVA_MODEL
  DeviceId  *string `json:"device_id" form:"device_id" gorm:"uniqueIndex;comment:设备唯一标识;column:device_id;size:100;" binding:"required"`  //设备ID
  AppId  *int `json:"app_id" form:"app_id" gorm:"index;comment:设备所属APP ID;column:app_id;" binding:"required"`  //APP ID
  UserId  *string `json:"user_id" form:"user_id" gorm:"index;comment:设备关联的用户ID;column:user_id;size:100;"`  //用户ID
  Platform  *string `json:"platform" form:"platform" gorm:"comment:设备平台：android,ios,web;column:platform;size:20;" binding:"required"`  //平台
  DeviceType  *string `json:"device_type" form:"device_type" gorm:"comment:设备类型：phone,tablet,desktop;column:device_type;size:20;"`  //设备类型
  DeviceBrand  *string `json:"device_brand" form:"device_brand" gorm:"comment:设备品牌：Apple,Samsung,Huawei等;column:device_brand;size:50;"`  //设备品牌
  DeviceModel  *string `json:"device_model" form:"device_model" gorm:"comment:设备具体型号;column:device_model;size:100;"`  //设备型号
  OsVersion  *string `json:"os_version" form:"os_version" gorm:"comment:操作系统版本;column:os_version;size:50;"`  //系统版本
  ScreenWidth  *int `json:"screen_width" form:"screen_width" gorm:"default:0;comment:屏幕宽度像素;column:screen_width;"`  //屏幕宽度
  ScreenHeight  *int `json:"screen_height" form:"screen_height" gorm:"default:0;comment:屏幕高度像素;column:screen_height;"`  //屏幕高度
  Language  *string `json:"language" form:"language" gorm:"comment:系统语言设置;column:language;size:20;"`  //系统语言
  Country  *string `json:"country" form:"country" gorm:"comment:设备所在国家;column:country;size:50;"`  //国家
  Province  *string `json:"province" form:"province" gorm:"comment:设备所在省份;column:province;size:50;"`  //省份
  City  *string `json:"city" form:"city" gorm:"comment:设备所在城市;column:city;size:50;"`  //城市
  NetworkType  *string `json:"network_type" form:"network_type" gorm:"comment:网络类型：wifi,4g,5g,3g,2g;column:network_type;size:20;"`  //网络类型
  Carrier  *string `json:"carrier" form:"carrier" gorm:"comment:移动运营商;column:carrier;size:50;"`  //运营商
  FirstSeenTime  *time.Time `json:"first_seen_time" form:"first_seen_time" gorm:"comment:设备首次出现时间;column:first_seen_time;" binding:"required"`  //首次出现时间
  LastSeenTime  *time.Time `json:"last_seen_time" form:"last_seen_time" gorm:"comment:设备最后出现时间;column:last_seen_time;"`  //最后出现时间
  IsActive  *bool `json:"is_active" form:"is_active" gorm:"default:true;comment:设备是否活跃;column:is_active;"`  //是否活跃
}


// TableName 设备数据 DeviceData自定义表名 meta_device_data
func (DeviceData) TableName() string {
    return "meta_device_data"
}





