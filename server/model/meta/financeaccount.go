
// 自动生成模板FinanceAccount
package meta
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"time"
)

// 财务账户 结构体  FinanceAccount
type FinanceAccount struct {
    global.GVA_MODEL
  UserId  *int `json:"user_id" form:"user_id" gorm:"uniqueIndex;comment:账户所属用户ID;column:user_id;" binding:"required"`  //用户ID
  AccountType  *string `json:"account_type" form:"account_type" gorm:"default:advertiser;comment:账户类型：advertiser-广告主,developer-开发者;column:account_type;size:20;" binding:"required"`  //账户类型
  Balance  *float64 `json:"balance" form:"balance" gorm:"default:0;comment:账户当前余额;column:balance;"`  //账户余额
  FrozenAmount  *float64 `json:"frozen_amount" form:"frozen_amount" gorm:"default:0;comment:冻结金额;column:frozen_amount;"`  //冻结金额
  TotalRecharge  *float64 `json:"total_recharge" form:"total_recharge" gorm:"default:0;comment:累计充值金额;column:total_recharge;"`  //累计充值
  TotalConsume  *float64 `json:"total_consume" form:"total_consume" gorm:"default:0;comment:累计消费金额;column:total_consume;"`  //累计消费
  TotalEarning  *float64 `json:"total_earning" form:"total_earning" gorm:"default:0;comment:累计收入金额（开发者）;column:total_earning;"`  //累计收入
  CreditLimit  *float64 `json:"credit_limit" form:"credit_limit" gorm:"default:0;comment:信用额度;column:credit_limit;"`  //信用额度
  Status  *string `json:"status" form:"status" gorm:"default:active;comment:账户状态：active-正常,frozen-冻结,closed-关闭;column:status;size:20;"`  //账户状态
  Currency  *string `json:"currency" form:"currency" gorm:"default:CNY;comment:货币类型：CNY,USD,EUR等;column:currency;size:10;"`  //货币类型
  LastTransactionTime  *time.Time `json:"last_transaction_time" form:"last_transaction_time" gorm:"comment:最后一次交易时间;column:last_transaction_time;"`  //最后交易时间
}


// TableName 财务账户 FinanceAccount自定义表名 meta_finance_accounts
func (FinanceAccount) TableName() string {
    return "meta_finance_accounts"
}





