
// 自动生成模板App
package meta
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/datatypes"
)

// 应用管理 结构体  App
type App struct {
    global.GVA_MODEL
  Name  *string `json:"name" form:"name" gorm:"comment:APP应用名称;column:name;size:100;" binding:"required"`  //应用名称
  PackageId  *string `json:"package_id" form:"package_id" gorm:"uniqueIndex;comment:APP包名或Bundle ID;column:package_id;size:100;" binding:"required"`  //包名
  Platform  *string `json:"platform" form:"platform" gorm:"default:android;comment:应用平台：android,ios,web;column:platform;size:20;"`  //平台
  UserId  *int `json:"user_id" form:"user_id" gorm:"index;comment:APP开发者用户ID;column:user_id;"`  //开发者ID
  AdPositions  datatypes.JSON `json:"ad_positions" form:"ad_positions" gorm:"comment:APP支持的广告位置ID列表;column:ad_positions;" swaggertype:"array,object"`  //广告位
  Status  *bool `json:"status" form:"status" gorm:"default:true;comment:应用上架状态;column:status;size:2;"`  //是否上架
  AdEnabled  *bool `json:"ad_enabled" form:"ad_enabled" gorm:"default:true;comment:是否允许显示广告;column:ad_enabled;"`  //允许广告
  Description  *string `json:"description" form:"description" gorm:"comment:应用描述信息;column:description;size:500;"`  //应用描述
  Icon  string `json:"icon" form:"icon" gorm:"comment:应用图标URL;column:icon;"`  //应用图标
}


// TableName 应用管理 App自定义表名 meta_apps
func (App) TableName() string {
    return "meta_apps"
}





