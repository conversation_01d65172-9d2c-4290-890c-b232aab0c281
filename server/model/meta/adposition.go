
// 自动生成模板AdPosition
package meta
import (
)

// 广告位置管理 结构体  AdPosition
type AdPosition struct {
  ID  *int `json:"id" form:"id" gorm:"primarykey;comment:ID;column:id;size:20;" binding:"required"`  //ID
  Name  *string `json:"name" form:"name" gorm:"comment:广告位置名称;column:name;size:50;" binding:"required"`  //位置名称
  Description  *string `json:"description" form:"description" gorm:"comment:广告位置描述信息;column:description;size:200;"`  //位置描述
}


// TableName 广告位置管理 AdPosition自定义表名 meta_ad_positions
func (AdPosition) TableName() string {
    return "meta_ad_positions"
}





