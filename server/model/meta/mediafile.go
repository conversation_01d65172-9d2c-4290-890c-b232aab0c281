
// 自动生成模板MediaFile
package meta
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// 媒体文件 结构体  MediaFile
type MediaFile struct {
    global.GVA_MODEL
  Name  *string `json:"name" form:"name" gorm:"comment:媒体文件名称;column:name;size:200;" binding:"required"`  //文件名称
  OriginalName  *string `json:"original_name" form:"original_name" gorm:"comment:原始文件名;column:original_name;size:200;" binding:"required"`  //原始文件名
  UserId  *int `json:"user_id" form:"user_id" gorm:"index;comment:上传用户ID;column:user_id;" binding:"required"`  //上传用户
  FileType  *string `json:"file_type" form:"file_type" gorm:"default:image;comment:文件类型：image,video,audio;column:file_type;size:20;" binding:"required"`  //文件类型
  MimeType  *string `json:"mime_type" form:"mime_type" gorm:"comment:文件MIME类型;column:mime_type;size:50;" binding:"required"`  //MIME类型
  FileSize  *int `json:"file_size" form:"file_size" gorm:"default:0;comment:文件大小（字节）;column:file_size;" binding:"required"`  //文件大小
  FilePath  *string `json:"file_path" form:"file_path" gorm:"comment:文件存储路径;column:file_path;size:500;" binding:"required"`  //文件路径
  FileUrl  *string `json:"file_url" form:"file_url" gorm:"comment:文件访问URL;column:file_url;size:500;" binding:"required"`  //访问URL
  ThumbnailUrl  *string `json:"thumbnail_url" form:"thumbnail_url" gorm:"comment:缩略图URL;column:thumbnail_url;size:500;"`  //缩略图URL
  Width  *int `json:"width" form:"width" gorm:"default:0;comment:图片/视频宽度;column:width;"`  //宽度
  Height  *int `json:"height" form:"height" gorm:"default:0;comment:图片/视频高度;column:height;"`  //高度
  Duration  *int `json:"duration" form:"duration" gorm:"default:0;comment:视频时长（秒）;column:duration;"`  //时长
  Md5Hash  *string `json:"md5_hash" form:"md5_hash" gorm:"index;comment:文件MD5哈希值;column:md5_hash;size:32;"`  //MD5哈希
  Tags  *string `json:"tags" form:"tags" gorm:"comment:文件标签，逗号分隔;column:tags;size:500;"`  //标签
  Status  *string `json:"status" form:"status" gorm:"default:active;comment:文件状态：uploading-上传中,processing-处理中,active-正常,deleted-已删除,error-错误;column:status;size:20;"`  //状态
}


// TableName 媒体文件 MediaFile自定义表名 meta_media_files
func (MediaFile) TableName() string {
    return "meta_media_files"
}





