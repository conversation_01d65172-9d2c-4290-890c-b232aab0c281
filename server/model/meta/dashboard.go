package meta

// Dashboard项目配置
type DashboardItem struct {
	ID          string      `json:"id"`          // 项目ID
	Title       string      `json:"title"`       // 项目标题
	Type        string      `json:"type"`        // 图表类型：card, line_chart, bar_chart, pie_chart
	ColSpan     int         `json:"colSpan"`     // 占用列数（1-4）
	Value       interface{} `json:"value"`       // 数值
	Unit        string      `json:"unit"`        // 单位
	Trend       *Trend      `json:"trend"`       // 趋势信息
	ChartData   interface{} `json:"chartData"`   // 图表数据
	Description string      `json:"description"` // 描述信息
}

// 趋势信息
type Trend struct {
	Value     float64 `json:"value"`     // 趋势值
	IsUp      bool    `json:"isUp"`      // 是否上升
	Percent   float64 `json:"percent"`   // 百分比
	TimeRange string  `json:"timeRange"` // 时间范围
}

// Dashboard响应结构
type DashboardResponse struct {
	Items []DashboardItem `json:"items"`
}

// Dashboard项目ID常量
const (
	// 用户相关
	ITEM_DAILY_ACTIVE_USERS   = "daily_active_users"   // 日活用户
	ITEM_WEEKLY_ACTIVE_USERS  = "weekly_active_users"  // 周活用户
	ITEM_MONTHLY_ACTIVE_USERS = "monthly_active_users" // 月活用户
	ITEM_TOTAL_USERS          = "total_users"          // 总用户数
	ITEM_NEW_USERS            = "new_users"            // 新增用户数
	ITEM_ACTIVE_USERS         = "active_users"         // 活跃用户数
	ITEM_USER_GROWTH_TREND    = "user_growth_trend"    // 用户增长趋势

	// 广告相关
	ITEM_ACTIVE_ADS        = "active_ads"        // 投放中的广告数量
	ITEM_AD_IMPRESSIONS    = "ad_impressions"    // 广告展示数
	ITEM_AD_CLICKS         = "ad_clicks"         // 广告点击数
	ITEM_AD_CTR            = "ad_ctr"            // 广告点击率
	ITEM_AD_CONVERSIONS    = "ad_conversions"    // 广告转化数
	ITEM_AD_CVR            = "ad_cvr"            // 广告转化率

	// 财务相关
	ITEM_ACCOUNT_BALANCE    = "account_balance"    // 账户余额
	ITEM_DAILY_CONSUME      = "daily_consume"      // 日消费
	ITEM_WEEKLY_CONSUME     = "weekly_consume"     // 周消费
	ITEM_MONTHLY_CONSUME    = "monthly_consume"    // 月消费
	ITEM_DAILY_REVENUE      = "daily_revenue"      // 日收益
	ITEM_WEEKLY_REVENUE     = "weekly_revenue"     // 周收益
	ITEM_MONTHLY_REVENUE    = "monthly_revenue"    // 月收益
	ITEM_TOTAL_RECHARGE     = "total_recharge"     // 累计充值
	ITEM_TOTAL_EARNING      = "total_earning"      // 累计收入

	// 系统相关
	ITEM_SYSTEM_STATUS      = "system_status"      // 系统状态
	ITEM_APP_COUNT          = "app_count"          // 应用数量
	ITEM_CAMPAIGN_COUNT     = "campaign_count"     // 广告计划数量
)

// 角色权限配置
var RolePermissions = map[uint][]string{
	888: { // 系统管理员 - 所有项目
		ITEM_DAILY_ACTIVE_USERS,
		ITEM_WEEKLY_ACTIVE_USERS,
		ITEM_MONTHLY_ACTIVE_USERS,
		ITEM_TOTAL_USERS,
		ITEM_NEW_USERS,
		ITEM_ACTIVE_USERS,
		ITEM_USER_GROWTH_TREND,
		ITEM_ACTIVE_ADS,
		ITEM_AD_IMPRESSIONS,
		ITEM_AD_CLICKS,
		ITEM_AD_CTR,
		ITEM_AD_CONVERSIONS,
		ITEM_AD_CVR,
		ITEM_ACCOUNT_BALANCE,
		ITEM_DAILY_CONSUME,
		ITEM_WEEKLY_CONSUME,
		ITEM_MONTHLY_CONSUME,
		ITEM_DAILY_REVENUE,
		ITEM_WEEKLY_REVENUE,
		ITEM_MONTHLY_REVENUE,
		ITEM_TOTAL_RECHARGE,
		ITEM_TOTAL_EARNING,
		ITEM_SYSTEM_STATUS,
		ITEM_APP_COUNT,
		ITEM_CAMPAIGN_COUNT,
	},
	9528: { // 广告主
		ITEM_ACTIVE_ADS,
		ITEM_AD_IMPRESSIONS,
		ITEM_AD_CLICKS,
		ITEM_ACCOUNT_BALANCE,
		ITEM_DAILY_CONSUME,
		ITEM_WEEKLY_CONSUME,
		ITEM_MONTHLY_CONSUME,
	},
	8881: { // APP开发者
		ITEM_AD_IMPRESSIONS,
		ITEM_AD_CLICKS,
		ITEM_DAILY_REVENUE,
		ITEM_WEEKLY_REVENUE,
		ITEM_MONTHLY_REVENUE,
		ITEM_DAILY_ACTIVE_USERS,
		ITEM_WEEKLY_ACTIVE_USERS,
		ITEM_MONTHLY_ACTIVE_USERS,
		ITEM_TOTAL_USERS,
	},
}

// 项目配置模板
var ItemTemplates = map[string]DashboardItem{
	ITEM_DAILY_ACTIVE_USERS: {
		ID:          ITEM_DAILY_ACTIVE_USERS,
		Title:       "日活用户",
		Type:        "card",
		ColSpan:     1,
		Unit:        "人",
		Description: "今日活跃用户数量",
	},
	ITEM_WEEKLY_ACTIVE_USERS: {
		ID:          ITEM_WEEKLY_ACTIVE_USERS,
		Title:       "周活用户",
		Type:        "card",
		ColSpan:     1,
		Unit:        "人",
		Description: "本周活跃用户数量",
	},
	ITEM_MONTHLY_ACTIVE_USERS: {
		ID:          ITEM_MONTHLY_ACTIVE_USERS,
		Title:       "月活用户",
		Type:        "card",
		ColSpan:     1,
		Unit:        "人",
		Description: "本月活跃用户数量",
	},
	ITEM_TOTAL_USERS: {
		ID:          ITEM_TOTAL_USERS,
		Title:       "总用户数",
		Type:        "card",
		ColSpan:     1,
		Unit:        "人",
		Description: "累计注册用户数量",
	},
	ITEM_NEW_USERS: {
		ID:          ITEM_NEW_USERS,
		Title:       "新增用户",
		Type:        "card",
		ColSpan:     1,
		Unit:        "人",
		Description: "今日新增用户数量",
	},
	ITEM_ACTIVE_USERS: {
		ID:          ITEM_ACTIVE_USERS,
		Title:       "活跃用户",
		Type:        "card",
		ColSpan:     1,
		Unit:        "人",
		Description: "当前活跃用户数量",
	},
	ITEM_USER_GROWTH_TREND: {
		ID:          ITEM_USER_GROWTH_TREND,
		Title:       "用户增长趋势",
		Type:        "line_chart",
		ColSpan:     4,
		Unit:        "人",
		Description: "用户增长趋势图表",
	},
	ITEM_ACTIVE_ADS: {
		ID:          ITEM_ACTIVE_ADS,
		Title:       "投放中广告",
		Type:        "card",
		ColSpan:     1,
		Unit:        "个",
		Description: "当前投放中的广告数量",
	},
	ITEM_AD_IMPRESSIONS: {
		ID:          ITEM_AD_IMPRESSIONS,
		Title:       "广告展示数",
		Type:        "card",
		ColSpan:     1,
		Unit:        "次",
		Description: "广告展示总次数",
	},
	ITEM_AD_CLICKS: {
		ID:          ITEM_AD_CLICKS,
		Title:       "广告点击数",
		Type:        "card",
		ColSpan:     1,
		Unit:        "次",
		Description: "广告点击总次数",
	},
	ITEM_ACCOUNT_BALANCE: {
		ID:          ITEM_ACCOUNT_BALANCE,
		Title:       "账户余额",
		Type:        "card",
		ColSpan:     1,
		Unit:        "元",
		Description: "当前账户余额",
	},
	ITEM_DAILY_CONSUME: {
		ID:          ITEM_DAILY_CONSUME,
		Title:       "日消费",
		Type:        "card",
		ColSpan:     1,
		Unit:        "元",
		Description: "今日消费金额",
	},
	ITEM_WEEKLY_CONSUME: {
		ID:          ITEM_WEEKLY_CONSUME,
		Title:       "周消费",
		Type:        "card",
		ColSpan:     1,
		Unit:        "元",
		Description: "本周消费金额",
	},
	ITEM_MONTHLY_CONSUME: {
		ID:          ITEM_MONTHLY_CONSUME,
		Title:       "月消费",
		Type:        "card",
		ColSpan:     1,
		Unit:        "元",
		Description: "本月消费金额",
	},
	ITEM_DAILY_REVENUE: {
		ID:          ITEM_DAILY_REVENUE,
		Title:       "日收益",
		Type:        "card",
		ColSpan:     1,
		Unit:        "元",
		Description: "今日收益金额",
	},
	ITEM_WEEKLY_REVENUE: {
		ID:          ITEM_WEEKLY_REVENUE,
		Title:       "周收益",
		Type:        "card",
		ColSpan:     1,
		Unit:        "元",
		Description: "本周收益金额",
	},
	ITEM_MONTHLY_REVENUE: {
		ID:          ITEM_MONTHLY_REVENUE,
		Title:       "月收益",
		Type:        "card",
		ColSpan:     1,
		Unit:        "元",
		Description: "本月收益金额",
	},
}
