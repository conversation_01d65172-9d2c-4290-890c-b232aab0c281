package response

// GetAdsResponse 获取广告列表响应
type GetAdsResponse struct {
	Ads []AdInfo `json:"ads"` // 广告列表
}

// AdInfo 广告信息
type AdInfo struct {
	ID          uint    `json:"id"`           // 广告ID
	CampaignId  int     `json:"campaign_id"`  // 广告投放计划ID
	Title       string  `json:"title"`        // 广告标题
	Description string  `json:"description"`  // 广告描述
	CoverUrl    string  `json:"cover"`        // 封面文件URL
	MediaUrl    string  `json:"media"`        // 媒体文件URL
	ClickUrl    string  `json:"url"`          // 点击链接
	AdTypeId    int     `json:"ad_type_id"`   // 广告类型ID
	AdActionId  int     `json:"ad_action_id"` // 广告行为ID
	BidAmount   float64 `json:"-"`            // 出价金额
	BidType     string  `json:"-"`            // 计费方式
	// Name        string `json:"name"`         // 广告名称
	//CreatedAt   time.Time `json:"created_at"`   // 创建时间
	//AdPositionId int       `json:"ad_position_id"` // 广告位ID
}

// AdEventResponse 广告事件上报响应
type AdEventResponse struct {
	Success        bool              `json:"success"`                 // 是否成功
	Message        string            `json:"message"`                 // 响应消息
	ProcessedCount int               `json:"processed_count"`         // 处理的事件数量
	FailedEvents   []FailedEventInfo `json:"failed_events,omitempty"` // 失败的事件信息
}

// FailedEventInfo 失败事件信息
type FailedEventInfo struct {
	Index      int    `json:"index"`       // 事件在请求列表中的索引
	Reason     string `json:"reason"`      // 失败原因
	AdId       int    `json:"ad_id"`       // 广告ID
	CampaignId int    `json:"campaign_id"` // 投放计划ID
}
