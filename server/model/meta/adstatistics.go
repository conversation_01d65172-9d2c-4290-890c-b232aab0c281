
// 自动生成模板AdStatistics
package meta
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"time"
)

// 广告统计 结构体  AdStatistics
type AdStatistics struct {
    global.GVA_MODEL
  StatDate  *time.Time `json:"stat_date" form:"stat_date" gorm:"index;comment:统计日期;column:stat_date;" binding:"required"`  //统计日期
  AdId  *int `json:"ad_id" form:"ad_id" gorm:"index;default:0;comment:广告，0表示汇总统计;column:ad_id;"`  //广告名称
  CampaignId  *int `json:"campaign_id" form:"campaign_id" gorm:"index;default:0;comment:广告计划ID，0表示汇总统计;column:campaign_id;"`  //计划ID
  AppId  *int `json:"app_id" form:"app_id" gorm:"index;default:0;comment:APP ID，0表示汇总统计;column:app_id;"`  //APP ID
  UserId  *int `json:"user_id" form:"user_id" gorm:"index;default:0;comment:用户ID，0表示汇总统计;column:user_id;"`  //用户ID
  AdPositionId  *int `json:"ad_position_id" form:"ad_position_id" gorm:"index;default:0;comment:广告位ID，0表示汇总统计;column:ad_position_id;"`  //广告位ID
  Impressions  *int `json:"impressions" form:"impressions" gorm:"default:0;comment:广告展示次数;column:impressions;"`  //展示次数
  Clicks  *int `json:"clicks" form:"clicks" gorm:"default:0;comment:广告点击次数;column:clicks;"`  //点击次数
  Conversions  *int `json:"conversions" form:"conversions" gorm:"default:0;comment:广告转化次数;column:conversions;"`  //转化次数
  UniqueUsers  *int `json:"unique_users" form:"unique_users" gorm:"default:0;comment:独立用户数;column:unique_users;"`  //独立用户数
  Revenue  *float64 `json:"revenue" form:"revenue" gorm:"default:0;comment:广告收入金额;column:revenue;"`  //收入金额
  Cost  *float64 `json:"cost" form:"cost" gorm:"default:0;comment:广告成本金额;column:cost;"`  //成本金额
  Ctr  *float64 `json:"ctr" form:"ctr" gorm:"default:0;comment:点击率（%）;column:ctr;"`  //点击率
  Cvr  *float64 `json:"cvr" form:"cvr" gorm:"default:0;comment:转化率（%）;column:cvr;"`  //转化率
  Ecpm  *float64 `json:"ecpm" form:"ecpm" gorm:"default:0;comment:千次展示收入;column:ecpm;"`  //千次展示收入
  Cpc  *float64 `json:"cpc" form:"cpc" gorm:"default:0;comment:平均点击成本;column:cpc;"`  //平均点击成本
  Cpa  *float64 `json:"cpa" form:"cpa" gorm:"default:0;comment:平均转化成本;column:cpa;"`  //平均转化成本
}


// TableName 广告统计 AdStatistics自定义表名 meta_ad_statistics
func (AdStatistics) TableName() string {
    return "meta_ad_statistics"
}





