package request

import (
	"time"
)

// AdWizardRequest 广告向导请求结构体
type AdWizardRequest struct {
	// 第一步：广告目标
	Objective string `json:"objective" binding:"required"` // "brand" | "traffic"
	
	// 第二步：广告类型和素材
	AdTypeId  int    `json:"ad_type_id" binding:"required"`
	MediaUrl  string `json:"media_url" binding:"required"`
	
	// 第三步：基础信息
	Name        string `json:"name" binding:"required"`
	Title       string `json:"title" binding:"required"`
	Description string `json:"description" binding:"required"`
	ClickUrl    string `json:"click_url" binding:"required"`
	
	// 第四步：预算设置
	BidAmount    float64 `json:"bid_amount" binding:"required"`
	TotalBudget  float64 `json:"total_budget" binding:"required"`
	DailyBudget  float64 `json:"daily_budget"`
	
	// 可选参数
	AdPositions  []int     `json:"ad_positions"`  // 广告位置ID列表
	StartTime    time.Time `json:"start_time"`    // 开始时间，默认为当前时间
	EndTime      time.Time `json:"end_time"`      // 结束时间，默认为30天后
	MaxImpressions int     `json:"max_impressions"` // 最大展示次数
	MaxClicks      int     `json:"max_clicks"`      // 最大点击次数
}

// AdWizardResponse 广告向导响应结构体
type AdWizardResponse struct {
	AdId       uint `json:"ad_id"`       // 创建的广告ID
	CampaignId uint `json:"campaign_id"` // 创建的投放计划ID
	Message    string `json:"message"`   // 成功消息
}
