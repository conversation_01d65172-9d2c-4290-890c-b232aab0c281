package request

// GetAdsRequest 获取广告列表请求参数
type GetAdsRequest struct {
	PackageId    string `json:"-"`                                                       // 当前包名
	AdPositionId int    `json:"ad_position_id" form:"ad_position_id" binding:"required"` // 广告位ID
	AdTypeId     int    `json:"ad_type_id" form:"ad_type_id" binding:"required"`         // 广告类型ID
}

// DeviceInfo 设备信息
//type DeviceInfo struct {
//	DeviceId     string `json:"device_id,omitempty"`     // 设备ID
//	Platform     string `json:"platform,omitempty"`      // 平台：android,ios,web
//	OsVersion    string `json:"os_version,omitempty"`    // 系统版本
//	AppVersion   string `json:"app_version,omitempty"`   // 应用版本
//	NetworkType  string `json:"network_type,omitempty"`  // 网络类型
//	Country      string `json:"country,omitempty"`       // 国家
//	Province     string `json:"province,omitempty"`      // 省份
//	City         string `json:"city,omitempty"`          // 城市
//	ScreenWidth  int    `json:"screen_width,omitempty"`  // 屏幕宽度
//	ScreenHeight int    `json:"screen_height,omitempty"` // 屏幕高度
//}

// AdEventRequest 广告事件上报请求参数
type AdEventRequest struct {
	PackageId string    `json:"-"`                                            // 当前包名
	Events    []AdEvent `json:"events" form:"events" binding:"required,dive"` // 事件列表
}

// AdEvent 广告事件
type AdEvent struct {
	ActionType   int    `json:"action_type" form:"action_type" binding:"required"`       // 行为类型ID（1展示、2点击...）
	CampaignId   int    `json:"campaign_id" form:"campaign_id" binding:"required"`       // 广告投放计划ID
	AdId         int    `json:"ad_id" form:"ad_id" binding:"required"`                   // 广告ID
	AdPositionId int    `json:"ad_position_id" form:"ad_position_id" binding:"required"` // 广告位ID
	EventTime    uint32 `json:"event_time,omitempty" form:"event_time"`                  // unix时间戳（可选，默认当前时间）
}
