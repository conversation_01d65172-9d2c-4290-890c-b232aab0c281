
// 自动生成模板Dashub
package meta
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// 统计汇总 结构体  Dashub
type Dashub struct {
    global.GVA_MODEL
  Day  *string `json:"day" form:"day" gorm:"index;comment:统计数据的日期;column:day;size:50;" binding:"required"`  //统计日期
  Kind  *int `json:"kind" form:"kind" gorm:"index;comment:统计数据类型;column:kind;" binding:"required"`  //数据类型
  TargetType  *int `json:"target_type" form:"target_type" gorm:"index;default:0;comment:统计目标类型;column:target_type;"`  //目标类型
  Target  *int `json:"target" form:"target" gorm:"index;default:0;comment:统计目标ID;column:target;" binding:"required"`  //统计目标
  Nums  *float64 `json:"nums" form:"nums" gorm:"default:0;comment:统计数值;column:nums;" binding:"required"`  //数值
}


// TableName 统计汇总 Dashub自定义表名 meta_dashubs
func (Dashub) TableName() string {
    return "meta_dashubs"
}





