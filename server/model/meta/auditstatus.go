
// 自动生成模板AuditStatus
package meta
import (
)

// 审核状态配置 结构体  AuditStatus
type AuditStatus struct {
  ID  *int `json:"id" form:"id" gorm:"primarykey;comment:ID;column:id;size:20;" binding:"required"`  //ID
  Name  *string `json:"name" form:"name" gorm:"comment:审核状态代码;column:name;size:50;" binding:"required"`  //状态代码
  Description  *string `json:"description" form:"description" gorm:"comment:状态描述信息;column:description;size:200;"`  //描述
}


// TableName 审核状态配置 AuditStatus自定义表名 meta_audit_status
func (AuditStatus) TableName() string {
    return "meta_audit_status"
}





