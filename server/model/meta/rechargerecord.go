
// 自动生成模板RechargeRecord
package meta
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"time"
)

// 充值记录 结构体  RechargeRecord
type RechargeRecord struct {
    global.GVA_MODEL
  UserId  *int `json:"user_id" form:"user_id" gorm:"index;comment:充值用户;column:user_id;"`  //用户
  OrderNo  *string `json:"order_no" form:"order_no" gorm:"uniqueIndex;comment:充值订单号;column:order_no;size:50;" binding:"required"`  //订单号
  Amount  *float64 `json:"amount" form:"amount" gorm:"default:0;comment:充值金额;column:amount;" binding:"required"`  //充值金额
  ActualAmount  *float64 `json:"actual_amount" form:"actual_amount" gorm:"default:0;comment:实际到账金额;column:actual_amount;"`  //实际到账
  Currency  *string `json:"currency" form:"currency" gorm:"default:CNY;comment:货币类型：CNY,USD,EUR等;column:currency;size:10;"`  //货币类型
  PaymentMethod  *string `json:"payment_method" form:"payment_method" gorm:"default:alipay;comment:支付方式：alipay,wechat,bank,paypal等;column:payment_method;size:20;" binding:"required"`  //支付方式
  PaymentOrderNo  *string `json:"payment_order_no" form:"payment_order_no" gorm:"comment:第三方支付订单号;column:payment_order_no;size:100;"`  //支付订单号
  Status  *string `json:"status" form:"status" gorm:"default:pending;comment:充值状态：pending-待支付,processing-处理中,success-成功,failed-失败,cancelled-已取消;column:status;size:20;"`  //状态
  RequestTime  *time.Time `json:"request_time" form:"request_time" gorm:"comment:充值申请时间;column:request_time;" binding:"required"`  //申请时间
  PaymentTime  *time.Time `json:"payment_time" form:"payment_time" gorm:"comment:支付完成时间;column:payment_time;"`  //支付时间
  CompletedTime  *time.Time `json:"completed_time" form:"completed_time" gorm:"comment:充值完成时间;column:completed_time;"`  //完成时间
  FailureReason  *string `json:"failure_reason" form:"failure_reason" gorm:"comment:充值失败原因;column:failure_reason;size:200;"`  //失败原因
  Remark  *string `json:"remark" form:"remark" gorm:"comment:充值备注信息;column:remark;size:200;"`  //备注
}


// TableName 充值记录 RechargeRecord自定义表名 meta_recharge_records
func (RechargeRecord) TableName() string {
    return "meta_recharge_records"
}





