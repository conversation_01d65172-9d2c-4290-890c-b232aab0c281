
// 自动生成模板Ad
package meta
import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"time"
	"gorm.io/datatypes"
)

// 广告管理 结构体  Ad
type Ad struct {
    global.GVA_MODEL
  Name  *string `json:"name" form:"name" gorm:"comment:广告名称;column:name;size:100;" binding:"required"`  //名称
  UserId  *int `json:"user_id" form:"user_id" gorm:"index;comment:广告主用户ID;column:user_id;"`  //广告主
  AdTypeId  *int `json:"ad_type_id" form:"ad_type_id" gorm:"index;comment:广告类型ID;column:ad_type_id;" binding:"required"`  //类型
  AdActionId  *int `json:"ad_action_id" form:"ad_action_id" gorm:"index;comment:广告行为ID;column:ad_action_id;" binding:"required"`  //广告行为
  Status  *int `json:"status" form:"status" gorm:"default:1;comment:广告状态：draft-草稿,pending-待审核,approved-已通过,rejected-已拒绝,published-已上架,unpublished-已下架;column:status;size:10;"`  //状态
  Title  *string `json:"title" form:"title" gorm:"comment:广告显示标题;column:title;size:200;"`  //广告标题
  Description  *string `json:"description" form:"description" gorm:"comment:广告描述信息;column:description;size:500;"`  //广告描述
  AdPositions  datatypes.JSON `json:"ad_positions" form:"ad_positions" gorm:"comment:投放广告位置ID列表;column:ad_positions;" swaggertype:"array,object"`  //广告位置
  MediaUrl  string `json:"media_url" form:"media_url" gorm:"comment:广告媒体文件;column:media_url;size:500;"`  //媒体文件
  ClickUrl  *string `json:"click_url" form:"click_url" gorm:"comment:点击后跳转的链接;column:click_url;size:500;"`  //点击链接
  ReviewNote  *string `json:"review_note" form:"review_note" gorm:"comment:审核备注信息;column:review_note;size:500;"`  //审核备注
  ReviewedAt  *time.Time `json:"reviewed_at" form:"reviewed_at" gorm:"comment:审核时间;column:reviewed_at;"`  //审核时间
  ReviewerId  *int `json:"reviewer_id" form:"reviewer_id" gorm:"comment:审核人用户ID;column:reviewer_id;"`  //审核人
}


// TableName 广告管理 Ad自定义表名 meta_ads
func (Ad) TableName() string {
    return "meta_ads"
}





