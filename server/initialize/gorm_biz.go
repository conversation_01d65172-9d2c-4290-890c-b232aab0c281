package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
)

func bizModel() error {
	db := global.GVA_DB
	err := db.AutoMigrate(meta.ConsumeRecord{}, meta.AdReview{}, meta.DeviceData{}, meta.Campaign{}, meta.FinanceAccount{}, meta.AdStatistics{}, meta.AdAction{}, meta.AdPosition{}, meta.AdClick{}, meta.AdImpression{}, meta.RechargeRecord{}, meta.Ad{}, meta.MediaFile{}, meta.AdPrice{}, meta.App{}, meta.AdType{}, meta.AdType{}, meta.AdType{}, meta.AuditStatus{}, meta.Dashub{})
	if err != nil {
		return err
	}
	return nil
}
