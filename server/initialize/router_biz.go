package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/router"
	"github.com/gin-gonic/gin"
)

func holder(routers ...*gin.RouterGroup) {
	_ = routers
	_ = router.RouterGroupApp
}
func initBizRouter(routers ...*gin.RouterGroup) {
	privateGroup := routers[0]
	publicGroup := routers[1]
	holder(publicGroup, privateGroup)
	{
		metaRouter := router.RouterGroupApp.Meta
		metaRouter.InitConsumeRecordRouter(privateGroup, publicGroup)
		metaRouter.InitAdReviewRouter(privateGroup, publicGroup)
		metaRouter.InitDeviceDataRouter(privateGroup, publicGroup)
		metaRouter.InitCampaignRouter(privateGroup, publicGroup)
		metaRouter.InitFinanceAccountRouter(privateGroup, publicGroup)
		metaRouter.InitAdStatisticsRouter(privateGroup, publicGroup)
		metaRouter.InitAdActionRouter(privateGroup, publicGroup)
		metaRouter.InitAdPositionRouter(privateGroup, publicGroup)
		metaRouter.InitAdClickRouter(privateGroup, publicGroup)
		metaRouter.InitAdImpressionRouter(privateGroup, publicGroup)
		metaRouter.InitRechargeRecordRouter(privateGroup, publicGroup)
		metaRouter.InitAdRouter(privateGroup, publicGroup)
		metaRouter.InitMediaFileRouter(privateGroup, publicGroup)
		metaRouter.InitAdPriceRouter(privateGroup, publicGroup)
		metaRouter.InitAppRouter(privateGroup, publicGroup)
		metaRouter.InitAdTypeRouter(privateGroup, publicGroup)
		metaRouter.InitAuditStatusRouter(privateGroup, publicGroup)
		metaRouter.InitDashboardRouter(privateGroup, publicGroup)
		metaRouter.InitDashubRouter(privateGroup, publicGroup)
		metaRouter.InitDashubDashboardRouter(privateGroup, publicGroup)
		metaRouter.InitSdkRouter(privateGroup, publicGroup)
	}
}
