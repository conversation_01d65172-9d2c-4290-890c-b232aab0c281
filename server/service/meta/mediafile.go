
package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type MediaFileService struct {}
// CreateMediaFile 创建媒体文件记录
// Author [yourname](https://github.com/yourname)
func (mediafileService *MediaFileService) CreateMediaFile(ctx context.Context, mediafile *meta.MediaFile) (err error) {
	err = global.GVA_DB.Create(mediafile).Error
	return err
}

// DeleteMediaFile 删除媒体文件记录
// Author [yourname](https://github.com/yourname)
func (mediafileService *MediaFileService)DeleteMediaFile(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&meta.MediaFile{},"id = ?",ID).Error
	return err
}

// DeleteMediaFileByIds 批量删除媒体文件记录
// Author [yourname](https://github.com/yourname)
func (mediafileService *MediaFileService)DeleteMediaFileByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.MediaFile{},"id in ?",IDs).Error
	return err
}

// UpdateMediaFile 更新媒体文件记录
// Author [yourname](https://github.com/yourname)
func (mediafileService *MediaFileService)UpdateMediaFile(ctx context.Context, mediafile meta.MediaFile) (err error) {
	err = global.GVA_DB.Model(&meta.MediaFile{}).Where("id = ?",mediafile.ID).Updates(&mediafile).Error
	return err
}

// GetMediaFile 根据ID获取媒体文件记录
// Author [yourname](https://github.com/yourname)
func (mediafileService *MediaFileService)GetMediaFile(ctx context.Context, ID string) (mediafile meta.MediaFile, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&mediafile).Error
	return
}
// GetMediaFileInfoList 分页获取媒体文件记录
// Author [yourname](https://github.com/yourname)
func (mediafileService *MediaFileService)GetMediaFileInfoList(ctx context.Context, info metaReq.MediaFileSearch) (list []meta.MediaFile, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&meta.MediaFile{})
    var mediafiles []meta.MediaFile
    // 如果有条件搜索 下方会自动创建搜索语句
    if len(info.CreatedAtRange) == 2 {
     db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
    }
    
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }
        var OrderStr string
        orderMap := make(map[string]bool)
           orderMap["ID"] = true
           orderMap["CreatedAt"] = true
         	orderMap["name"] = true
         	orderMap["file_size"] = true
       if orderMap[info.Sort] {
          OrderStr = info.Sort
          if info.Order == "descending" {
             OrderStr = OrderStr + " desc"
          }
          db = db.Order(OrderStr)
       }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&mediafiles).Error
	return  mediafiles, total, err
}
func (mediafileService *MediaFileService)GetMediaFileDataSource(ctx context.Context) (res map[string][]map[string]any, err error) {
	res = make(map[string][]map[string]any)
	
	   user_id := make([]map[string]any, 0)
	   
       
       global.GVA_DB.Table("sys_users").Where("deleted_at IS NULL").Select("username as label,id as value").Scan(&user_id)
	   res["user_id"] = user_id
	return
}
func (mediafileService *MediaFileService)GetMediaFilePublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
