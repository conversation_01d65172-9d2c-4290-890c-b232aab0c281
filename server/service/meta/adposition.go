
package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type AdPositionService struct {}
// CreateAdPosition 创建广告位置管理记录
// Author [yourname](https://github.com/yourname)
func (adpositionService *AdPositionService) CreateAdPosition(ctx context.Context, adposition *meta.AdPosition) (err error) {
	err = global.GVA_DB.Create(adposition).Error
	return err
}

// DeleteAdPosition 删除广告位置管理记录
// Author [yourname](https://github.com/yourname)
func (adpositionService *AdPositionService)DeleteAdPosition(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&meta.AdPosition{},"id = ?",id).Error
	return err
}

// DeleteAdPositionByIds 批量删除广告位置管理记录
// Author [yourname](https://github.com/yourname)
func (adpositionService *AdPositionService)DeleteAdPositionByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.AdPosition{},"id in ?",ids).Error
	return err
}

// UpdateAdPosition 更新广告位置管理记录
// Author [yourname](https://github.com/yourname)
func (adpositionService *AdPositionService)UpdateAdPosition(ctx context.Context, adposition meta.AdPosition) (err error) {
	err = global.GVA_DB.Model(&meta.AdPosition{}).Where("id = ?",adposition.ID).Updates(&adposition).Error
	return err
}

// GetAdPosition 根据id获取广告位置管理记录
// Author [yourname](https://github.com/yourname)
func (adpositionService *AdPositionService)GetAdPosition(ctx context.Context, id string) (adposition meta.AdPosition, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&adposition).Error
	return
}
// GetAdPositionInfoList 分页获取广告位置管理记录
// Author [yourname](https://github.com/yourname)
func (adpositionService *AdPositionService)GetAdPositionInfoList(ctx context.Context, info metaReq.AdPositionSearch) (list []meta.AdPosition, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&meta.AdPosition{})
    var adpositions []meta.AdPosition
    // 如果有条件搜索 下方会自动创建搜索语句
    
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }
        var OrderStr string
        orderMap := make(map[string]bool)
         	orderMap["name"] = true
       if orderMap[info.Sort] {
          OrderStr = info.Sort
          if info.Order == "descending" {
             OrderStr = OrderStr + " desc"
          }
          db = db.Order(OrderStr)
       }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&adpositions).Error
	return  adpositions, total, err
}
func (adpositionService *AdPositionService)GetAdPositionPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
