package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type CampaignService struct{}

// CreateCampaign 创建广告投放记录
// Author [yourname](https://github.com/yourname)
func (campaignService *CampaignService) CreateCampaign(ctx context.Context, campaign *meta.Campaign) (err error) {
	err = global.GVA_DB.Create(campaign).Error
	return err
}

// DeleteCampaign 删除广告投放记录
// Author [yourname](https://github.com/yourname)
func (campaignService *CampaignService) DeleteCampaign(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&meta.Campaign{}, "id = ?", ID).Error
	return err
}

// DeleteCampaignByIds 批量删除广告投放记录
// Author [yourname](https://github.com/yourname)
func (campaignService *CampaignService) DeleteCampaignByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.Campaign{}, "id in ?", IDs).Error
	return err
}

// UpdateCampaign 更新广告投放记录
// Author [yourname](https://github.com/yourname)
func (campaignService *CampaignService) UpdateCampaign(ctx context.Context, campaign meta.Campaign) (err error) {
	err = global.GVA_DB.Model(&meta.Campaign{}).Where("id = ?", campaign.ID).Updates(&campaign).Error
	return err
}

// GetCampaign 根据ID获取广告投放记录
// Author [yourname](https://github.com/yourname)
func (campaignService *CampaignService) GetCampaign(ctx context.Context, ID string) (campaign meta.Campaign, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&campaign).Error
	return
}

// GetCampaignInfoList 分页获取广告投放记录
// Author [yourname](https://github.com/yourname)
func (campaignService *CampaignService) GetCampaignInfoList(ctx context.Context, info metaReq.CampaignSearch) (list []meta.Campaign, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&meta.Campaign{})
	var campaigns []meta.Campaign
	// 如果有条件搜索 下方会自动创建搜索语句
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["ID"] = true
	orderMap["CreatedAt"] = true
	orderMap["name"] = true
	orderMap["start_time"] = true
	orderMap["end_time"] = true
	orderMap["total_budget"] = true
	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc"
		}
		db = db.Order(OrderStr)
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&campaigns).Error
	return campaigns, total, err
}
func (campaignService *CampaignService) GetCampaignDataSource(ctx context.Context) (res map[string][]map[string]any, err error) {
	res = make(map[string][]map[string]any)

	ad_ids := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ads").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&ad_ids)
	res["ad_ids"] = ad_ids
	target_apps := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_apps").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&target_apps)
	res["target_apps"] = target_apps
	user_id := make([]map[string]any, 0)

	global.GVA_DB.Table("sys_users").Where("deleted_at IS NULL").Select("username as label,id as value").Scan(&user_id)
	res["user_id"] = user_id
	return
}
func (campaignService *CampaignService) GetCampaignPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

func (campaignService *CampaignService) PauseCampaign(ctx context.Context, info metaReq.CampaignPause) error {
	var status = "running"
	if info.Pause {
		status = "paused"
	}
	return global.GVA_DB.Model(&meta.Campaign{}).
		Where("id in ?", info.Ids).
		Update("status", status).
		Error
}
