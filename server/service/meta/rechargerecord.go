
package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type RechargeRecordService struct {}
// CreateRechargeRecord 创建充值记录记录
// Author [yourname](https://github.com/yourname)
func (rechargerecordService *RechargeRecordService) CreateRechargeRecord(ctx context.Context, rechargerecord *meta.RechargeRecord) (err error) {
	err = global.GVA_DB.Create(rechargerecord).Error
	return err
}

// DeleteRechargeRecord 删除充值记录记录
// Author [yourname](https://github.com/yourname)
func (rechargerecordService *RechargeRecordService)DeleteRechargeRecord(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&meta.RechargeRecord{},"id = ?",ID).Error
	return err
}

// DeleteRechargeRecordByIds 批量删除充值记录记录
// Author [yourname](https://github.com/yourname)
func (rechargerecordService *RechargeRecordService)DeleteRechargeRecordByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.RechargeRecord{},"id in ?",IDs).Error
	return err
}

// UpdateRechargeRecord 更新充值记录记录
// Author [yourname](https://github.com/yourname)
func (rechargerecordService *RechargeRecordService)UpdateRechargeRecord(ctx context.Context, rechargerecord meta.RechargeRecord) (err error) {
	err = global.GVA_DB.Model(&meta.RechargeRecord{}).Where("id = ?",rechargerecord.ID).Updates(&rechargerecord).Error
	return err
}

// GetRechargeRecord 根据ID获取充值记录记录
// Author [yourname](https://github.com/yourname)
func (rechargerecordService *RechargeRecordService)GetRechargeRecord(ctx context.Context, ID string) (rechargerecord meta.RechargeRecord, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&rechargerecord).Error
	return
}
// GetRechargeRecordInfoList 分页获取充值记录记录
// Author [yourname](https://github.com/yourname)
func (rechargerecordService *RechargeRecordService)GetRechargeRecordInfoList(ctx context.Context, info metaReq.RechargeRecordSearch) (list []meta.RechargeRecord, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&meta.RechargeRecord{})
    var rechargerecords []meta.RechargeRecord
    // 如果有条件搜索 下方会自动创建搜索语句
    if len(info.CreatedAtRange) == 2 {
     db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
    }
    
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }
        var OrderStr string
        orderMap := make(map[string]bool)
           orderMap["ID"] = true
           orderMap["CreatedAt"] = true
         	orderMap["order_no"] = true
         	orderMap["amount"] = true
         	orderMap["request_time"] = true
         	orderMap["payment_time"] = true
         	orderMap["completed_time"] = true
       if orderMap[info.Sort] {
          OrderStr = info.Sort
          if info.Order == "descending" {
             OrderStr = OrderStr + " desc"
          }
          db = db.Order(OrderStr)
       }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&rechargerecords).Error
	return  rechargerecords, total, err
}
func (rechargerecordService *RechargeRecordService)GetRechargeRecordDataSource(ctx context.Context) (res map[string][]map[string]any, err error) {
	res = make(map[string][]map[string]any)
	
	   user_id := make([]map[string]any, 0)
	   
       
       global.GVA_DB.Table("sys_users").Where("deleted_at IS NULL").Select("username as label,id as value").Scan(&user_id)
	   res["user_id"] = user_id
	return
}
func (rechargerecordService *RechargeRecordService)GetRechargeRecordPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
