package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type AdStatisticsService struct{}

// CreateAdStatistics 创建广告统计记录
// Author [yourname](https://github.com/yourname)
func (adstatisticsService *AdStatisticsService) CreateAdStatistics(ctx context.Context, adstatistics *meta.AdStatistics) (err error) {
	err = global.GVA_DB.Create(adstatistics).Error
	return err
}

// DeleteAdStatistics 删除广告统计记录
// Author [yourname](https://github.com/yourname)
func (adstatisticsService *AdStatisticsService) DeleteAdStatistics(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&meta.AdStatistics{}, "id = ?", ID).Error
	return err
}

// DeleteAdStatisticsByIds 批量删除广告统计记录
// Author [yourname](https://github.com/yourname)
func (adstatisticsService *AdStatisticsService) DeleteAdStatisticsByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.AdStatistics{}, "id in ?", IDs).Error
	return err
}

// UpdateAdStatistics 更新广告统计记录
// Author [yourname](https://github.com/yourname)
func (adstatisticsService *AdStatisticsService) UpdateAdStatistics(ctx context.Context, adstatistics meta.AdStatistics) (err error) {
	err = global.GVA_DB.Model(&meta.AdStatistics{}).Where("id = ?", adstatistics.ID).Updates(&adstatistics).Error
	return err
}

// GetAdStatistics 根据ID获取广告统计记录
// Author [yourname](https://github.com/yourname)
func (adstatisticsService *AdStatisticsService) GetAdStatistics(ctx context.Context, ID string) (adstatistics meta.AdStatistics, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&adstatistics).Error
	return
}

// GetAdStatisticsInfoList 分页获取广告统计记录
// Author [yourname](https://github.com/yourname)
func (adstatisticsService *AdStatisticsService) GetAdStatisticsInfoList(ctx context.Context, info metaReq.AdStatisticsSearch) (list []meta.AdStatistics, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&meta.AdStatistics{})
	var adstatisticss []meta.AdStatistics
	// 如果有条件搜索 下方会自动创建搜索语句
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["ID"] = true
	orderMap["CreatedAt"] = true
	orderMap["stat_date"] = true
	orderMap["impressions"] = true
	orderMap["clicks"] = true
	orderMap["revenue"] = true
	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc"
		}
		db = db.Order(OrderStr)
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&adstatisticss).Error
	return adstatisticss, total, err
}
func (adstatisticsService *AdStatisticsService) GetAdStatisticsDataSource(ctx context.Context) (res map[string][]map[string]any, err error) {
	res = make(map[string][]map[string]any)

	ad_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ads").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&ad_id)
	res["ad_id"] = ad_id
	ad_position_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ad_positions").Select("name as label,id as value").Scan(&ad_position_id)
	res["ad_position_id"] = ad_position_id
	app_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_apps").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&app_id)
	res["app_id"] = app_id
	campaign_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_campaigns").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&campaign_id)
	res["campaign_id"] = campaign_id
	user_id := make([]map[string]any, 0)

	global.GVA_DB.Table("sys_users").Where("deleted_at IS NULL").Select("username as label,id as value").Scan(&user_id)
	res["user_id"] = user_id
	return
}
func (adstatisticsService *AdStatisticsService) GetAdStatisticsPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
