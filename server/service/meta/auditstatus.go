
package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type AuditStatusService struct {}
// CreateAuditStatus 创建审核状态配置记录
// Author [yourname](https://github.com/yourname)
func (auditstatusService *AuditStatusService) CreateAuditStatus(ctx context.Context, auditstatus *meta.AuditStatus) (err error) {
	err = global.GVA_DB.Create(auditstatus).Error
	return err
}

// DeleteAuditStatus 删除审核状态配置记录
// Author [yourname](https://github.com/yourname)
func (auditstatusService *AuditStatusService)DeleteAuditStatus(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&meta.AuditStatus{},"id = ?",id).Error
	return err
}

// DeleteAuditStatusByIds 批量删除审核状态配置记录
// Author [yourname](https://github.com/yourname)
func (auditstatusService *AuditStatusService)DeleteAuditStatusByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.AuditStatus{},"id in ?",ids).Error
	return err
}

// UpdateAuditStatus 更新审核状态配置记录
// Author [yourname](https://github.com/yourname)
func (auditstatusService *AuditStatusService)UpdateAuditStatus(ctx context.Context, auditstatus meta.AuditStatus) (err error) {
	err = global.GVA_DB.Model(&meta.AuditStatus{}).Where("id = ?",auditstatus.ID).Updates(&auditstatus).Error
	return err
}

// GetAuditStatus 根据id获取审核状态配置记录
// Author [yourname](https://github.com/yourname)
func (auditstatusService *AuditStatusService)GetAuditStatus(ctx context.Context, id string) (auditstatus meta.AuditStatus, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&auditstatus).Error
	return
}
// GetAuditStatusInfoList 分页获取审核状态配置记录
// Author [yourname](https://github.com/yourname)
func (auditstatusService *AuditStatusService)GetAuditStatusInfoList(ctx context.Context, info metaReq.AuditStatusSearch) (list []meta.AuditStatus, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&meta.AuditStatus{})
    var auditstatuss []meta.AuditStatus
    // 如果有条件搜索 下方会自动创建搜索语句
    
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }
        var OrderStr string
        orderMap := make(map[string]bool)
         	orderMap["name"] = true
       if orderMap[info.Sort] {
          OrderStr = info.Sort
          if info.Order == "descending" {
             OrderStr = OrderStr + " desc"
          }
          db = db.Order(OrderStr)
       }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&auditstatuss).Error
	return  auditstatuss, total, err
}
func (auditstatusService *AuditStatusService)GetAuditStatusPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
