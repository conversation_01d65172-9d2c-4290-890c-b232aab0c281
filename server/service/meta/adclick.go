package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type AdClickService struct{}

// CreateAdClick 创建广告点击记录记录
// Author [yourname](https://github.com/yourname)
func (adclickService *AdClickService) CreateAdClick(ctx context.Context, adclick *meta.AdClick) (err error) {
	err = global.GVA_DB.Create(adclick).Error
	return err
}

// DeleteAdClick 删除广告点击记录记录
// Author [yourname](https://github.com/yourname)
func (adclickService *AdClickService) DeleteAdClick(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&meta.AdClick{}, "id = ?", ID).Error
	return err
}

// DeleteAdClickByIds 批量删除广告点击记录记录
// Author [yourname](https://github.com/yourname)
func (adclickService *AdClickService) DeleteAdClickByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.AdClick{}, "id in ?", IDs).Error
	return err
}

// UpdateAdClick 更新广告点击记录记录
// Author [yourname](https://github.com/yourname)
func (adclickService *AdClickService) UpdateAdClick(ctx context.Context, adclick meta.AdClick) (err error) {
	err = global.GVA_DB.Model(&meta.AdClick{}).Where("id = ?", adclick.ID).Updates(&adclick).Error
	return err
}

// GetAdClick 根据ID获取广告点击记录记录
// Author [yourname](https://github.com/yourname)
func (adclickService *AdClickService) GetAdClick(ctx context.Context, ID string) (adclick meta.AdClick, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&adclick).Error
	return
}

// GetAdClickInfoList 分页获取广告点击记录记录
// Author [yourname](https://github.com/yourname)
func (adclickService *AdClickService) GetAdClickInfoList(ctx context.Context, info metaReq.AdClickSearch) (list []meta.AdClick, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&meta.AdClick{})
	var adclicks []meta.AdClick
	// 如果有条件搜索 下方会自动创建搜索语句
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["ID"] = true
	orderMap["CreatedAt"] = true
	orderMap["click_time"] = true
	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc"
		}
		db = db.Order(OrderStr)
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&adclicks).Error
	return adclicks, total, err
}
func (adclickService *AdClickService) GetAdClickDataSource(ctx context.Context) (res map[string][]map[string]any, err error) {
	res = make(map[string][]map[string]any)

	ad_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ads").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&ad_id)
	res["ad_id"] = ad_id
	ad_position_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ad_positions").Select("name as label,id as value").Scan(&ad_position_id)
	res["ad_position_id"] = ad_position_id
	app_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_apps").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&app_id)
	res["app_id"] = app_id
	campaign_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_campaigns").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&campaign_id)
	res["campaign_id"] = campaign_id
	impression_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ad_impressions").Where("deleted_at IS NULL").Select("id as label,id as value").Scan(&impression_id)
	res["impression_id"] = impression_id
	return
}
func (adclickService *AdClickService) GetAdClickPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
