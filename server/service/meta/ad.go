package meta

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type AdService struct{}

// CreateAd 创建广告管理记录
// Author [yourname](https://github.com/yourname)
func (adService *AdService) CreateAd(ctx context.Context, ad *meta.Ad) (err error) {
	err = global.GVA_DB.Create(ad).Error
	return err
}

// DeleteAd 删除广告管理记录
// Author [yourname](https://github.com/yourname)
func (adService *AdService) DeleteAd(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&meta.Ad{}, "id = ?", ID).Error
	return err
}

// DeleteAdByIds 批量删除广告管理记录
// Author [yourname](https://github.com/yourname)
func (adService *AdService) DeleteAdByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.Ad{}, "id in ?", IDs).Error
	return err
}

// UpdateAd 更新广告管理记录
// Author [yourname](https://github.com/yourname)
func (adService *AdService) UpdateAd(ctx context.Context, ad meta.Ad) (err error) {
	err = global.GVA_DB.Model(&meta.Ad{}).Where("id = ?", ad.ID).Updates(&ad).Error
	return err
}

// GetAd 根据ID获取广告管理记录
// Author [yourname](https://github.com/yourname)
func (adService *AdService) GetAd(ctx context.Context, ID string) (ad meta.Ad, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&ad).Error
	return
}

// GetAdInfoList 分页获取广告管理记录
// Author [yourname](https://github.com/yourname)
func (adService *AdService) GetAdInfoList(ctx context.Context, info metaReq.AdSearch) (list []meta.Ad, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&meta.Ad{})
	var ads []meta.Ad
	// 如果有条件搜索 下方会自动创建搜索语句
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}

	if info.Status != nil {
		db = db.Where("status = ?", *info.Status)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["ID"] = true
	orderMap["CreatedAt"] = true
	orderMap["name"] = true
	orderMap["reviewed_at"] = true
	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc"
		}
		db = db.Order(OrderStr)
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&ads).Error
	return ads, total, err
}
func (adService *AdService) GetAdDataSource(ctx context.Context) (res map[string][]map[string]any, err error) {
	res = make(map[string][]map[string]any)

	ad_action_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ad_actions").Select("name as label,id as value").Scan(&ad_action_id)
	res["ad_action_id"] = ad_action_id
	ad_positions := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ad_positions").Select("name as label,id as value").Scan(&ad_positions)
	res["ad_positions"] = ad_positions
	ad_type_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ad_types").Select("name as label,id as value").Scan(&ad_type_id)
	res["ad_type_id"] = ad_type_id
	reviewer_id := make([]map[string]any, 0)

	global.GVA_DB.Table("sys_users").Where("deleted_at IS NULL").Select("username as label,id as value").Scan(&reviewer_id)
	res["reviewer_id"] = reviewer_id
	status := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_audit_status").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&status)
	res["status"] = status
	user_id := make([]map[string]any, 0)

	global.GVA_DB.Table("sys_users").Where("deleted_at IS NULL").Select("username as label,id as value").Scan(&user_id)
	res["user_id"] = user_id
	return
}
func (adService *AdService) GetAdPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}

// CreateAdWithCampaign 自动创建广告和投放计划
func (adService *AdService) CreateAdWithCampaign(ctx context.Context, req *metaReq.AdWizardRequest, userId int) (*metaReq.AdWizardResponse, error) {
	// 开始事务
	tx := global.GVA_DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 根据目标确定计费方式和广告行为
	var bidType string
	var adActionId int

	if req.Objective == "brand" {
		bidType = "CPM"
		adActionId = 1 // 假设1为品牌宣传行为ID
	} else {
		bidType = "CPC"
		adActionId = 2 // 假设2为引流行为ID
	}

	// 2. 创建广告
	status := 3 // 自动审核通过
	ad := meta.Ad{
		Name:        &req.Name,
		UserId:      &userId,
		AdTypeId:    &req.AdTypeId,
		AdActionId:  &adActionId,
		Status:      &status,
		Title:       &req.Title,
		Description: &req.Description,
		MediaUrl:    req.MediaUrl,
		ClickUrl:    &req.ClickUrl,
	}

	// 设置广告位置
	if len(req.AdPositions) > 0 {
		adPositionsJSON, _ := json.Marshal(req.AdPositions)
		ad.AdPositions = adPositionsJSON
	} else {
		// 默认所有广告位
		defaultPositions := []int{1, 2, 3} // 假设默认广告位ID
		adPositionsJSON, _ := json.Marshal(defaultPositions)
		ad.AdPositions = adPositionsJSON
	}

	if err := tx.Create(&ad).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建广告失败: %v", err)
	}

	// 3. 设置时间范围
	startTime := req.StartTime
	endTime := req.EndTime

	if startTime.IsZero() {
		startTime = time.Now()
	}
	if endTime.IsZero() {
		endTime = startTime.AddDate(0, 1, 0) // 默认30天
	}

	// 4. 创建投放计划
	campaignName := fmt.Sprintf("%s - 投放计划", req.Name)
	campaignStatus := "running"

	adIds := []uint{ad.ID}
	adIdsJSON, _ := json.Marshal(adIds)

	campaign := meta.Campaign{
		Name:           &campaignName,
		UserId:         &userId,
		Status:         &campaignStatus,
		AdIds:          adIdsJSON,
		BidType:        &bidType,
		StartTime:      &startTime,
		EndTime:        &endTime,
		TotalBudget:    &req.TotalBudget,
		DailyBudget:    &req.DailyBudget,
		BidAmount:      &req.BidAmount,
		MaxImpressions: &req.MaxImpressions,
		MaxClicks:      &req.MaxClicks,
	}

	if err := tx.Create(&campaign).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("创建投放计划失败: %v", err)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("提交事务失败: %v", err)
	}

	return &metaReq.AdWizardResponse{
		AdId:       ad.ID,
		CampaignId: campaign.ID,
		Message:    "广告和投放计划创建成功",
	}, nil
}
