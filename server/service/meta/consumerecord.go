package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type ConsumeRecordService struct{}

// CreateConsumeRecord 创建消费记录记录
// Author [yourname](https://github.com/yourname)
func (consumerecordService *ConsumeRecordService) CreateConsumeRecord(ctx context.Context, consumerecord *meta.ConsumeRecord) (err error) {
	err = global.GVA_DB.Create(consumerecord).Error
	return err
}

// DeleteConsumeRecord 删除消费记录记录
// Author [yourname](https://github.com/yourname)
func (consumerecordService *ConsumeRecordService) DeleteConsumeRecord(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&meta.ConsumeRecord{}, "id = ?", ID).Error
	return err
}

// DeleteConsumeRecordByIds 批量删除消费记录记录
// Author [yourname](https://github.com/yourname)
func (consumerecordService *ConsumeRecordService) DeleteConsumeRecordByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.ConsumeRecord{}, "id in ?", IDs).Error
	return err
}

// UpdateConsumeRecord 更新消费记录记录
// Author [yourname](https://github.com/yourname)
func (consumerecordService *ConsumeRecordService) UpdateConsumeRecord(ctx context.Context, consumerecord meta.ConsumeRecord) (err error) {
	err = global.GVA_DB.Model(&meta.ConsumeRecord{}).Where("id = ?", consumerecord.ID).Updates(&consumerecord).Error
	return err
}

// GetConsumeRecord 根据ID获取消费记录记录
// Author [yourname](https://github.com/yourname)
func (consumerecordService *ConsumeRecordService) GetConsumeRecord(ctx context.Context, ID string) (consumerecord meta.ConsumeRecord, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&consumerecord).Error
	return
}

// GetConsumeRecordInfoList 分页获取消费记录记录
// Author [yourname](https://github.com/yourname)
func (consumerecordService *ConsumeRecordService) GetConsumeRecordInfoList(ctx context.Context, info metaReq.ConsumeRecordSearch) (list []meta.ConsumeRecord, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&meta.ConsumeRecord{})
	var consumerecords []meta.ConsumeRecord
	// 如果有条件搜索 下方会自动创建搜索语句
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["ID"] = true
	orderMap["CreatedAt"] = true
	orderMap["amount"] = true
	orderMap["consume_time"] = true
	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc"
		}
		db = db.Order(OrderStr)
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&consumerecords).Error
	return consumerecords, total, err
}
func (consumerecordService *ConsumeRecordService) GetConsumeRecordDataSource(ctx context.Context) (res map[string][]map[string]any, err error) {
	res = make(map[string][]map[string]any)

	ad_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ads").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&ad_id)
	res["ad_id"] = ad_id
	ad_position_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ad_positions").Select("name as label,id as value").Scan(&ad_position_id)
	res["ad_position_id"] = ad_position_id
	app_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_apps").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&app_id)
	res["app_id"] = app_id
	campaign_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_campaigns").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&campaign_id)
	res["campaign_id"] = campaign_id
	user_id := make([]map[string]any, 0)

	global.GVA_DB.Table("sys_users").Where("deleted_at IS NULL").Select("username as label,id as value").Scan(&user_id)
	res["user_id"] = user_id
	return
}
func (consumerecordService *ConsumeRecordService) GetConsumeRecordPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
