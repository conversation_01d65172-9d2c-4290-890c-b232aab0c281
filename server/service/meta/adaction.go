
package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type AdActionService struct {}
// CreateAdAction 创建广告行为管理记录
// Author [yourname](https://github.com/yourname)
func (adactionService *AdActionService) CreateAdAction(ctx context.Context, adaction *meta.AdAction) (err error) {
	err = global.GVA_DB.Create(adaction).Error
	return err
}

// DeleteAdAction 删除广告行为管理记录
// Author [yourname](https://github.com/yourname)
func (adactionService *AdActionService)DeleteAdAction(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&meta.AdAction{},"id = ?",id).Error
	return err
}

// DeleteAdActionByIds 批量删除广告行为管理记录
// Author [yourname](https://github.com/yourname)
func (adactionService *AdActionService)DeleteAdActionByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.AdAction{},"id in ?",ids).Error
	return err
}

// UpdateAdAction 更新广告行为管理记录
// Author [yourname](https://github.com/yourname)
func (adactionService *AdActionService)UpdateAdAction(ctx context.Context, adaction meta.AdAction) (err error) {
	err = global.GVA_DB.Model(&meta.AdAction{}).Where("id = ?",adaction.ID).Updates(&adaction).Error
	return err
}

// GetAdAction 根据id获取广告行为管理记录
// Author [yourname](https://github.com/yourname)
func (adactionService *AdActionService)GetAdAction(ctx context.Context, id string) (adaction meta.AdAction, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&adaction).Error
	return
}
// GetAdActionInfoList 分页获取广告行为管理记录
// Author [yourname](https://github.com/yourname)
func (adactionService *AdActionService)GetAdActionInfoList(ctx context.Context, info metaReq.AdActionSearch) (list []meta.AdAction, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&meta.AdAction{})
    var adactions []meta.AdAction
    // 如果有条件搜索 下方会自动创建搜索语句
    
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }
        var OrderStr string
        orderMap := make(map[string]bool)
         	orderMap["name"] = true
       if orderMap[info.Sort] {
          OrderStr = info.Sort
          if info.Order == "descending" {
             OrderStr = OrderStr + " desc"
          }
          db = db.Order(OrderStr)
       }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&adactions).Error
	return  adactions, total, err
}
func (adactionService *AdActionService)GetAdActionPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
