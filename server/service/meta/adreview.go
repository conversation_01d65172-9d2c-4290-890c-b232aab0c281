
package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type AdReviewService struct {}
// CreateAdReview 创建广告审核记录
// Author [yourname](https://github.com/yourname)
func (adreviewService *AdReviewService) CreateAdReview(ctx context.Context, adreview *meta.AdReview) (err error) {
	err = global.GVA_DB.Create(adreview).Error
	return err
}

// DeleteAdReview 删除广告审核记录
// Author [yourname](https://github.com/yourname)
func (adreviewService *AdReviewService)DeleteAdReview(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&meta.AdReview{},"id = ?",ID).Error
	return err
}

// DeleteAdReviewByIds 批量删除广告审核记录
// Author [yourname](https://github.com/yourname)
func (adreviewService *AdReviewService)DeleteAdReviewByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.AdReview{},"id in ?",IDs).Error
	return err
}

// UpdateAdReview 更新广告审核记录
// Author [yourname](https://github.com/yourname)
func (adreviewService *AdReviewService)UpdateAdReview(ctx context.Context, adreview meta.AdReview) (err error) {
	err = global.GVA_DB.Model(&meta.AdReview{}).Where("id = ?",adreview.ID).Updates(&adreview).Error
	return err
}

// GetAdReview 根据ID获取广告审核记录
// Author [yourname](https://github.com/yourname)
func (adreviewService *AdReviewService)GetAdReview(ctx context.Context, ID string) (adreview meta.AdReview, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&adreview).Error
	return
}
// GetAdReviewInfoList 分页获取广告审核记录
// Author [yourname](https://github.com/yourname)
func (adreviewService *AdReviewService)GetAdReviewInfoList(ctx context.Context, info metaReq.AdReviewSearch) (list []meta.AdReview, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&meta.AdReview{})
    var adreviews []meta.AdReview
    // 如果有条件搜索 下方会自动创建搜索语句
    if len(info.CreatedAtRange) == 2 {
     db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
    }
    
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }
        var OrderStr string
        orderMap := make(map[string]bool)
           orderMap["ID"] = true
           orderMap["CreatedAt"] = true
         	orderMap["submit_time"] = true
         	orderMap["start_time"] = true
         	orderMap["complete_time"] = true
         	orderMap["priority"] = true
       if orderMap[info.Sort] {
          OrderStr = info.Sort
          if info.Order == "descending" {
             OrderStr = OrderStr + " desc"
          }
          db = db.Order(OrderStr)
       }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&adreviews).Error
	return  adreviews, total, err
}
func (adreviewService *AdReviewService)GetAdReviewDataSource(ctx context.Context) (res map[string][]map[string]any, err error) {
	res = make(map[string][]map[string]any)
	
	   ad_id := make([]map[string]any, 0)
	   
       
       global.GVA_DB.Table("meta_ads").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&ad_id)
	   res["ad_id"] = ad_id
	   reviewer_id := make([]map[string]any, 0)
	   
       
       global.GVA_DB.Table("sys_users").Where("deleted_at IS NULL").Select("username as label,id as value").Scan(&reviewer_id)
	   res["reviewer_id"] = reviewer_id
	return
}
func (adreviewService *AdReviewService)GetAdReviewPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
