package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type AdImpressionService struct{}

// CreateAdImpression 创建广告展示记录记录
// Author [yourname](https://github.com/yourname)
func (adimpressionService *AdImpressionService) CreateAdImpression(ctx context.Context, adimpression *meta.AdImpression) (err error) {
	err = global.GVA_DB.Create(adimpression).Error
	return err
}

// DeleteAdImpression 删除广告展示记录记录
// Author [yourname](https://github.com/yourname)
func (adimpressionService *AdImpressionService) DeleteAdImpression(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&meta.AdImpression{}, "id = ?", ID).Error
	return err
}

// DeleteAdImpressionByIds 批量删除广告展示记录记录
// Author [yourname](https://github.com/yourname)
func (adimpressionService *AdImpressionService) DeleteAdImpressionByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.AdImpression{}, "id in ?", IDs).Error
	return err
}

// UpdateAdImpression 更新广告展示记录记录
// Author [yourname](https://github.com/yourname)
func (adimpressionService *AdImpressionService) UpdateAdImpression(ctx context.Context, adimpression meta.AdImpression) (err error) {
	err = global.GVA_DB.Model(&meta.AdImpression{}).Where("id = ?", adimpression.ID).Updates(&adimpression).Error
	return err
}

// GetAdImpression 根据ID获取广告展示记录记录
// Author [yourname](https://github.com/yourname)
func (adimpressionService *AdImpressionService) GetAdImpression(ctx context.Context, ID string) (adimpression meta.AdImpression, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&adimpression).Error
	return
}

// GetAdImpressionInfoList 分页获取广告展示记录记录
// Author [yourname](https://github.com/yourname)
func (adimpressionService *AdImpressionService) GetAdImpressionInfoList(ctx context.Context, info metaReq.AdImpressionSearch) (list []meta.AdImpression, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&meta.AdImpression{})
	var adimpressions []meta.AdImpression
	// 如果有条件搜索 下方会自动创建搜索语句
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["ID"] = true
	orderMap["CreatedAt"] = true
	orderMap["impression_time"] = true
	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc"
		}
		db = db.Order(OrderStr)
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&adimpressions).Error
	return adimpressions, total, err
}
func (adimpressionService *AdImpressionService) GetAdImpressionDataSource(ctx context.Context) (res map[string][]map[string]any, err error) {
	res = make(map[string][]map[string]any)

	ad_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ads").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&ad_id)
	res["ad_id"] = ad_id
	ad_position_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ad_positions").Select("name as label,id as value").Scan(&ad_position_id)
	res["ad_position_id"] = ad_position_id
	app_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_apps").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&app_id)
	res["app_id"] = app_id
	campaign_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_campaigns").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&campaign_id)
	res["campaign_id"] = campaign_id
	return
}
func (adimpressionService *AdImpressionService) GetAdImpressionPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
