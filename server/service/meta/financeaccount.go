
package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type FinanceAccountService struct {}
// CreateFinanceAccount 创建财务账户记录
// Author [yourname](https://github.com/yourname)
func (financeaccountService *FinanceAccountService) CreateFinanceAccount(ctx context.Context, financeaccount *meta.FinanceAccount) (err error) {
	err = global.GVA_DB.Create(financeaccount).Error
	return err
}

// DeleteFinanceAccount 删除财务账户记录
// Author [yourname](https://github.com/yourname)
func (financeaccountService *FinanceAccountService)DeleteFinanceAccount(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&meta.FinanceAccount{},"id = ?",ID).Error
	return err
}

// DeleteFinanceAccountByIds 批量删除财务账户记录
// Author [yourname](https://github.com/yourname)
func (financeaccountService *FinanceAccountService)DeleteFinanceAccountByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.FinanceAccount{},"id in ?",IDs).Error
	return err
}

// UpdateFinanceAccount 更新财务账户记录
// Author [yourname](https://github.com/yourname)
func (financeaccountService *FinanceAccountService)UpdateFinanceAccount(ctx context.Context, financeaccount meta.FinanceAccount) (err error) {
	err = global.GVA_DB.Model(&meta.FinanceAccount{}).Where("id = ?",financeaccount.ID).Updates(&financeaccount).Error
	return err
}

// GetFinanceAccount 根据ID获取财务账户记录
// Author [yourname](https://github.com/yourname)
func (financeaccountService *FinanceAccountService)GetFinanceAccount(ctx context.Context, ID string) (financeaccount meta.FinanceAccount, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&financeaccount).Error
	return
}
// GetFinanceAccountInfoList 分页获取财务账户记录
// Author [yourname](https://github.com/yourname)
func (financeaccountService *FinanceAccountService)GetFinanceAccountInfoList(ctx context.Context, info metaReq.FinanceAccountSearch) (list []meta.FinanceAccount, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&meta.FinanceAccount{})
    var financeaccounts []meta.FinanceAccount
    // 如果有条件搜索 下方会自动创建搜索语句
    if len(info.CreatedAtRange) == 2 {
     db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
    }
    
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }
        var OrderStr string
        orderMap := make(map[string]bool)
           orderMap["ID"] = true
           orderMap["CreatedAt"] = true
         	orderMap["balance"] = true
         	orderMap["total_recharge"] = true
         	orderMap["total_consume"] = true
         	orderMap["total_earning"] = true
         	orderMap["last_transaction_time"] = true
       if orderMap[info.Sort] {
          OrderStr = info.Sort
          if info.Order == "descending" {
             OrderStr = OrderStr + " desc"
          }
          db = db.Order(OrderStr)
       }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&financeaccounts).Error
	return  financeaccounts, total, err
}
func (financeaccountService *FinanceAccountService)GetFinanceAccountDataSource(ctx context.Context) (res map[string][]map[string]any, err error) {
	res = make(map[string][]map[string]any)
	
	   user_id := make([]map[string]any, 0)
	   
       
       global.GVA_DB.Table("sys_users").Where("deleted_at IS NULL").Select("username as label,id as value").Scan(&user_id)
	   res["user_id"] = user_id
	return
}
func (financeaccountService *FinanceAccountService)GetFinanceAccountPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
