package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type AppService struct{}

// CreateApp 创建应用管理记录
// Author [yourname](https://github.com/yourname)
func (appService *AppService) CreateApp(ctx context.Context, app *meta.App) (err error) {
	err = global.GVA_DB.Create(app).Error
	return err
}

// DeleteApp 删除应用管理记录
// Author [yourname](https://github.com/yourname)
func (appService *AppService) DeleteApp(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&meta.App{}, "id = ?", ID).Error
	return err
}

// DeleteAppByIds 批量删除应用管理记录
// Author [yourname](https://github.com/yourname)
func (appService *AppService) DeleteAppByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.App{}, "id in ?", IDs).Error
	return err
}

// UpdateApp 更新应用管理记录
// Author [yourname](https://github.com/yourname)
func (appService *AppService) UpdateApp(ctx context.Context, app meta.App) (err error) {
	err = global.GVA_DB.Model(&meta.App{}).Where("id = ?", app.ID).Updates(&app).Error
	return err
}

// GetApp 根据ID获取应用管理记录
// Author [yourname](https://github.com/yourname)
func (appService *AppService) GetApp(ctx context.Context, ID string) (app meta.App, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&app).Error
	return
}

// GetAppInfoList 分页获取应用管理记录
// Author [yourname](https://github.com/yourname)
func (appService *AppService) GetAppInfoList(ctx context.Context, info metaReq.AppSearch) (list []meta.App, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&meta.App{})
	var apps []meta.App
	// 如果有条件搜索 下方会自动创建搜索语句
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}

	if info.Status != nil {
		db = db.Where("status = ?", *info.Status)
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["ID"] = true
	orderMap["CreatedAt"] = true
	orderMap["name"] = true
	orderMap["package_id"] = true
	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc"
		}
		db = db.Order(OrderStr)
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&apps).Error
	return apps, total, err
}
func (appService *AppService) GetAppDataSource(ctx context.Context) (res map[string][]map[string]any, err error) {
	res = make(map[string][]map[string]any)

	ad_positions := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ad_positions").Select("name as label,id as value").Scan(&ad_positions)
	res["ad_positions"] = ad_positions
	user_id := make([]map[string]any, 0)

	global.GVA_DB.Table("sys_users").Where("deleted_at IS NULL").Select("username as label,id as value").Scan(&user_id)
	res["user_id"] = user_id
	return
}
func (appService *AppService) GetAppPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
