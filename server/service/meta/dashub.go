package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type DashubService struct{}

// CreateDashub 创建统计汇总记录
// Author [yourname](https://github.com/yourname)
func (dashubService *DashubService) CreateDashub(ctx context.Context, dashub *meta.Dashub) (err error) {
	err = global.GVA_DB.Create(dashub).Error
	return err
}

// DeleteDashub 删除统计汇总记录
// Author [yourname](https://github.com/yourname)
func (dashubService *DashubService) DeleteDashub(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&meta.Dashub{}, "id = ?", ID).Error
	return err
}

// DeleteDashubByIds 批量删除统计汇总记录
// Author [yourname](https://github.com/yourname)
func (dashubService *DashubService) DeleteDashubByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.Dashub{}, "id in ?", IDs).Error
	return err
}

// UpdateDashub 更新统计汇总记录
// Author [yourname](https://github.com/yourname)
func (dashubService *DashubService) UpdateDashub(ctx context.Context, dashub meta.Dashub) (err error) {
	err = global.GVA_DB.Model(&meta.Dashub{}).Where("id = ?", dashub.ID).Updates(&dashub).Error
	return err
}

// GetDashub 根据ID获取统计汇总记录
// Author [yourname](https://github.com/yourname)
func (dashubService *DashubService) GetDashub(ctx context.Context, ID string) (dashub meta.Dashub, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&dashub).Error
	return
}

// GetDashubInfoList 分页获取统计汇总记录
// Author [yourname](https://github.com/yourname)
func (dashubService *DashubService) GetDashubInfoList(ctx context.Context, info metaReq.DashubSearch) (list []meta.Dashub, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&meta.Dashub{})
	var dashubs []meta.Dashub
	// 如果有条件搜索 下方会自动创建搜索语句
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}

	if info.Kind != nil {
		db = db.Where("kind = ?", *info.Kind)
	}
	// TODO 允许查询条件
	// if info.TargetType != nil {
	//     db = db.Where("target_type = ?", *info.TargetType)
	// }
	// if info.Target != nil {
	//     db = db.Where("target = ?", *info.Target)
	// }
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&dashubs).Error
	return dashubs, total, err
}
func (dashubService *DashubService) GetDashubPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
