package meta

import (
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

type DashboardService struct{}

// GetDashboardData 获取用户的Dashboard数据
func (dashboardService *DashboardService) GetDashboardData(userID uint, authorityID uint) (meta.DashboardResponse, error) {
	var response meta.DashboardResponse
	var items []meta.DashboardItem

	// 获取用户权限对应的项目列表
	allowedItems, exists := meta.RolePermissions[authorityID]
	if !exists {
		return response, fmt.Errorf("未找到角色权限配置")
	}

	// 为每个允许的项目生成数据
	for _, itemID := range allowedItems {
		item, err := dashboardService.generateItemData(itemID, userID, authorityID)
		if err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("生成项目数据失败: %s, 错误: %v", itemID, err))
			continue
		}
		items = append(items, item)
	}

	response.Items = items
	return response, nil
}

// generateItemData 生成单个项目的数据
func (dashboardService *DashboardService) generateItemData(itemID string, userID uint, authorityID uint) (meta.DashboardItem, error) {
	// 获取项目模板
	template, exists := meta.ItemTemplates[itemID]
	if !exists {
		return meta.DashboardItem{}, fmt.Errorf("未找到项目模板: %s", itemID)
	}

	// 复制模板
	item := template

	// 根据项目ID生成具体数据
	switch itemID {
	case meta.ITEM_DAILY_ACTIVE_USERS:
		value, trend, err := dashboardService.getDailyActiveUsers()
		if err != nil {
			return item, err
		}
		item.Value = value
		item.Trend = trend

	case meta.ITEM_WEEKLY_ACTIVE_USERS:
		value, trend, err := dashboardService.getWeeklyActiveUsers()
		if err != nil {
			return item, err
		}
		item.Value = value
		item.Trend = trend

	case meta.ITEM_MONTHLY_ACTIVE_USERS:
		value, trend, err := dashboardService.getMonthlyActiveUsers()
		if err != nil {
			return item, err
		}
		item.Value = value
		item.Trend = trend

	case meta.ITEM_TOTAL_USERS:
		value, trend, err := dashboardService.getTotalUsers()
		if err != nil {
			return item, err
		}
		item.Value = value
		item.Trend = trend

	case meta.ITEM_NEW_USERS:
		value, trend, err := dashboardService.getNewUsers()
		if err != nil {
			return item, err
		}
		item.Value = value
		item.Trend = trend

	case meta.ITEM_ACTIVE_ADS:
		value, trend, err := dashboardService.getActiveAds(userID, authorityID)
		if err != nil {
			return item, err
		}
		item.Value = value
		item.Trend = trend

	case meta.ITEM_AD_IMPRESSIONS:
		value, trend, err := dashboardService.getAdImpressions(userID, authorityID)
		if err != nil {
			return item, err
		}
		item.Value = value
		item.Trend = trend

	case meta.ITEM_AD_CLICKS:
		value, trend, err := dashboardService.getAdClicks(userID, authorityID)
		if err != nil {
			return item, err
		}
		item.Value = value
		item.Trend = trend

	case meta.ITEM_ACCOUNT_BALANCE:
		value, err := dashboardService.getAccountBalance(userID, authorityID)
		if err != nil {
			return item, err
		}
		item.Value = value

	case meta.ITEM_DAILY_CONSUME:
		value, trend, err := dashboardService.getDailyConsume(userID, authorityID)
		if err != nil {
			return item, err
		}
		item.Value = value
		item.Trend = trend

	case meta.ITEM_WEEKLY_CONSUME:
		value, trend, err := dashboardService.getWeeklyConsume(userID, authorityID)
		if err != nil {
			return item, err
		}
		item.Value = value
		item.Trend = trend

	case meta.ITEM_MONTHLY_CONSUME:
		value, trend, err := dashboardService.getMonthlyConsume(userID, authorityID)
		if err != nil {
			return item, err
		}
		item.Value = value
		item.Trend = trend

	case meta.ITEM_DAILY_REVENUE:
		value, trend, err := dashboardService.getDailyRevenue(userID, authorityID)
		if err != nil {
			return item, err
		}
		item.Value = value
		item.Trend = trend

	case meta.ITEM_WEEKLY_REVENUE:
		value, trend, err := dashboardService.getWeeklyRevenue(userID, authorityID)
		if err != nil {
			return item, err
		}
		item.Value = value
		item.Trend = trend

	case meta.ITEM_MONTHLY_REVENUE:
		value, trend, err := dashboardService.getMonthlyRevenue(userID, authorityID)
		if err != nil {
			return item, err
		}
		item.Value = value
		item.Trend = trend

	case meta.ITEM_USER_GROWTH_TREND:
		chartData, err := dashboardService.getUserGrowthTrend()
		if err != nil {
			return item, err
		}
		item.ChartData = chartData

	default:
		return item, fmt.Errorf("未实现的项目类型: %s", itemID)
	}

	return item, nil
}

// 用户相关统计方法
func (dashboardService *DashboardService) getDailyActiveUsers() (int64, *meta.Trend, error) {
	var count int64
	today := time.Now().Format("2006-01-02")
	
	// 统计今日活跃用户（这里简化为今日登录的用户）
	err := global.GVA_DB.Model(&system.SysUser{}).
		Where("DATE(updated_at) = ?", today).
		Count(&count).Error
	
	if err != nil {
		return 0, nil, err
	}

	// 计算趋势（与昨日对比）
	var yesterdayCount int64
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	global.GVA_DB.Model(&system.SysUser{}).
		Where("DATE(updated_at) = ?", yesterday).
		Count(&yesterdayCount)

	trend := &meta.Trend{
		TimeRange: "与昨日对比",
	}
	if yesterdayCount > 0 {
		trend.Percent = float64(count-yesterdayCount) / float64(yesterdayCount) * 100
		trend.IsUp = count > yesterdayCount
		trend.Value = float64(count - yesterdayCount)
	}

	return count, trend, nil
}

func (dashboardService *DashboardService) getWeeklyActiveUsers() (int64, *meta.Trend, error) {
	var count int64
	weekStart := time.Now().AddDate(0, 0, -int(time.Now().Weekday())).Format("2006-01-02")
	
	err := global.GVA_DB.Model(&system.SysUser{}).
		Where("DATE(updated_at) >= ?", weekStart).
		Count(&count).Error
	
	if err != nil {
		return 0, nil, err
	}

	// 计算趋势（与上周对比）
	var lastWeekCount int64
	lastWeekStart := time.Now().AddDate(0, 0, -7-int(time.Now().Weekday())).Format("2006-01-02")
	lastWeekEnd := time.Now().AddDate(0, 0, -int(time.Now().Weekday())-1).Format("2006-01-02")
	global.GVA_DB.Model(&system.SysUser{}).
		Where("DATE(updated_at) >= ? AND DATE(updated_at) <= ?", lastWeekStart, lastWeekEnd).
		Count(&lastWeekCount)

	trend := &meta.Trend{
		TimeRange: "与上周对比",
	}
	if lastWeekCount > 0 {
		trend.Percent = float64(count-lastWeekCount) / float64(lastWeekCount) * 100
		trend.IsUp = count > lastWeekCount
		trend.Value = float64(count - lastWeekCount)
	}

	return count, trend, nil
}

func (dashboardService *DashboardService) getMonthlyActiveUsers() (int64, *meta.Trend, error) {
	var count int64
	monthStart := time.Now().Format("2006-01") + "-01"
	
	err := global.GVA_DB.Model(&system.SysUser{}).
		Where("DATE(updated_at) >= ?", monthStart).
		Count(&count).Error
	
	if err != nil {
		return 0, nil, err
	}

	// 计算趋势（与上月对比）
	var lastMonthCount int64
	lastMonth := time.Now().AddDate(0, -1, 0)
	lastMonthStart := lastMonth.Format("2006-01") + "-01"
	lastMonthEnd := time.Date(lastMonth.Year(), lastMonth.Month()+1, 0, 0, 0, 0, 0, lastMonth.Location()).Format("2006-01-02")
	global.GVA_DB.Model(&system.SysUser{}).
		Where("DATE(updated_at) >= ? AND DATE(updated_at) <= ?", lastMonthStart, lastMonthEnd).
		Count(&lastMonthCount)

	trend := &meta.Trend{
		TimeRange: "与上月对比",
	}
	if lastMonthCount > 0 {
		trend.Percent = float64(count-lastMonthCount) / float64(lastMonthCount) * 100
		trend.IsUp = count > lastMonthCount
		trend.Value = float64(count - lastMonthCount)
	}

	return count, trend, nil
}

func (dashboardService *DashboardService) getTotalUsers() (int64, *meta.Trend, error) {
	var count int64
	err := global.GVA_DB.Model(&system.SysUser{}).Count(&count).Error
	if err != nil {
		return 0, nil, err
	}

	// 计算本月新增用户趋势
	var monthNewCount int64
	monthStart := time.Now().Format("2006-01") + "-01"
	global.GVA_DB.Model(&system.SysUser{}).
		Where("DATE(created_at) >= ?", monthStart).
		Count(&monthNewCount)

	trend := &meta.Trend{
		Value:     float64(monthNewCount),
		IsUp:      monthNewCount > 0,
		TimeRange: "本月新增",
	}

	return count, trend, nil
}

func (dashboardService *DashboardService) getNewUsers() (int64, *meta.Trend, error) {
	var count int64
	today := time.Now().Format("2006-01-02")
	
	err := global.GVA_DB.Model(&system.SysUser{}).
		Where("DATE(created_at) = ?", today).
		Count(&count).Error
	
	if err != nil {
		return 0, nil, err
	}

	// 计算趋势（与昨日对比）
	var yesterdayCount int64
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	global.GVA_DB.Model(&system.SysUser{}).
		Where("DATE(created_at) = ?", yesterday).
		Count(&yesterdayCount)

	trend := &meta.Trend{
		TimeRange: "与昨日对比",
	}
	if yesterdayCount > 0 {
		trend.Percent = float64(count-yesterdayCount) / float64(yesterdayCount) * 100
		trend.IsUp = count > yesterdayCount
		trend.Value = float64(count - yesterdayCount)
	}

	return count, trend, nil
}

// 广告相关统计方法
func (dashboardService *DashboardService) getActiveAds(userID uint, authorityID uint) (int64, *meta.Trend, error) {
	var count int64
	query := global.GVA_DB.Model(&meta.Ad{})

	// 如果是广告主，只查看自己的广告
	if authorityID == 9528 {
		query = query.Where("user_id = ?", userID)
	}

	// 查询状态为已发布的广告
	err := query.Where("status = ?", 5).Count(&count).Error // 5表示已上架状态
	if err != nil {
		return 0, nil, err
	}

	// 计算趋势（与昨日对比）
	var yesterdayCount int64
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	yesterdayQuery := global.GVA_DB.Model(&meta.Ad{})
	if authorityID == 9528 {
		yesterdayQuery = yesterdayQuery.Where("user_id = ?", userID)
	}
	yesterdayQuery.Where("status = ? AND DATE(updated_at) <= ?", 5, yesterday).Count(&yesterdayCount)

	trend := &meta.Trend{
		TimeRange: "与昨日对比",
	}
	if yesterdayCount > 0 {
		trend.Percent = float64(count-yesterdayCount) / float64(yesterdayCount) * 100
		trend.IsUp = count > yesterdayCount
		trend.Value = float64(count - yesterdayCount)
	}

	return count, trend, nil
}

func (dashboardService *DashboardService) getAdImpressions(userID uint, authorityID uint) (int64, *meta.Trend, error) {
	var count int64
	today := time.Now().Format("2006-01-02")

	query := global.GVA_DB.Model(&meta.AdImpression{})

	// 根据角色过滤数据
	if authorityID == 9528 { // 广告主
		// 查询该用户的广告展示数
		query = query.Joins("JOIN meta_ads ON meta_ad_impressions.ad_id = meta_ads.id").
			Where("meta_ads.user_id = ?", userID)
	} else if authorityID == 8881 { // APP开发者
		// 查询该用户APP的广告展示数
		query = query.Joins("JOIN meta_apps ON meta_ad_impressions.app_id = meta_apps.id").
			Where("meta_apps.user_id = ?", userID)
	}

	err := query.Where("DATE(impression_time) = ?", today).Count(&count).Error
	if err != nil {
		return 0, nil, err
	}

	// 计算趋势（与昨日对比）
	var yesterdayCount int64
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	yesterdayQuery := global.GVA_DB.Model(&meta.AdImpression{})
	if authorityID == 9528 {
		yesterdayQuery = yesterdayQuery.Joins("JOIN meta_ads ON meta_ad_impressions.ad_id = meta_ads.id").
			Where("meta_ads.user_id = ?", userID)
	} else if authorityID == 8881 {
		yesterdayQuery = yesterdayQuery.Joins("JOIN meta_apps ON meta_ad_impressions.app_id = meta_apps.id").
			Where("meta_apps.user_id = ?", userID)
	}
	yesterdayQuery.Where("DATE(impression_time) = ?", yesterday).Count(&yesterdayCount)

	trend := &meta.Trend{
		TimeRange: "与昨日对比",
	}
	if yesterdayCount > 0 {
		trend.Percent = float64(count-yesterdayCount) / float64(yesterdayCount) * 100
		trend.IsUp = count > yesterdayCount
		trend.Value = float64(count - yesterdayCount)
	}

	return count, trend, nil
}

func (dashboardService *DashboardService) getAdClicks(userID uint, authorityID uint) (int64, *meta.Trend, error) {
	var count int64
	today := time.Now().Format("2006-01-02")

	query := global.GVA_DB.Model(&meta.AdClick{})

	// 根据角色过滤数据
	if authorityID == 9528 { // 广告主
		query = query.Joins("JOIN meta_ads ON meta_ad_clicks.ad_id = meta_ads.id").
			Where("meta_ads.user_id = ?", userID)
	} else if authorityID == 8881 { // APP开发者
		query = query.Joins("JOIN meta_apps ON meta_ad_clicks.app_id = meta_apps.id").
			Where("meta_apps.user_id = ?", userID)
	}

	err := query.Where("DATE(click_time) = ? AND is_valid = ?", today, true).Count(&count).Error
	if err != nil {
		return 0, nil, err
	}

	// 计算趋势（与昨日对比）
	var yesterdayCount int64
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	yesterdayQuery := global.GVA_DB.Model(&meta.AdClick{})
	if authorityID == 9528 {
		yesterdayQuery = yesterdayQuery.Joins("JOIN meta_ads ON meta_ad_clicks.ad_id = meta_ads.id").
			Where("meta_ads.user_id = ?", userID)
	} else if authorityID == 8881 {
		yesterdayQuery = yesterdayQuery.Joins("JOIN meta_apps ON meta_ad_clicks.app_id = meta_apps.id").
			Where("meta_apps.user_id = ?", userID)
	}
	yesterdayQuery.Where("DATE(click_time) = ? AND is_valid = ?", yesterday, true).Count(&yesterdayCount)

	trend := &meta.Trend{
		TimeRange: "与昨日对比",
	}
	if yesterdayCount > 0 {
		trend.Percent = float64(count-yesterdayCount) / float64(yesterdayCount) * 100
		trend.IsUp = count > yesterdayCount
		trend.Value = float64(count - yesterdayCount)
	}

	return count, trend, nil
}

// 财务相关统计方法
func (dashboardService *DashboardService) getAccountBalance(userID uint, authorityID uint) (float64, error) {
	var account meta.FinanceAccount
	var accountType string

	// 根据角色确定账户类型
	if authorityID == 9528 {
		accountType = "advertiser"
	} else if authorityID == 8881 {
		accountType = "developer"
	} else {
		return 0, fmt.Errorf("不支持的角色类型")
	}

	err := global.GVA_DB.Where("user_id = ? AND account_type = ?", userID, accountType).
		First(&account).Error
	if err != nil {
		return 0, err
	}

	return *account.Balance, nil
}

func (dashboardService *DashboardService) getDailyConsume(userID uint, authorityID uint) (float64, *meta.Trend, error) {
	if authorityID != 9528 {
		return 0, nil, fmt.Errorf("只有广告主可以查看消费数据")
	}

	var totalCost float64
	today := time.Now().Format("2006-01-02")

	// 统计今日广告消费（点击成本 + 展示成本）
	var clickCost, impressionCost float64

	// 点击成本
	global.GVA_DB.Model(&meta.AdClick{}).
		Select("COALESCE(SUM(cost), 0)").
		Joins("JOIN meta_ads ON meta_ad_clicks.ad_id = meta_ads.id").
		Where("meta_ads.user_id = ? AND DATE(click_time) = ?", userID, today).
		Scan(&clickCost)

	// 展示成本
	global.GVA_DB.Model(&meta.AdImpression{}).
		Select("COALESCE(SUM(cost), 0)").
		Joins("JOIN meta_ads ON meta_ad_impressions.ad_id = meta_ads.id").
		Where("meta_ads.user_id = ? AND DATE(impression_time) = ?", userID, today).
		Scan(&impressionCost)

	totalCost = clickCost + impressionCost

	// 计算趋势（与昨日对比）
	var yesterdayClickCost, yesterdayImpressionCost float64
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")

	global.GVA_DB.Model(&meta.AdClick{}).
		Select("COALESCE(SUM(cost), 0)").
		Joins("JOIN meta_ads ON meta_ad_clicks.ad_id = meta_ads.id").
		Where("meta_ads.user_id = ? AND DATE(click_time) = ?", userID, yesterday).
		Scan(&yesterdayClickCost)

	global.GVA_DB.Model(&meta.AdImpression{}).
		Select("COALESCE(SUM(cost), 0)").
		Joins("JOIN meta_ads ON meta_ad_impressions.ad_id = meta_ads.id").
		Where("meta_ads.user_id = ? AND DATE(impression_time) = ?", userID, yesterday).
		Scan(&yesterdayImpressionCost)

	yesterdayTotalCost := yesterdayClickCost + yesterdayImpressionCost

	trend := &meta.Trend{
		TimeRange: "与昨日对比",
	}
	if yesterdayTotalCost > 0 {
		trend.Percent = (totalCost - yesterdayTotalCost) / yesterdayTotalCost * 100
		trend.IsUp = totalCost > yesterdayTotalCost
		trend.Value = totalCost - yesterdayTotalCost
	}

	return totalCost, trend, nil
}

func (dashboardService *DashboardService) getWeeklyConsume(userID uint, authorityID uint) (float64, *meta.Trend, error) {
	if authorityID != 9528 {
		return 0, nil, fmt.Errorf("只有广告主可以查看消费数据")
	}

	var totalCost float64
	weekStart := time.Now().AddDate(0, 0, -int(time.Now().Weekday())).Format("2006-01-02")

	// 统计本周广告消费
	var clickCost, impressionCost float64

	global.GVA_DB.Model(&meta.AdClick{}).
		Select("COALESCE(SUM(cost), 0)").
		Joins("JOIN meta_ads ON meta_ad_clicks.ad_id = meta_ads.id").
		Where("meta_ads.user_id = ? AND DATE(click_time) >= ?", userID, weekStart).
		Scan(&clickCost)

	global.GVA_DB.Model(&meta.AdImpression{}).
		Select("COALESCE(SUM(cost), 0)").
		Joins("JOIN meta_ads ON meta_ad_impressions.ad_id = meta_ads.id").
		Where("meta_ads.user_id = ? AND DATE(impression_time) >= ?", userID, weekStart).
		Scan(&impressionCost)

	totalCost = clickCost + impressionCost

	// 计算趋势（与上周对比）
	var lastWeekClickCost, lastWeekImpressionCost float64
	lastWeekStart := time.Now().AddDate(0, 0, -7-int(time.Now().Weekday())).Format("2006-01-02")
	lastWeekEnd := time.Now().AddDate(0, 0, -int(time.Now().Weekday())-1).Format("2006-01-02")

	global.GVA_DB.Model(&meta.AdClick{}).
		Select("COALESCE(SUM(cost), 0)").
		Joins("JOIN meta_ads ON meta_ad_clicks.ad_id = meta_ads.id").
		Where("meta_ads.user_id = ? AND DATE(click_time) >= ? AND DATE(click_time) <= ?", userID, lastWeekStart, lastWeekEnd).
		Scan(&lastWeekClickCost)

	global.GVA_DB.Model(&meta.AdImpression{}).
		Select("COALESCE(SUM(cost), 0)").
		Joins("JOIN meta_ads ON meta_ad_impressions.ad_id = meta_ads.id").
		Where("meta_ads.user_id = ? AND DATE(impression_time) >= ? AND DATE(impression_time) <= ?", userID, lastWeekStart, lastWeekEnd).
		Scan(&lastWeekImpressionCost)

	lastWeekTotalCost := lastWeekClickCost + lastWeekImpressionCost

	trend := &meta.Trend{
		TimeRange: "与上周对比",
	}
	if lastWeekTotalCost > 0 {
		trend.Percent = (totalCost - lastWeekTotalCost) / lastWeekTotalCost * 100
		trend.IsUp = totalCost > lastWeekTotalCost
		trend.Value = totalCost - lastWeekTotalCost
	}

	return totalCost, trend, nil
}

func (dashboardService *DashboardService) getMonthlyConsume(userID uint, authorityID uint) (float64, *meta.Trend, error) {
	if authorityID != 9528 {
		return 0, nil, fmt.Errorf("只有广告主可以查看消费数据")
	}

	var totalCost float64
	monthStart := time.Now().Format("2006-01") + "-01"

	// 统计本月广告消费
	var clickCost, impressionCost float64

	global.GVA_DB.Model(&meta.AdClick{}).
		Select("COALESCE(SUM(cost), 0)").
		Joins("JOIN meta_ads ON meta_ad_clicks.ad_id = meta_ads.id").
		Where("meta_ads.user_id = ? AND DATE(click_time) >= ?", userID, monthStart).
		Scan(&clickCost)

	global.GVA_DB.Model(&meta.AdImpression{}).
		Select("COALESCE(SUM(cost), 0)").
		Joins("JOIN meta_ads ON meta_ad_impressions.ad_id = meta_ads.id").
		Where("meta_ads.user_id = ? AND DATE(impression_time) >= ?", userID, monthStart).
		Scan(&impressionCost)

	totalCost = clickCost + impressionCost

	// 计算趋势（与上月对比）
	var lastMonthClickCost, lastMonthImpressionCost float64
	lastMonth := time.Now().AddDate(0, -1, 0)
	lastMonthStart := lastMonth.Format("2006-01") + "-01"
	lastMonthEnd := time.Date(lastMonth.Year(), lastMonth.Month()+1, 0, 0, 0, 0, 0, lastMonth.Location()).Format("2006-01-02")

	global.GVA_DB.Model(&meta.AdClick{}).
		Select("COALESCE(SUM(cost), 0)").
		Joins("JOIN meta_ads ON meta_ad_clicks.ad_id = meta_ads.id").
		Where("meta_ads.user_id = ? AND DATE(click_time) >= ? AND DATE(click_time) <= ?", userID, lastMonthStart, lastMonthEnd).
		Scan(&lastMonthClickCost)

	global.GVA_DB.Model(&meta.AdImpression{}).
		Select("COALESCE(SUM(cost), 0)").
		Joins("JOIN meta_ads ON meta_ad_impressions.ad_id = meta_ads.id").
		Where("meta_ads.user_id = ? AND DATE(impression_time) >= ? AND DATE(impression_time) <= ?", userID, lastMonthStart, lastMonthEnd).
		Scan(&lastMonthImpressionCost)

	lastMonthTotalCost := lastMonthClickCost + lastMonthImpressionCost

	trend := &meta.Trend{
		TimeRange: "与上月对比",
	}
	if lastMonthTotalCost > 0 {
		trend.Percent = (totalCost - lastMonthTotalCost) / lastMonthTotalCost * 100
		trend.IsUp = totalCost > lastMonthTotalCost
		trend.Value = totalCost - lastMonthTotalCost
	}

	return totalCost, trend, nil
}

// 收益相关统计方法（APP开发者）
func (dashboardService *DashboardService) getDailyRevenue(userID uint, authorityID uint) (float64, *meta.Trend, error) {
	if authorityID != 8881 {
		return 0, nil, fmt.Errorf("只有APP开发者可以查看收益数据")
	}

	var totalRevenue float64
	today := time.Now().Format("2006-01-02")

	// 统计今日广告收益（基于该用户APP的广告展示和点击）
	global.GVA_DB.Model(&meta.AdStatistics{}).
		Select("COALESCE(SUM(revenue), 0)").
		Where("user_id = ? AND DATE(stat_date) = ?", userID, today).
		Scan(&totalRevenue)

	// 计算趋势（与昨日对比）
	var yesterdayRevenue float64
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	global.GVA_DB.Model(&meta.AdStatistics{}).
		Select("COALESCE(SUM(revenue), 0)").
		Where("user_id = ? AND DATE(stat_date) = ?", userID, yesterday).
		Scan(&yesterdayRevenue)

	trend := &meta.Trend{
		TimeRange: "与昨日对比",
	}
	if yesterdayRevenue > 0 {
		trend.Percent = (totalRevenue - yesterdayRevenue) / yesterdayRevenue * 100
		trend.IsUp = totalRevenue > yesterdayRevenue
		trend.Value = totalRevenue - yesterdayRevenue
	}

	return totalRevenue, trend, nil
}

func (dashboardService *DashboardService) getWeeklyRevenue(userID uint, authorityID uint) (float64, *meta.Trend, error) {
	if authorityID != 8881 {
		return 0, nil, fmt.Errorf("只有APP开发者可以查看收益数据")
	}

	var totalRevenue float64
	weekStart := time.Now().AddDate(0, 0, -int(time.Now().Weekday())).Format("2006-01-02")

	global.GVA_DB.Model(&meta.AdStatistics{}).
		Select("COALESCE(SUM(revenue), 0)").
		Where("user_id = ? AND DATE(stat_date) >= ?", userID, weekStart).
		Scan(&totalRevenue)

	// 计算趋势（与上周对比）
	var lastWeekRevenue float64
	lastWeekStart := time.Now().AddDate(0, 0, -7-int(time.Now().Weekday())).Format("2006-01-02")
	lastWeekEnd := time.Now().AddDate(0, 0, -int(time.Now().Weekday())-1).Format("2006-01-02")
	global.GVA_DB.Model(&meta.AdStatistics{}).
		Select("COALESCE(SUM(revenue), 0)").
		Where("user_id = ? AND DATE(stat_date) >= ? AND DATE(stat_date) <= ?", userID, lastWeekStart, lastWeekEnd).
		Scan(&lastWeekRevenue)

	trend := &meta.Trend{
		TimeRange: "与上周对比",
	}
	if lastWeekRevenue > 0 {
		trend.Percent = (totalRevenue - lastWeekRevenue) / lastWeekRevenue * 100
		trend.IsUp = totalRevenue > lastWeekRevenue
		trend.Value = totalRevenue - lastWeekRevenue
	}

	return totalRevenue, trend, nil
}

func (dashboardService *DashboardService) getMonthlyRevenue(userID uint, authorityID uint) (float64, *meta.Trend, error) {
	if authorityID != 8881 {
		return 0, nil, fmt.Errorf("只有APP开发者可以查看收益数据")
	}

	var totalRevenue float64
	monthStart := time.Now().Format("2006-01") + "-01"

	global.GVA_DB.Model(&meta.AdStatistics{}).
		Select("COALESCE(SUM(revenue), 0)").
		Where("user_id = ? AND DATE(stat_date) >= ?", userID, monthStart).
		Scan(&totalRevenue)

	// 计算趋势（与上月对比）
	var lastMonthRevenue float64
	lastMonth := time.Now().AddDate(0, -1, 0)
	lastMonthStart := lastMonth.Format("2006-01") + "-01"
	lastMonthEnd := time.Date(lastMonth.Year(), lastMonth.Month()+1, 0, 0, 0, 0, 0, lastMonth.Location()).Format("2006-01-02")
	global.GVA_DB.Model(&meta.AdStatistics{}).
		Select("COALESCE(SUM(revenue), 0)").
		Where("user_id = ? AND DATE(stat_date) >= ? AND DATE(stat_date) <= ?", userID, lastMonthStart, lastMonthEnd).
		Scan(&lastMonthRevenue)

	trend := &meta.Trend{
		TimeRange: "与上月对比",
	}
	if lastMonthRevenue > 0 {
		trend.Percent = (totalRevenue - lastMonthRevenue) / lastMonthRevenue * 100
		trend.IsUp = totalRevenue > lastMonthRevenue
		trend.Value = totalRevenue - lastMonthRevenue
	}

	return totalRevenue, trend, nil
}

// 用户增长趋势图表数据
func (dashboardService *DashboardService) getUserGrowthTrend() (interface{}, error) {
	type ChartData struct {
		XAxis []string `json:"xAxis"`
		Data  []int64  `json:"data"`
	}

	var chartData ChartData

	// 获取最近7天的用户增长数据
	for i := 6; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i)
		dateStr := date.Format("2006-01-02")

		var count int64
		global.GVA_DB.Model(&system.SysUser{}).
			Where("DATE(created_at) = ?", dateStr).
			Count(&count)

		chartData.XAxis = append(chartData.XAxis, date.Format("01-02"))
		chartData.Data = append(chartData.Data, count)
	}

	return chartData, nil
}
