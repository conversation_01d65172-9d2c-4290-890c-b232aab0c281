package meta

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"sort"
	"strings"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
	metaRes "github.com/flipped-aurora/gin-vue-admin/server/model/meta/response"
	"gorm.io/gorm"
)

type SdkService struct{}

// GetAds 获取广告列表
func (this *SdkService) GetAds(ctx context.Context, req *metaReq.GetAdsRequest) (*metaRes.GetAdsResponse, error) {
	// 1. 查找APP（可选，如果提供了package_id）
	var app *meta.App
	var appIdInt *int

	if req.PackageId != "" {
		var appRecord meta.App
		if err := global.GVA_DB.Where("package_id = ? AND status = ? AND ad_enabled = ?",
			req.PackageId, true, true).First(&appRecord).Error; err != nil {
			return nil, fmt.Errorf("应用不存在或未启用广告: %v", err)
		}
		app = &appRecord
		appId := int(app.ID)
		appIdInt = &appId

		// 2. 检查今天是否第一次请求，如果是则日活+1
		if err := this.updateDailyActiveUsers(ctx, appIdInt); err != nil {
			global.GVA_LOG.Error("更新日活失败: " + err.Error())
		}
	}

	// 3. 获取符合条件的广告投放计划
	campaigns, err := this.getEligibleCampaigns(ctx, appIdInt, req.AdPositionId, req.AdTypeId)
	if err != nil {
		return nil, fmt.Errorf("获取投放计划失败: %v", err)
	}

	if 1 > len(campaigns) {
		return &metaRes.GetAdsResponse{Ads: []metaRes.AdInfo{}}, nil
	}

	// 4. 根据出价排序投放计划
	this.sortCampaignsByBid(campaigns)

	// 5. 获取每个计划的广告并筛选
	var allAds = make([]metaRes.AdInfo, 0)
	for _, campaign := range campaigns {
		ads, err := this.getAdsFromCampaign(ctx, campaign, req.AdPositionId, req.AdTypeId)
		if err != nil {
			global.GVA_LOG.Error("获取计划广告失败: " + err.Error())
			continue
		}
		if len(ads) > 0 {
			allAds = append(allAds, ads...)
		}
	}

	// 6. 使用加权平均法选择3个广告
	selectedAds := this.selectAdsByWeight(allAds, 20)

	return &metaRes.GetAdsResponse{Ads: selectedAds}, nil
}

// ReportEvents 上报广告事件
func (this *SdkService) ReportEvents(ctx context.Context, req *metaReq.AdEventRequest) (*metaRes.AdEventResponse, error) {
	response := &metaRes.AdEventResponse{
		Success:        true,
		ProcessedCount: 0,
		FailedEvents:   []metaRes.FailedEventInfo{},
	}

	var app meta.App
	if err := global.GVA_DB.Where("package_id = ?", req.PackageId).First(&app).Error; err != nil {
		fmt.Errorf("应用不存在: %v", err)
	}

	for i, event := range req.Events {
		if err := this.processAdEvent(ctx, &event, &app); err != nil {
			response.FailedEvents = append(response.FailedEvents, metaRes.FailedEventInfo{
				Index:      i,
				Reason:     err.Error(),
				AdId:       event.AdId,
				CampaignId: event.CampaignId,
			})
			continue
		}
		response.ProcessedCount++
	}

	if len(response.FailedEvents) > 0 {
		response.Success = false
		response.Message = fmt.Sprintf("部分事件处理失败，成功: %d, 失败: %d",
			response.ProcessedCount, len(response.FailedEvents))
	} else {
		response.Message = "所有事件处理成功"
	}

	return response, nil
}

// updateDailyActiveUsers 更新日活用户数
func (this *SdkService) updateDailyActiveUsers(ctx context.Context, appId *int) error {
	today := time.Now().Format("2006-01-02")
	redisKey := fmt.Sprintf("daily_active:%s:%d", today, *appId)

	// 检查今天是否已经记录过
	exists, err := global.GVA_REDIS.Exists(ctx, redisKey).Result()
	if err != nil {
		return err
	}

	if exists == 0 {
		// 第一次请求，设置标记并增加日活
		if err := global.GVA_REDIS.Set(ctx, redisKey, "1", 24*time.Hour).Err(); err != nil {
			return err
		}

		// 更新统计表
		return this.incrementDashubStat(ctx, meta.DASHUB_DAILY_ACTIVE_USERS, *appId, today, 1)
	}

	return nil
}

// getEligibleCampaigns 获取符合条件的投放计划
func (this *SdkService) getEligibleCampaigns(ctx context.Context, appId *int, adPositionId, adTypeId int) ([]meta.Campaign, error) {
	var campaigns []meta.Campaign
	now := time.Now()

	// 基本条件查询
	query := global.GVA_DB.Where("status = ? AND start_time <= ? AND end_time >= ? AND deleted_at IS NULL",
		"running", now, now)

	if err := query.Find(&campaigns).Error; err != nil {
		return nil, err
	}

	// 过滤符合目标APP的计划
	var eligibleCampaigns []meta.Campaign
	for _, campaign := range campaigns {
		// 检查是否在Redis中被暂停
		if this.isCampaignPausedInRedis(ctx, campaign.ID) {
			continue
		}

		// 检查目标APP
		if this.checkApp(campaign.TargetApps, appId) {
			eligibleCampaigns = append(eligibleCampaigns, campaign)
		}
	}

	return eligibleCampaigns, nil
}

// sortCampaignsByBid 根据出价排序投放计划
func (this *SdkService) sortCampaignsByBid(campaigns []meta.Campaign) {
	sort.Slice(campaigns, func(i, j int) bool {
		bidI := this.getCampaignBid(campaigns[i])
		bidJ := this.getCampaignBid(campaigns[j])
		return bidI > bidJ // 价高者优先
	})
}

// getCampaignBid 获取投放计划的出价
func (this *SdkService) getCampaignBid(campaign meta.Campaign) float64 {
	if campaign.BidAmount != nil && *campaign.BidAmount > 0 {
		return *campaign.BidAmount
	}
	// 如果没有出价，返回默认出价（可以根据广告类型等计算）
	return this.calculateDefaultBid(campaign)
}

// calculateDefaultBid 计算默认出价
func (this *SdkService) calculateDefaultBid(campaign meta.Campaign) float64 {
	// 简单的默认出价逻辑，可以根据需要调整
	if campaign.BidType != nil && *campaign.BidType == "CPM" {
		return 1.0 // CPM默认1元
	}
	return 0.1 // CPC默认0.1元
}

func (this *SdkService) coverImage(url string) string {
	return this.cdnImage(url)
}
func (this *SdkService) cdnImage(url string) string {
	if strings.HasPrefix(url, "http") {
		return url
	}

	return "https://cdn.amountads.com/" + url
}

// getAdsFromCampaign 从投放计划获取广告
func (this *SdkService) getAdsFromCampaign(ctx context.Context, campaign meta.Campaign, adPositionId, adTypeId int) ([]metaRes.AdInfo, error) {
	// 解析广告ID列表
	var adIds []int
	if err := json.Unmarshal(campaign.AdIds, &adIds); err != nil {
		return nil, err
	}

	var ads []meta.Ad
	if err := global.GVA_DB.Where("id IN ? AND status = ? AND deleted_at IS NULL",
		adIds, 5).Find(&ads).Error; err != nil { // status=5表示已上架
		return nil, err
	}

	var result []metaRes.AdInfo
	for _, ad := range ads {
		if adTypeId > 0 && adTypeId != *ad.AdTypeId {
			//过滤汪符合的广告类型
			continue
		}
		// 检查广告位
		if this.isAdPositionMatched(ad.AdPositions, adPositionId) {
			result = append(result, metaRes.AdInfo{
				ID:          ad.ID,
				CampaignId:  int(campaign.ID),
				Title:       *ad.Title,
				Description: *ad.Description,
				CoverUrl:    this.coverImage(ad.MediaUrl),
				MediaUrl:    this.cdnImage(ad.MediaUrl),
				ClickUrl:    *ad.ClickUrl,
				AdTypeId:    *ad.AdTypeId,
				AdActionId:  *ad.AdActionId,
				BidAmount:   this.getCampaignBid(campaign),
				//AdPositionId: adPositionId,
				//Name:         *ad.Name,
				//BidType:      *campaign.BidType,
				//CreatedAt:    ad.CreatedAt,
			})
		}
	}

	return result, nil
}

// selectAdsByWeight 使用加权平均法选择广告
func (this *SdkService) selectAdsByWeight(ads []metaRes.AdInfo, count int) []metaRes.AdInfo {
	if len(ads) <= count {
		return ads
	}

	// 计算权重（基于出价）
	totalWeight := 0.0
	weights := make([]float64, len(ads))
	for i, ad := range ads {
		weight := ad.BidAmount
		if weight <= 0 {
			weight = 0.01 // 最小权重
		}
		weights[i] = weight
		totalWeight += weight
	}

	// 加权随机选择
	selected := make([]metaRes.AdInfo, 0, count)
	usedIndices := make(map[int]bool)

	for len(selected) < count && len(selected) < len(ads) {
		r := rand.Float64() * totalWeight
		cumWeight := 0.0

		for i, weight := range weights {
			if usedIndices[i] {
				continue
			}
			cumWeight += weight
			if r <= cumWeight {
				selected = append(selected, ads[i])
				usedIndices[i] = true
				totalWeight -= weight
				break
			}
		}
	}

	return selected
}

// 辅助方法
func (this *SdkService) isCampaignPausedInRedis(ctx context.Context, campaignId uint) bool {
	key := fmt.Sprintf("campaign_paused:%d", campaignId)
	exists, _ := global.GVA_REDIS.Exists(ctx, key).Result()
	return exists > 0
}

func (this *SdkService) checkApp(appJson []byte, appId *int) bool {
	// 如果投放计划没有设置目标APP，则适用于所有APP
	if 3 > len(appJson) {
		return true
	}

	// 如果请求中没有提供appId，但计划中设置了目标APP，则不匹配
	if appId == nil {
		return false
	}

	var appIds []int

	_ = json.Unmarshal(appJson, &appIds)

	// 如果计划的目标APP列表为空，表示适用于所有APP
	if 1 > len(appIds) {
		return true
	}

	// 检查当前APP是否在目标列表中
	for _, id := range appIds {
		if id == *appId {
			return true
		}
	}
	return false
}

func (this *SdkService) isAdPositionMatched(adPositions []byte, positionId int) bool {
	if 3 > len(adPositions) || 1 > positionId {
		//没有位置的限制
		return true
	}

	var positionIds []int
	_ = json.Unmarshal(adPositions, &positionIds)

	for _, id := range positionIds {
		if id == positionId {
			return true
		}
	}
	return false
}

// processAdEvent 处理单个广告事件
func (this *SdkService) processAdEvent(ctx context.Context, event *metaReq.AdEvent, app *meta.App) error {
	// 1. 验证广告和投放计划是否存在
	var ad meta.Ad
	if err := global.GVA_DB.Where("id = ?", event.AdId).First(&ad).Error; err != nil {
		return fmt.Errorf("广告不存在: %v", err)
	}

	var campaign meta.Campaign
	if err := global.GVA_DB.Where("id = ?", event.CampaignId).First(&campaign).Error; err != nil {
		return fmt.Errorf("投放计划不存在: %v", err)
	}

	// 2. 验证APP
	//var app meta.App
	//if err := global.GVA_DB.Where("package_id = ?", event.PackageId).First(&app).Error; err != nil {
	//	return fmt.Errorf("应用不存在: %v", err)
	//}

	// 3. 记录事件
	if err := this.recordAdEvent(ctx, event, &ad, &campaign, app); err != nil {
		return fmt.Errorf("记录事件失败: %v", err)
	}

	// 4. 更新统计数据
	if err := this.updateStatistics(ctx, event, &ad, &campaign, app); err != nil {
		global.GVA_LOG.Error("更新统计失败: " + err.Error())
	}

	// 5. 处理计费
	if err := this.processBilling(ctx, event, &ad, &campaign, app); err != nil {
		return fmt.Errorf("计费处理失败: %v", err)
	}

	return nil
}

// recordAdEvent 记录广告事件
func (this *SdkService) recordAdEvent(ctx context.Context, event *metaReq.AdEvent, ad *meta.Ad, campaign *meta.Campaign, app *meta.App) error {
	eventTime := time.Now()
	if event.EventTime > 0 {
		eventTime = time.Unix(int64(event.EventTime), 0)
	}

	// 根据行为类型记录不同的事件
	switch event.ActionType {
	case 1: // 展示
		appIdInt := int(app.ID)
		impression := meta.AdImpression{
			AdId:           &event.AdId,
			CampaignId:     &event.CampaignId,
			AppId:          &appIdInt,
			AdPositionId:   &event.AdPositionId,
			ImpressionTime: &eventTime,
			Cost:           &[]float64{this.calculateEventCost(campaign, "impression")}[0],
		}

		// 设置设备信息
		//if event.DeviceInfo != nil {
		//	impression.DeviceId = &event.DeviceInfo.DeviceId
		//	impression.Platform = &event.DeviceInfo.Platform
		//	impression.Country = &event.DeviceInfo.Country
		//	impression.Province = &event.DeviceInfo.Province
		//	impression.City = &event.DeviceInfo.City
		//}

		return global.GVA_DB.Create(&impression).Error

	case 2: // 点击
		appIdInt := int(app.ID)
		click := meta.AdClick{
			AdId:         &event.AdId,
			CampaignId:   &event.CampaignId,
			AppId:        &appIdInt,
			AdPositionId: &event.AdPositionId,
			ClickTime:    &eventTime,
			Cost:         &[]float64{this.calculateEventCost(campaign, "click")}[0],
			IsValid:      &[]bool{true}[0],
		}

		// 设置设备信息
		//if event.DeviceInfo != nil {
		//	click.DeviceId = &event.DeviceInfo.DeviceId
		//	click.Platform = &event.DeviceInfo.Platform
		//	click.Country = &event.DeviceInfo.Country
		//	click.Province = &event.DeviceInfo.Province
		//	click.City = &event.DeviceInfo.City
		//}

		return global.GVA_DB.Create(&click).Error

	default:
		return fmt.Errorf("不支持的行为类型: %d", event.ActionType)
	}
}

// updateStatistics 更新统计数据
func (this *SdkService) updateStatistics(ctx context.Context, event *metaReq.AdEvent, ad *meta.Ad, campaign *meta.Campaign, app *meta.App) error {
	today := time.Now().Format("2006-01-02")

	// 更新APP统计
	appIdInt := int(app.ID)
	if err := this.updateAppStatistics(ctx, appIdInt, event.ActionType, today); err != nil {
		return err
	}

	// 更新广告统计
	if err := this.updateAdStatistics(ctx, event.AdId, event.ActionType, today); err != nil {
		return err
	}

	// 更新投放计划统计
	if err := this.updateCampaignStatistics(ctx, event.CampaignId, event.ActionType, today); err != nil {
		return err
	}

	return nil
}

// processBilling 处理计费
func (this *SdkService) processBilling(ctx context.Context, event *metaReq.AdEvent, ad *meta.Ad, campaign *meta.Campaign, app *meta.App) error {
	// 计算费用
	cost := this.calculateEventCost(campaign, this.getActionTypeName(event.ActionType))
	if cost <= 0 {
		return nil // 无需扣费
	}

	// 获取广告主账户
	var account meta.FinanceAccount
	if err := global.GVA_DB.Where("user_id = ? AND account_type = ?",
		campaign.UserId, "advertiser").First(&account).Error; err != nil {
		return fmt.Errorf("广告主账户不存在: %v", err)
	}

	// 检查余额
	if *account.Balance < cost {
		// 余额不足，暂停所有计划
		if err := this.pauseAllUserCampaigns(ctx, *campaign.UserId); err != nil {
			global.GVA_LOG.Error("暂停用户计划失败: " + err.Error())
		}
		return fmt.Errorf("账户余额不足")
	}

	// 扣费
	if err := this.deductBalance(ctx, &account, cost, event, ad, campaign, app); err != nil {
		return err
	}

	// 检查预算限制
	if err := this.checkBudgetLimits(ctx, campaign, cost); err != nil {
		global.GVA_LOG.Error("预算检查失败: " + err.Error())
	}

	return nil
}

// calculateEventCost 计算事件费用
func (this *SdkService) calculateEventCost(campaign *meta.Campaign, actionType string) float64 {
	if campaign.BidType == nil || campaign.BidAmount == nil {
		return 0
	}

	switch *campaign.BidType {
	case "CPC":
		if actionType == "click" {
			return *campaign.BidAmount
		}
		return 0
	case "CPM":
		if actionType == "impression" {
			return *campaign.BidAmount / 1000 // CPM按千次计费
		}
		return 0
	default:
		return 0
	}
}

// getActionTypeName 获取行为类型名称
func (this *SdkService) getActionTypeName(actionType int) string {
	switch actionType {
	case 1:
		return "impression"
	case 2:
		return "click"
	default:
		return "unknown"
	}
}

// deductBalance 扣除余额
func (this *SdkService) deductBalance(ctx context.Context, account *meta.FinanceAccount, cost float64, event *metaReq.AdEvent, ad *meta.Ad, campaign *meta.Campaign, app *meta.App) error {
	// 开始事务
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// 更新账户余额
		balanceBefore := *account.Balance
		balanceAfter := balanceBefore - cost

		if err := tx.Model(account).Updates(map[string]interface{}{
			"balance":       balanceAfter,
			"total_consume": *account.TotalConsume + cost,
		}).Error; err != nil {
			return err
		}

		// 获取APP ID
		var appId *int
		if app != nil {
			appIdInt := int(app.ID)
			appId = &appIdInt
		}

		// 创建消费记录
		consumeRecord := meta.ConsumeRecord{
			UserId:        campaign.UserId,
			AdId:          &event.AdId,
			CampaignId:    &event.CampaignId,
			ConsumeType:   &[]string{this.getActionTypeName(event.ActionType)}[0],
			Amount:        &cost,
			Currency:      account.Currency,
			BidType:       campaign.BidType,
			BidAmount:     campaign.BidAmount,
			AppId:         appId,
			AdPositionId:  &event.AdPositionId,
			ConsumeTime:   &[]time.Time{time.Now()}[0],
			BalanceBefore: &balanceBefore,
			BalanceAfter:  &balanceAfter,
			Status:        &[]string{"success"}[0],
		}

		return tx.Create(&consumeRecord).Error
	})
}

// pauseAllUserCampaigns 暂停用户所有计划
func (this *SdkService) pauseAllUserCampaigns(ctx context.Context, userId int) error {
	// 更新数据库状态
	if err := global.GVA_DB.Model(&meta.Campaign{}).
		Where("user_id = ? AND status = ?", userId, "running").
		Update("status", "paused").Error; err != nil {
		return err
	}

	// 在Redis中标记暂停
	var campaigns []meta.Campaign
	global.GVA_DB.Where("user_id = ?", userId).Find(&campaigns)

	for _, campaign := range campaigns {
		key := fmt.Sprintf("campaign_paused:%d", campaign.ID)
		global.GVA_REDIS.Set(ctx, key, "1", 24*time.Hour)
	}

	return nil
}

// checkBudgetLimits 检查预算限制
func (this *SdkService) checkBudgetLimits(ctx context.Context, campaign *meta.Campaign, cost float64) error {
	// 检查总预算
	if campaign.TotalBudget != nil && *campaign.TotalBudget > 0 {
		totalSpent := this.getTotalSpent(campaign.ID)
		if totalSpent+cost >= *campaign.TotalBudget {
			// 暂停计划
			global.GVA_DB.Model(campaign).Update("status", "paused")
		}
	}

	// 检查日预算
	if campaign.DailyBudget != nil && *campaign.DailyBudget > 0 {
		today := time.Now().Format("2006-01-02")
		dailySpent := this.getDailySpent(campaign.ID, today)
		if dailySpent+cost >= *campaign.DailyBudget {
			// 在Redis中标记今日暂停
			key := fmt.Sprintf("campaign_daily_paused:%d:%s", campaign.ID, today)
			global.GVA_REDIS.Set(ctx, key, "1", 24*time.Hour)
		}
	}

	return nil
}

// 辅助统计方法
func (this *SdkService) updateAppStatistics(ctx context.Context, appId int, actionType int, day string) error {
	// 实现APP统计更新
	return nil
}

func (this *SdkService) updateAdStatistics(ctx context.Context, adId int, actionType int, day string) error {
	// 实现广告统计更新
	return nil
}

func (this *SdkService) updateCampaignStatistics(ctx context.Context, campaignId int, actionType int, day string) error {
	// 实现投放计划统计更新
	return nil
}

func (this *SdkService) getTotalSpent(campaignId uint) float64 {
	var total float64
	global.GVA_DB.Model(&meta.ConsumeRecord{}).
		Where("campaign_id = ?", campaignId).
		Select("COALESCE(SUM(amount), 0)").Scan(&total)
	return total
}

func (this *SdkService) getDailySpent(campaignId uint, day string) float64 {
	var total float64
	global.GVA_DB.Model(&meta.ConsumeRecord{}).
		Where("campaign_id = ? AND DATE(consume_time) = ?", campaignId, day).
		Select("COALESCE(SUM(amount), 0)").Scan(&total)
	return total
}

func (this *SdkService) incrementDashubStat(ctx context.Context, kind int, target int, day string, increment float64) error {
	// 使用Redis实现统计增量
	key := fmt.Sprintf("dashub_stat:%d:%d:%s", kind, target, day)
	return global.GVA_REDIS.IncrByFloat(ctx, key, increment).Err()
}
