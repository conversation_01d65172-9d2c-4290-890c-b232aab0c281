
package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type DeviceDataService struct {}
// CreateDeviceData 创建设备数据记录
// Author [yourname](https://github.com/yourname)
func (devicedataService *DeviceDataService) CreateDeviceData(ctx context.Context, devicedata *meta.DeviceData) (err error) {
	err = global.GVA_DB.Create(devicedata).Error
	return err
}

// DeleteDeviceData 删除设备数据记录
// Author [yourname](https://github.com/yourname)
func (devicedataService *DeviceDataService)DeleteDeviceData(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&meta.DeviceData{},"id = ?",ID).Error
	return err
}

// DeleteDeviceDataByIds 批量删除设备数据记录
// Author [yourname](https://github.com/yourname)
func (devicedataService *DeviceDataService)DeleteDeviceDataByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.DeviceData{},"id in ?",IDs).Error
	return err
}

// UpdateDeviceData 更新设备数据记录
// Author [yourname](https://github.com/yourname)
func (devicedataService *DeviceDataService)UpdateDeviceData(ctx context.Context, devicedata meta.DeviceData) (err error) {
	err = global.GVA_DB.Model(&meta.DeviceData{}).Where("id = ?",devicedata.ID).Updates(&devicedata).Error
	return err
}

// GetDeviceData 根据ID获取设备数据记录
// Author [yourname](https://github.com/yourname)
func (devicedataService *DeviceDataService)GetDeviceData(ctx context.Context, ID string) (devicedata meta.DeviceData, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&devicedata).Error
	return
}
// GetDeviceDataInfoList 分页获取设备数据记录
// Author [yourname](https://github.com/yourname)
func (devicedataService *DeviceDataService)GetDeviceDataInfoList(ctx context.Context, info metaReq.DeviceDataSearch) (list []meta.DeviceData, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&meta.DeviceData{})
    var devicedatas []meta.DeviceData
    // 如果有条件搜索 下方会自动创建搜索语句
    if len(info.CreatedAtRange) == 2 {
     db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
    }
    
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }
        var OrderStr string
        orderMap := make(map[string]bool)
           orderMap["ID"] = true
           orderMap["CreatedAt"] = true
         	orderMap["first_seen_time"] = true
         	orderMap["last_seen_time"] = true
       if orderMap[info.Sort] {
          OrderStr = info.Sort
          if info.Order == "descending" {
             OrderStr = OrderStr + " desc"
          }
          db = db.Order(OrderStr)
       }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&devicedatas).Error
	return  devicedatas, total, err
}
func (devicedataService *DeviceDataService)GetDeviceDataDataSource(ctx context.Context) (res map[string][]map[string]any, err error) {
	res = make(map[string][]map[string]any)
	
	   app_id := make([]map[string]any, 0)
	   
       
       global.GVA_DB.Table("meta_apps").Where("deleted_at IS NULL").Select("name as label,id as value").Scan(&app_id)
	   res["app_id"] = app_id
	return
}
func (devicedataService *DeviceDataService)GetDeviceDataPublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
