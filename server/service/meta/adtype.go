
package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
    metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type AdTypeService struct {}
// CreateAdType 创建广告类型管理记录
// Author [yourname](https://github.com/yourname)
func (adtypeService *AdTypeService) CreateAdType(ctx context.Context, adtype *meta.AdType) (err error) {
	err = global.GVA_DB.Create(adtype).Error
	return err
}

// DeleteAdType 删除广告类型管理记录
// Author [yourname](https://github.com/yourname)
func (adtypeService *AdTypeService)DeleteAdType(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&meta.AdType{},"id = ?",id).Error
	return err
}

// DeleteAdTypeByIds 批量删除广告类型管理记录
// Author [yourname](https://github.com/yourname)
func (adtypeService *AdTypeService)DeleteAdTypeByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.AdType{},"id in ?",ids).Error
	return err
}

// UpdateAdType 更新广告类型管理记录
// Author [yourname](https://github.com/yourname)
func (adtypeService *AdTypeService)UpdateAdType(ctx context.Context, adtype meta.AdType) (err error) {
	err = global.GVA_DB.Model(&meta.AdType{}).Where("id = ?",adtype.ID).Updates(&adtype).Error
	return err
}

// GetAdType 根据id获取广告类型管理记录
// Author [yourname](https://github.com/yourname)
func (adtypeService *AdTypeService)GetAdType(ctx context.Context, id string) (adtype meta.AdType, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&adtype).Error
	return
}
// GetAdTypeInfoList 分页获取广告类型管理记录
// Author [yourname](https://github.com/yourname)
func (adtypeService *AdTypeService)GetAdTypeInfoList(ctx context.Context, info metaReq.AdTypeSearch) (list []meta.AdType, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
    // 创建db
	db := global.GVA_DB.Model(&meta.AdType{})
    var adtypes []meta.AdType
    // 如果有条件搜索 下方会自动创建搜索语句
    
	err = db.Count(&total).Error
	if err!=nil {
    	return
    }
        var OrderStr string
        orderMap := make(map[string]bool)
         	orderMap["name"] = true
       if orderMap[info.Sort] {
          OrderStr = info.Sort
          if info.Order == "descending" {
             OrderStr = OrderStr + " desc"
          }
          db = db.Order(OrderStr)
       }

	if limit != 0 {
       db = db.Limit(limit).Offset(offset)
    }

	err = db.Find(&adtypes).Error
	return  adtypes, total, err
}
func (adtypeService *AdTypeService)GetAdTypePublic(ctx context.Context) {
    // 此方法为获取数据源定义的数据
    // 请自行实现
}
