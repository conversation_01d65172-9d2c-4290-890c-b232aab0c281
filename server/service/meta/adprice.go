package meta

import (
	"context"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
	metaReq "github.com/flipped-aurora/gin-vue-admin/server/model/meta/request"
)

type AdPriceService struct{}

// CreateAdPrice 创建广告价格管理记录
// Author [yourname](https://github.com/yourname)
func (adpriceService *AdPriceService) CreateAdPrice(ctx context.Context, adprice *meta.AdPrice) (err error) {
	err = global.GVA_DB.Create(adprice).Error
	return err
}

// DeleteAdPrice 删除广告价格管理记录
// Author [yourname](https://github.com/yourname)
func (adpriceService *AdPriceService) DeleteAdPrice(ctx context.Context, id string) (err error) {
	err = global.GVA_DB.Delete(&meta.AdPrice{}, "id = ?", id).Error
	return err
}

// DeleteAdPriceByIds 批量删除广告价格管理记录
// Author [yourname](https://github.com/yourname)
func (adpriceService *AdPriceService) DeleteAdPriceByIds(ctx context.Context, ids []string) (err error) {
	err = global.GVA_DB.Delete(&[]meta.AdPrice{}, "id in ?", ids).Error
	return err
}

// UpdateAdPrice 更新广告价格管理记录
// Author [yourname](https://github.com/yourname)
func (adpriceService *AdPriceService) UpdateAdPrice(ctx context.Context, adprice meta.AdPrice) (err error) {
	err = global.GVA_DB.Model(&meta.AdPrice{}).Where("id = ?", adprice.ID).Updates(&adprice).Error
	return err
}

// GetAdPrice 根据id获取广告价格管理记录
// Author [yourname](https://github.com/yourname)
func (adpriceService *AdPriceService) GetAdPrice(ctx context.Context, id string) (adprice meta.AdPrice, err error) {
	err = global.GVA_DB.Where("id = ?", id).First(&adprice).Error
	return
}

// GetAdPriceInfoList 分页获取广告价格管理记录
// Author [yourname](https://github.com/yourname)
func (adpriceService *AdPriceService) GetAdPriceInfoList(ctx context.Context, info metaReq.AdPriceSearch) (list []meta.AdPrice, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&meta.AdPrice{})
	var adprices []meta.AdPrice
	// 如果有条件搜索 下方会自动创建搜索语句

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	var OrderStr string
	orderMap := make(map[string]bool)
	orderMap["base_price"] = true
	orderMap["effective_time"] = true
	if orderMap[info.Sort] {
		OrderStr = info.Sort
		if info.Order == "descending" {
			OrderStr = OrderStr + " desc"
		}
		db = db.Order(OrderStr)
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&adprices).Error
	return adprices, total, err
}
func (adpriceService *AdPriceService) GetAdPriceDataSource(ctx context.Context) (res map[string][]map[string]any, err error) {
	res = make(map[string][]map[string]any)

	ad_action_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ad_actions").Select("name as label,id as value").Scan(&ad_action_id)
	res["ad_action_id"] = ad_action_id
	ad_position_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ad_positions").Select("name as label,id as value").Scan(&ad_position_id)
	res["ad_position_id"] = ad_position_id
	ad_type_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_ad_types").Select("name as label,id as value").Scan(&ad_type_id)
	res["ad_type_id"] = ad_type_id
	app_id := make([]map[string]any, 0)

	global.GVA_DB.Table("meta_apps").Select("name as label,id as value").Scan(&app_id)
	res["app_id"] = app_id
	return
}
func (adpriceService *AdPriceService) GetAdPricePublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
