package meta

import (
	"fmt"
	"sort"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/meta"
)

type DashubDashboardService struct{}

// DashubDashboardItem Dashboard项目结构
type DashubDashboardItem struct {
	ID          string      `json:"id"`          // 项目ID
	Title       string      `json:"title"`       // 项目标题
	Type        string      `json:"type"`        // 图表类型：card, line_chart, bar_chart, pie_chart, multi_chart
	ColSpan     int         `json:"colSpan"`     // 占用列数（1-4）
	Value       interface{} `json:"value"`       // 数值
	Unit        string      `json:"unit"`        // 单位
	Trend       *meta.Trend `json:"trend"`       // 趋势信息
	ChartData   interface{} `json:"chartData"`   // 图表数据
	Description string      `json:"description"` // 描述信息

	// 组合图表特有字段
	GroupID     string                `json:"groupId,omitempty"`     // 图表组ID
	GroupTitle  string                `json:"groupTitle,omitempty"`  // 图表组标题
	Charts      []DashubChartOption   `json:"charts,omitempty"`      // 图表选项列表
}

// DashubDashboardResponse Dashboard响应结构
type DashubDashboardResponse struct {
	Items []DashubDashboardItem `json:"items"`
}

// DashubChartGroupItem 图表组合项目
type DashubChartGroupItem struct {
	ID          string                 `json:"id"`
	Title       string                 `json:"title"`
	Type        string                 `json:"type"`        // multi_chart
	ColSpan     int                    `json:"colSpan"`
	Description string                 `json:"description"`
	GroupID     string                 `json:"groupId"`
	GroupTitle  string                 `json:"groupTitle"`
	Charts      []DashubChartOption    `json:"charts"`      // 图表选项
	ChartData   map[string]interface{} `json:"chartData"`   // 当前显示的图表数据
}

// DashubChartOption 图表选项
type DashubChartOption struct {
	ID    int    `json:"id"`
	Title string `json:"title"`
	Unit  string `json:"unit"`
}

// GetDashubDashboardData 获取用户的Dashboard数据（基于Dashub表）
func (service *DashubDashboardService) GetDashubDashboardData(userID uint, authorityID uint) (DashubDashboardResponse, error) {
	var response DashubDashboardResponse
	var items []DashubDashboardItem

	// 获取用户权限对应的项目列表
	allowedKinds, exists := meta.GetRolePermissions(authorityID)
	if !exists {
		return response, fmt.Errorf("未找到角色权限配置")
	}

	// 获取图表组合
	chartGroups := meta.GetChartGroups(allowedKinds)

	// 生成图表组合项目
	for _, group := range chartGroups {
		groupItem, err := service.generateChartGroupItem(group, userID, authorityID)
		if err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("生成图表组合失败: %s, 错误: %v", group.GroupTitle, err))
			continue
		}
		items = append(items, groupItem)
	}

	// 获取单独的项目（非组合）
	individualItems := meta.GetIndividualItems(allowedKinds)
	for _, config := range individualItems {
		item, err := service.generateDashubItemData(config, userID, authorityID)
		if err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("生成项目数据失败: %s, 错误: %v", config.Name, err))
			continue
		}
		items = append(items, item)
	}

	// 对items进行排序：Card类型优先，然后按ColSpan排序
	sort.Slice(items, func(i, j int) bool {
		// Card类型优先
		if items[i].Type == "card" && items[j].Type != "card" {
			return true
		}
		if items[i].Type != "card" && items[j].Type == "card" {
			return false
		}

		// 同类型按ColSpan排序（小的在前）
		if items[i].Type == items[j].Type {
			return items[i].ColSpan < items[j].ColSpan
		}

		return false
	})

	response.Items = items
	return response, nil
}

// generateChartGroupItem 生成图表组合项目
func (service *DashubDashboardService) generateChartGroupItem(group meta.ChartGroup, userID uint, authorityID uint) (DashubDashboardItem, error) {
	var item DashubDashboardItem

	// 构建图表选项
	var chartOptions []DashubChartOption
	for _, config := range group.Items {
		chartOptions = append(chartOptions, DashubChartOption{
			ID:    config.ID,
			Title: config.Title,
			Unit:  config.Unit,
		})
	}

	// 默认显示第一个图表的数据
	var defaultChartData map[string]interface{}
	if len(group.Items) > 0 {
		firstConfig := group.Items[0]
		chartData, err := service.getChartDataForConfig(firstConfig, userID, authorityID)
		if err != nil {
			return item, err
		}
		defaultChartData = chartData
	}

	item = DashubDashboardItem{
		ID:          group.GroupID,
		Title:       group.GroupTitle,
		Type:        "multi_chart",
		ColSpan:     group.ColSpan,
		Description: fmt.Sprintf("包含%d个相关图表", len(group.Items)),
		ChartData:   defaultChartData,
		GroupID:     group.GroupID,
		GroupTitle:  group.GroupTitle,
		Charts:      chartOptions,
	}

	return item, nil
}

// getChartDataForConfig 为指定配置获取图表数据
func (service *DashubDashboardService) getChartDataForConfig(config meta.DashubItemConfig, userID uint, authorityID uint) (map[string]interface{}, error) {
	chartData, err := service.GetChartData(config.ID, userID, authorityID)
	if err != nil {
		return nil, err
	}

	// 确保返回的是map[string]interface{}类型
	if data, ok := chartData.(map[string]interface{}); ok {
		return data, nil
	}

	return nil, fmt.Errorf("图表数据格式错误")
}

// generateDashubItemData 生成单个项目的数据
func (service *DashubDashboardService) generateDashubItemData(config meta.DashubItemConfig, userID uint, authorityID uint) (DashubDashboardItem, error) {
	item := DashubDashboardItem{
		ID:          config.Name,
		Title:       config.Title,
		Type:        config.Type,
		ColSpan:     config.ColSpan,
		Unit:        config.Unit,
		Description: config.Description,
	}

	// 根据项目类型获取数据
	if config.HasHistory {
		// 有历史数据的项目，生成图表数据
		chartData, err := service.GetChartData(config.ID, userID, authorityID)
		if err != nil {
			return item, err
		}
		item.ChartData = chartData
		
		// 获取最新值作为当前值
		latestValue, err := service.getLatestValue(config.ID, userID, authorityID)
		if err != nil {
			return item, err
		}
		item.Value = latestValue
	} else {
		// 没有历史数据的项目，只获取当前值
		currentValue, err := service.getCurrentValue(config.ID, userID, authorityID)
		if err != nil {
			return item, err
		}
		item.Value = currentValue
	}

	// 计算趋势（如果需要）
	if config.HasTrend {
		trend, err := service.calculateTrend(config.ID, userID, authorityID)
		if err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("计算趋势失败: %s, 错误: %v", config.Name, err))
		} else {
			item.Trend = trend
		}
	}

	return item, nil
}

// GetChartData 获取图表数据（最近7天）
func (service *DashubDashboardService) GetChartData(kind int, userID uint, authorityID uint) (interface{}, error) {
	var dashubData []meta.Dashub
	
	// 获取最近7天的数据
	endDate := time.Now().Format("2006-01-02")
	startDate := time.Now().AddDate(0, 0, -6).Format("2006-01-02")
	
	query := global.GVA_DB.Where("kind = ? AND day >= ? AND day <= ?", kind, startDate, endDate)
	
	// 根据角色过滤数据
	if authorityID != 888 { // 非管理员需要过滤组织数据
		query = query.Where("organize = ?", userID)
	}
	
	err := query.Order("day ASC").Find(&dashubData).Error
	if err != nil {
		return nil, err
	}

	// 构造图表数据
	chartData := map[string]interface{}{
		"labels": []string{},
		"data":   []float64{},
	}
	
	// 填充7天的数据，缺失的日期补0
	for i := 0; i < 7; i++ {
		currentDate := time.Now().AddDate(0, 0, -6+i).Format("2006-01-02")
		labels := chartData["labels"].([]string)
		data := chartData["data"].([]float64)
		
		// 查找当天的数据
		found := false
		for _, item := range dashubData {
			if *item.Day == currentDate {
				labels = append(labels, currentDate)
				data = append(data, *item.Nums)
				found = true
				break
			}
		}
		
		if !found {
			labels = append(labels, currentDate)
			data = append(data, 0)
		}
		
		chartData["labels"] = labels
		chartData["data"] = data
	}

	return chartData, nil
}

// getLatestValue 获取最新值
func (service *DashubDashboardService) getLatestValue(kind int, userID uint, authorityID uint) (float64, error) {
	var dashub meta.Dashub
	
	query := global.GVA_DB.Where("kind = ?", kind)
	
	// 根据角色过滤数据
	if authorityID != 888 { // 非管理员需要过滤组织数据
		query = query.Where("organize = ?", userID)
	}
	
	err := query.Order("day DESC").First(&dashub).Error
	if err != nil {
		return 0, err
	}

	return *dashub.Nums, nil
}

// getCurrentValue 获取当前值（最新一天的数据）
func (service *DashubDashboardService) getCurrentValue(kind int, userID uint, authorityID uint) (float64, error) {
	return service.getLatestValue(kind, userID, authorityID)
}

// calculateTrend 计算趋势
func (service *DashubDashboardService) calculateTrend(kind int, userID uint, authorityID uint) (*meta.Trend, error) {
	// 获取今天和昨天的数据
	today := time.Now().Format("2006-01-02")
	yesterday := time.Now().AddDate(0, 0, -1).Format("2006-01-02")
	
	todayValue, err := service.getValueByDate(kind, userID, authorityID, today)
	if err != nil {
		return nil, err
	}
	
	yesterdayValue, err := service.getValueByDate(kind, userID, authorityID, yesterday)
	if err != nil {
		// 如果没有昨天的数据，返回空趋势
		return &meta.Trend{
			Value:     todayValue,
			IsUp:      todayValue > 0,
			Percent:   0,
			TimeRange: "与昨日对比",
		}, nil
	}
	
	// 计算趋势
	trend := &meta.Trend{
		TimeRange: "与昨日对比",
	}
	
	if yesterdayValue > 0 {
		trend.Percent = (todayValue - yesterdayValue) / yesterdayValue * 100
		trend.IsUp = todayValue > yesterdayValue
		trend.Value = todayValue - yesterdayValue
	} else {
		trend.Percent = 0
		trend.IsUp = todayValue > 0
		trend.Value = todayValue
	}
	
	return trend, nil
}

// getValueByDate 获取指定日期的数值
func (service *DashubDashboardService) getValueByDate(kind int, userID uint, authorityID uint, date string) (float64, error) {
	var dashub meta.Dashub
	
	query := global.GVA_DB.Where("kind = ? AND day = ?", kind, date)
	
	// 根据角色过滤数据
	if authorityID != 888 { // 非管理员需要过滤组织数据
		query = query.Where("organize = ?", userID)
	}
	
	err := query.First(&dashub).Error
	if err != nil {
		return 0, err
	}

	return *dashub.Nums, nil
}

// GetMultiPeriodChartData 获取多周期图表数据（日活、周活、月活等）
func (service *DashubDashboardService) GetMultiPeriodChartData(kinds []int, userID uint, authorityID uint) (interface{}, error) {
	// 获取最近7天的数据
	endDate := time.Now().Format("2006-01-02")
	startDate := time.Now().AddDate(0, 0, -6).Format("2006-01-02")
	
	var dashubData []meta.Dashub
	query := global.GVA_DB.Where("kind IN ? AND day >= ? AND day <= ?", kinds, startDate, endDate)
	
	// 根据角色过滤数据
	if authorityID != 888 { // 非管理员需要过滤组织数据
		query = query.Where("organize = ?", userID)
	}
	
	err := query.Order("day ASC, kind ASC").Find(&dashubData).Error
	if err != nil {
		return nil, err
	}

	// 构造多系列图表数据
	chartData := map[string]interface{}{
		"labels":   []string{},
		"datasets": []map[string]interface{}{},
	}
	
	// 生成日期标签
	labels := []string{}
	for i := 0; i < 7; i++ {
		currentDate := time.Now().AddDate(0, 0, -6+i).Format("2006-01-02")
		labels = append(labels, currentDate)
	}
	chartData["labels"] = labels
	
	// 为每个kind生成一个数据系列
	datasets := []map[string]interface{}{}
	for _, kind := range kinds {
		config, exists := meta.GetDashubItemConfig(kind)
		if !exists {
			continue
		}
		
		data := []float64{}
		for i := 0; i < 7; i++ {
			currentDate := time.Now().AddDate(0, 0, -6+i).Format("2006-01-02")
			
			// 查找当天当前kind的数据
			found := false
			for _, item := range dashubData {
				if *item.Day == currentDate && *item.Kind == kind {
					data = append(data, *item.Nums)
					found = true
					break
				}
			}
			
			if !found {
				data = append(data, 0)
			}
		}
		
		dataset := map[string]interface{}{
			"label": config.Title,
			"data":  data,
		}
		datasets = append(datasets, dataset)
	}
	
	chartData["datasets"] = datasets
	return chartData, nil
}
