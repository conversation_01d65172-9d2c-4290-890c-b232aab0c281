package mcpTool

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/flipped-aurora/gin-vue-admin/server/service"

	"github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/mark3labs/mcp-go/mcp"
)

var (
	autoCodeTemplateService = service.ServiceGroupApp.SystemServiceGroup.AutoCodeTemplate
)

func init() {
	RegisterTool(&Mcp_coder{})
}

type Mcp_coder struct {
}

// 通过json数据生成Golang类型及代码
func (t *Mcp_coder) Handle(ctx context.Context, r mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	data := r.GetArguments()["data"].(string)
	var info request.AutoCode
	err := json.Unmarshal([]byte(data), &info)
	if err != nil {
		return t.result(err)
	}
	err = utils.Verify(info, utils.AutoCodeVerify)
	if err != nil {
		return t.result(err)
	}
	err = info.Pretreatment()
	if err != nil {
		return t.result(err)
	}
	err = autoCodeTemplateService.Create(ctx, info)
	if err != nil {
		return t.result(err)
	}

	return t.result(nil)
}
func (t *Mcp_coder) result(err error) (*mcp.CallToolResult, error) {
	var message = "执行成功"
	if err != nil {
		message = fmt.Sprintf("执行失败，失败原因：\n\n\n%s", err.Error())
	}
	return &mcp.CallToolResult{
		Content: []mcp.Content{
			mcp.TextContent{Type: "text", Text: message},
		},
	}, nil
}
func (t *Mcp_coder) New() mcp.Tool {
	return mcp.NewTool("mcp_coder",
		mcp.WithDescription("通过json数据生成Golang类型及代码"),
		mcp.WithString("data",
			mcp.Description("json数据(类型：request.AutoCode)"),
		),
	)
}
