# 移动端抽屉导航功能测试指南

## 功能概述
实现了手机版下左侧导航栏的隐藏和抽屉式打开功能，提升移动端用户体验。

## 测试步骤

### 1. 桌面端测试
1. 在桌面浏览器中打开应用：http://localhost:8081/
2. 确认左侧导航栏正常显示
3. 确认头部没有菜单按钮
4. 测试导航功能正常

### 2. 移动端测试
1. 使用浏览器开发者工具切换到移动设备模式
2. 或者调整浏览器窗口宽度到 992px 以下
3. 确认左侧导航栏被隐藏
4. 确认头部左侧出现菜单按钮（三条横线图标）

### 3. 抽屉功能测试
1. 点击头部的菜单按钮
2. 确认从左侧滑出抽屉导航
3. 确认抽屉包含：
   - 顶部 Logo 和应用名称
   - 完整的导航菜单
   - 底部关闭按钮
4. 确认抽屉有半透明遮罩

### 4. 交互测试
1. 点击抽屉中的菜单项，确认：
   - 页面正确跳转
   - 抽屉自动关闭
2. 点击遮罩区域，确认抽屉关闭
3. 点击底部"关闭菜单"按钮，确认抽屉关闭
4. 在抽屉打开状态下切换回桌面端，确认抽屉自动关闭

### 5. 不同侧边栏模式测试
测试在不同的侧边栏模式下抽屉功能：
1. 普通模式 (normal)
2. 头部模式 (head)
3. 组合模式 (combination)
4. 侧边栏模式 (sidebar)

### 6. 主题测试
1. 测试亮色主题下的抽屉显示
2. 测试暗色主题下的抽屉显示
3. 确认菜单按钮在不同主题下都清晰可见

## 预期结果
- ✅ 桌面端：显示固定侧边栏，无菜单按钮
- ✅ 移动端：隐藏侧边栏，显示菜单按钮
- ✅ 抽屉：从左侧滑出，包含完整导航
- ✅ 交互：点击菜单项或遮罩关闭抽屉
- ✅ 响应式：设备切换时正确显示/隐藏

## 技术实现要点
1. 使用 `useResponsive` hook 检测设备类型
2. 在 app store 中管理抽屉状态
3. 使用 Element Plus Drawer 组件
4. 复用现有的导航菜单逻辑
5. 支持不同的侧边栏模式
6. 自动关闭机制（路由变化、设备切换）

## 文件修改清单
- `web/src/pinia/modules/app.js` - 添加抽屉状态管理
- `web/src/view/layout/header/index.vue` - 添加移动端菜单按钮
- `web/src/view/layout/index.vue` - 修改布局逻辑
- `web/src/view/layout/aside/drawerMode.vue` - 新建抽屉组件
