# 功能开发任务

完成以下开发作协


## 为移动端增加一个底部导航组件
- 有三个功能标签：首页，系统，设置；
- 首页：展示一些快捷功能和简单系统数据，如：快速添加广告，查看统计。。。等等；
- 系统：展示广告系统；
- 设置：设置页


## 增加一个广告发布向导
- 点击首页的【快速添加广告】打开此向导；
- 本向导是单页面模式，所有步骤完成后再提交数据；
- 第一步：询问用户需要达到什么效果，可选项：品牌宣传（CPM），引流（CPC）；
- 第二步：选择广告类型，广告图片等；
- 第三步：填写广告的基础数据，如：标题，描述，点击跳转URL等；
- 第四步：填写出价金额，总预算等，以及发布按钮；
- 开发新后端接口，当用户点击发布后，广告全自动发布（自动根据用户所填创建广告，取得广告ID后再自动创建投放计划）；

## 为这些页面增加大图




<!--  -->
参考截图，为 `ad.vue` 新增加一种新视图，并使用按钮切换普通/新视图，切换后需要在浏览器记住用户的选择以便下次直接使用

<!-- 2 -->
参考我提供的截图，为当前view文件新增加一种新视图，并使用按钮切换普通/新视图，切换后需要在浏览器记住用户的选择以便下次直接使用，新视图以组件方式开发，接收单个campaign实体作为参数


<!-- 3 -->
为当前view文件新增加图表视图，并使用按钮切换普通/图表，切换后需要在浏览器记住用户的选择以便下次直接使用，新视图以组件方式开发，按日期、展示、点击...维度(分多个图表，按钮切换)



图表组件需要使用真实数据

<!-- 11 -->
为 `计划名称` 添加点击打开统计图表功能，开发要求：
- 以新组件方式开发，新功能代码要有具备独立性和通用性，尽量少侵入原有代码，便于在其它页面中重复使用；
- 当用户点击后，在当前页面以弹出层方式展示数据统计图表；
- 弹出层接受 id，数据类型(target_type) 作为参数，使用传入的 id 作为查询条件（`统计目标`字段）；
- target_type的类型分别是：1:APP，2:广告，3:广告计划；
- 数据获取参考：
    * 本统计图表功能是 `dashub` 的图表化实现 （参考 `web/src/view/meta/dashub/dashub.vue`）；
    * `Dashub` 是图表数据的实体，数据查询API参考 `dashub.vue`；
    * 当切换不同的图表时，使用id、target_type、图表类型等读取数据（图表类型对应的值需参考 dashub_constants.go）；
- 图表以 `统计日期` 作为横轴，根据不同的传入类型展示多张图表：
    * 默认图表：`广告展示数`、`点击数`；
    * 当类型是广告时：展示默认图表；
    * 当类型是APP时，展示 `日活用户数`、默认图表等；
    * 当类型（target_type=3）是广告计划时：
      - 先加载广告计划自身的图表数据；
      - 再根据广告计划ID加载广告计划关联的广告ID列表；
      - 再根据广告ID列表加载广告的图表数据；
      - 把广告计划以及关联的每个广告的数据合并展示（同一个图表，多条曲线展示，不同颜色区分）；



请求返回格式如下：
{"code":0,"data":{"list":[{"ID":1,"CreatedAt":"2025-06-04T14:29:12+08:00","UpdatedAt":"2025-06-04T14:29:12+08:00","day":"2025-06-04","kind":1001,"target_type":0,"target":0,"nums":5488},{"ID":2,"CreatedAt":"2025-06-04T14:29:12+08:00","UpdatedAt":"2025-06-04T14:29:12+08:00","day":"2025-06-04","kind":1002,"target_type":0,"target":0,"nums":8232},...]}}
