import service from '@/utils/request'

// @Tags DashubDashboard
// @Summary 获取Dashboard数据（基于Dashub表）
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dashub-dashboard/data [get]
export const getDashubDashboardData = () => {
  return service({
    url: '/dashub-dashboard/data',
    method: 'get'
  })
}

// @Tags DashubDashboard
// @Summary 获取多周期图表数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param kinds query string true "统计类型ID列表，用逗号分隔"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dashub-dashboard/multi-period-chart [get]
export const getMultiPeriodChart = (kinds) => {
  return service({
    url: '/dashub-dashboard/multi-period-chart',
    method: 'get',
    params: {
      kinds: kinds.join(',')
    }
  })
}

// @Tags DashubDashboard
// @Summary 获取单个图表数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param kind query int true "统计类型ID"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dashub-dashboard/chart-data [get]
export const getChartData = (kind) => {
  return service({
    url: '/dashub-dashboard/chart-data',
    method: 'get',
    params: {
      kind
    }
  })
}

// @Tags DashubDashboard
// @Summary 获取所有统计项目配置
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dashub-dashboard/item-configs [get]
export const getDashubItemConfigs = () => {
  return service({
    url: '/dashub-dashboard/item-configs',
    method: 'get'
  })
}

// @Tags DashubDashboard
// @Summary 获取角色权限
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=array,msg=string} "获取成功"
// @Router /dashub-dashboard/role-permissions [get]
export const getRolePermissions = () => {
  return service({
    url: '/dashub-dashboard/role-permissions',
    method: 'get'
  })
}

// @Tags DashubDashboard
// @Summary 获取Dashub统计信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dashub-dashboard/stats [get]
export const getDashubStats = () => {
  return service({
    url: '/dashub-dashboard/stats',
    method: 'get'
  })
}
