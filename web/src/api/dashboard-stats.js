import service from '@/utils/request'

// @Tags DashboardStats
// @Summary 获取系统统计数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dashboard/stats [get]
export const getDashboardStats = () => {
  return service({
    url: '/dashboard/stats',
    method: 'get',
  })
}

// @Tags DashboardStats
// @Summary 获取广告统计概览
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dashboard/ad-overview [get]
export const getAdOverview = () => {
  return service({
    url: '/dashboard/ad-overview',
    method: 'get',
  })
}

// @Tags DashboardStats
// @Summary 获取财务统计数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dashboard/finance-stats [get]
export const getFinanceStats = () => {
  return service({
    url: '/dashboard/finance-stats',
    method: 'get',
  })
}

// @Tags DashboardStats
// @Summary 获取今日统计数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dashboard/today-stats [get]
export const getTodayStats = () => {
  return service({
    url: '/dashboard/today-stats',
    method: 'get',
  })
}

// @Tags DashboardStats
// @Summary 获取系统健康状态
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dashboard/system-health [get]
export const getSystemHealth = () => {
  return service({
    url: '/dashboard/system-health',
    method: 'get',
  })
}

// @Tags DashboardStats
// @Summary 获取快捷操作相关数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dashboard/quick-actions-data [get]
export const getQuickActionsData = () => {
  return service({
    url: '/dashboard/quick-actions-data',
    method: 'get',
  })
}
