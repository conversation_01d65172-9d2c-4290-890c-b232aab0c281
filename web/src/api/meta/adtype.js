import service from '@/utils/request'
// @Tags AdType
// @Summary 创建广告类型管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdType true "创建广告类型管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /adtype/createAdType [post]
export const createAdType = (data) => {
  return service({
    url: '/adtype/createAdType',
    method: 'post',
    data
  })
}

// @Tags AdType
// @Summary 删除广告类型管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdType true "删除广告类型管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adtype/deleteAdType [delete]
export const deleteAdType = (params) => {
  return service({
    url: '/adtype/deleteAdType',
    method: 'delete',
    params
  })
}

// @Tags AdType
// @Summary 批量删除广告类型管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除广告类型管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adtype/deleteAdType [delete]
export const deleteAdTypeByIds = (params) => {
  return service({
    url: '/adtype/deleteAdTypeByIds',
    method: 'delete',
    params
  })
}

// @Tags AdType
// @Summary 更新广告类型管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdType true "更新广告类型管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /adtype/updateAdType [put]
export const updateAdType = (data) => {
  return service({
    url: '/adtype/updateAdType',
    method: 'put',
    data
  })
}

// @Tags AdType
// @Summary 用id查询广告类型管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.AdType true "用id查询广告类型管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adtype/findAdType [get]
export const findAdType = (params) => {
  return service({
    url: '/adtype/findAdType',
    method: 'get',
    params
  })
}

// @Tags AdType
// @Summary 分页获取广告类型管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取广告类型管理列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adtype/getAdTypeList [get]
export const getAdTypeList = (params) => {
  return service({
    url: '/adtype/getAdTypeList',
    method: 'get',
    params
  })
}

// @Tags AdType
// @Summary 不需要鉴权的广告类型管理接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdTypeSearch true "分页获取广告类型管理列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adtype/getAdTypePublic [get]
export const getAdTypePublic = () => {
  return service({
    url: '/adtype/getAdTypePublic',
    method: 'get',
  })
}
