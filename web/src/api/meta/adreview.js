import service from '@/utils/request'
// @Tags AdReview
// @Summary 创建广告审核
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdReview true "创建广告审核"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /adreview/createAdReview [post]
export const createAdReview = (data) => {
  return service({
    url: '/adreview/createAdReview',
    method: 'post',
    data
  })
}

// @Tags AdReview
// @Summary 删除广告审核
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdReview true "删除广告审核"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adreview/deleteAdReview [delete]
export const deleteAdReview = (params) => {
  return service({
    url: '/adreview/deleteAdReview',
    method: 'delete',
    params
  })
}

// @Tags AdReview
// @Summary 批量删除广告审核
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除广告审核"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adreview/deleteAdReview [delete]
export const deleteAdReviewByIds = (params) => {
  return service({
    url: '/adreview/deleteAdReviewByIds',
    method: 'delete',
    params
  })
}

// @Tags AdReview
// @Summary 更新广告审核
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdReview true "更新广告审核"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /adreview/updateAdReview [put]
export const updateAdReview = (data) => {
  return service({
    url: '/adreview/updateAdReview',
    method: 'put',
    data
  })
}

// @Tags AdReview
// @Summary 用id查询广告审核
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.AdReview true "用id查询广告审核"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adreview/findAdReview [get]
export const findAdReview = (params) => {
  return service({
    url: '/adreview/findAdReview',
    method: 'get',
    params
  })
}

// @Tags AdReview
// @Summary 分页获取广告审核列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取广告审核列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adreview/getAdReviewList [get]
export const getAdReviewList = (params) => {
  return service({
    url: '/adreview/getAdReviewList',
    method: 'get',
    params
  })
}
// @Tags AdReview
// @Summary 获取数据源
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adreview/findAdReviewDataSource [get]
export const getAdReviewDataSource = () => {
  return service({
    url: '/adreview/getAdReviewDataSource',
    method: 'get',
  })
}

// @Tags AdReview
// @Summary 不需要鉴权的广告审核接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdReviewSearch true "分页获取广告审核列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adreview/getAdReviewPublic [get]
export const getAdReviewPublic = () => {
  return service({
    url: '/adreview/getAdReviewPublic',
    method: 'get',
  })
}
