import service from '@/utils/request'
// @Tags Campaign
// @Summary 创建广告投放
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Campaign true "创建广告投放"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /campaign/createCampaign [post]
export const createCampaign = (data) => {
  return service({
    url: '/campaign/createCampaign',
    method: 'post',
    data
  })
}

// @Tags Campaign
// @Summary 删除广告投放
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Campaign true "删除广告投放"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /campaign/deleteCampaign [delete]
export const deleteCampaign = (params) => {
  return service({
    url: '/campaign/deleteCampaign',
    method: 'delete',
    params
  })
}

// @Tags Campaign
// @Summary 批量删除广告投放
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除广告投放"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /campaign/deleteCampaign [delete]
export const deleteCampaignByIds = (params) => {
  return service({
    url: '/campaign/deleteCampaignByIds',
    method: 'delete',
    params
  })
}

// @Tags Campaign
// @Summary 更新广告投放
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Campaign true "更新广告投放"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /campaign/updateCampaign [put]
export const updateCampaign = (data) => {
  return service({
    url: '/campaign/updateCampaign',
    method: 'put',
    data
  })
}

// @Tags Campaign
// @Summary 用id查询广告投放
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.Campaign true "用id查询广告投放"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /campaign/findCampaign [get]
export const findCampaign = (params) => {
  return service({
    url: '/campaign/findCampaign',
    method: 'get',
    params
  })
}

// @Tags Campaign
// @Summary 分页获取广告投放列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取广告投放列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /campaign/getCampaignList [get]
export const getCampaignList = (params) => {
  return service({
    url: '/campaign/getCampaignList',
    method: 'get',
    params
  })
}
// @Tags Campaign
// @Summary 获取数据源
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /campaign/findCampaignDataSource [get]
export const getCampaignDataSource = () => {
  return service({
    url: '/campaign/getCampaignDataSource',
    method: 'get',
  })
}

// @Tags Campaign
// @Summary 不需要鉴权的广告投放接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.CampaignSearch true "分页获取广告投放列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /campaign/getCampaignPublic [get]
export const getCampaignPublic = () => {
  return service({
    url: '/campaign/getCampaignPublic',
    method: 'get',
  })
}

// @Tags Campaign
// @Summary 暂停广告投放
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdReq true "暂停广告投放"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"暂停成功"}"
// @Router /campaign/pauseCampaign [put]
export const pauseCampaign = (data) => {
  return service({  
    url: '/campaign/pauseCampaign',
    method: 'post',
    data
  })
}
