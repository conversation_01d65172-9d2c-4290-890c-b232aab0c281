import service from '@/utils/request'
// @Tags AdClick
// @Summary 创建广告点击记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdClick true "创建广告点击记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /adclick/createAdClick [post]
export const createAdClick = (data) => {
  return service({
    url: '/adclick/createAdClick',
    method: 'post',
    data
  })
}

// @Tags AdClick
// @Summary 删除广告点击记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdClick true "删除广告点击记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adclick/deleteAdClick [delete]
export const deleteAdClick = (params) => {
  return service({
    url: '/adclick/deleteAdClick',
    method: 'delete',
    params
  })
}

// @Tags AdClick
// @Summary 批量删除广告点击记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除广告点击记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adclick/deleteAdClick [delete]
export const deleteAdClickByIds = (params) => {
  return service({
    url: '/adclick/deleteAdClickByIds',
    method: 'delete',
    params
  })
}

// @Tags AdClick
// @Summary 更新广告点击记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdClick true "更新广告点击记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /adclick/updateAdClick [put]
export const updateAdClick = (data) => {
  return service({
    url: '/adclick/updateAdClick',
    method: 'put',
    data
  })
}

// @Tags AdClick
// @Summary 用id查询广告点击记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.AdClick true "用id查询广告点击记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adclick/findAdClick [get]
export const findAdClick = (params) => {
  return service({
    url: '/adclick/findAdClick',
    method: 'get',
    params
  })
}

// @Tags AdClick
// @Summary 分页获取广告点击记录列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取广告点击记录列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adclick/getAdClickList [get]
export const getAdClickList = (params) => {
  return service({
    url: '/adclick/getAdClickList',
    method: 'get',
    params
  })
}
// @Tags AdClick
// @Summary 获取数据源
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adclick/findAdClickDataSource [get]
export const getAdClickDataSource = () => {
  return service({
    url: '/adclick/getAdClickDataSource',
    method: 'get',
  })
}

// @Tags AdClick
// @Summary 不需要鉴权的广告点击记录接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdClickSearch true "分页获取广告点击记录列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adclick/getAdClickPublic [get]
export const getAdClickPublic = () => {
  return service({
    url: '/adclick/getAdClickPublic',
    method: 'get',
  })
}
