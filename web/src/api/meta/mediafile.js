import service from '@/utils/request'
// @Tags MediaFile
// @Summary 创建媒体文件
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.MediaFile true "创建媒体文件"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /mediafile/createMediaFile [post]
export const createMediaFile = (data) => {
  return service({
    url: '/mediafile/createMediaFile',
    method: 'post',
    data
  })
}

// @Tags MediaFile
// @Summary 删除媒体文件
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.MediaFile true "删除媒体文件"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /mediafile/deleteMediaFile [delete]
export const deleteMediaFile = (params) => {
  return service({
    url: '/mediafile/deleteMediaFile',
    method: 'delete',
    params
  })
}

// @Tags MediaFile
// @Summary 批量删除媒体文件
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除媒体文件"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /mediafile/deleteMediaFile [delete]
export const deleteMediaFileByIds = (params) => {
  return service({
    url: '/mediafile/deleteMediaFileByIds',
    method: 'delete',
    params
  })
}

// @Tags MediaFile
// @Summary 更新媒体文件
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.MediaFile true "更新媒体文件"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /mediafile/updateMediaFile [put]
export const updateMediaFile = (data) => {
  return service({
    url: '/mediafile/updateMediaFile',
    method: 'put',
    data
  })
}

// @Tags MediaFile
// @Summary 用id查询媒体文件
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.MediaFile true "用id查询媒体文件"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /mediafile/findMediaFile [get]
export const findMediaFile = (params) => {
  return service({
    url: '/mediafile/findMediaFile',
    method: 'get',
    params
  })
}

// @Tags MediaFile
// @Summary 分页获取媒体文件列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取媒体文件列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /mediafile/getMediaFileList [get]
export const getMediaFileList = (params) => {
  return service({
    url: '/mediafile/getMediaFileList',
    method: 'get',
    params
  })
}
// @Tags MediaFile
// @Summary 获取数据源
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /mediafile/findMediaFileDataSource [get]
export const getMediaFileDataSource = () => {
  return service({
    url: '/mediafile/getMediaFileDataSource',
    method: 'get',
  })
}

// @Tags MediaFile
// @Summary 不需要鉴权的媒体文件接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.MediaFileSearch true "分页获取媒体文件列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /mediafile/getMediaFilePublic [get]
export const getMediaFilePublic = () => {
  return service({
    url: '/mediafile/getMediaFilePublic',
    method: 'get',
  })
}
