import service from '@/utils/request'
// @Tags Dashub
// @Summary 创建统计汇总
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Dashub true "创建统计汇总"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /dashub/createDashub [post]
export const createDashub = (data) => {
  return service({
    url: '/dashub/createDashub',
    method: 'post',
    data
  })
}

// @Tags Dashub
// @Summary 删除统计汇总
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Dashub true "删除统计汇总"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /dashub/deleteDashub [delete]
export const deleteDashub = (params) => {
  return service({
    url: '/dashub/deleteDashub',
    method: 'delete',
    params
  })
}

// @Tags Dashub
// @Summary 批量删除统计汇总
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除统计汇总"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /dashub/deleteDashub [delete]
export const deleteDashubByIds = (params) => {
  return service({
    url: '/dashub/deleteDashubByIds',
    method: 'delete',
    params
  })
}

// @Tags Dashub
// @Summary 更新统计汇总
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.Dashub true "更新统计汇总"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /dashub/updateDashub [put]
export const updateDashub = (data) => {
  return service({
    url: '/dashub/updateDashub',
    method: 'put',
    data
  })
}

// @Tags Dashub
// @Summary 用id查询统计汇总
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.Dashub true "用id查询统计汇总"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /dashub/findDashub [get]
export const findDashub = (params) => {
  return service({
    url: '/dashub/findDashub',
    method: 'get',
    params
  })
}

// @Tags Dashub
// @Summary 分页获取统计汇总列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取统计汇总列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /dashub/getDashubList [get]
export const getDashubList = (params) => {
  return service({
    url: '/dashub/getDashubList',
    method: 'get',
    params
  })
}

// @Tags Dashub
// @Summary 不需要鉴权的统计汇总接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.DashubSearch true "分页获取统计汇总列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /dashub/getDashubPublic [get]
export const getDashubPublic = () => {
  return service({
    url: '/dashub/getDashubPublic',
    method: 'get',
  })
}
