import service from '@/utils/request'
// @Tags ConsumeRecord
// @Summary 创建消费记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.ConsumeRecord true "创建消费记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /consumerecord/createConsumeRecord [post]
export const createConsumeRecord = (data) => {
  return service({
    url: '/consumerecord/createConsumeRecord',
    method: 'post',
    data
  })
}

// @Tags ConsumeRecord
// @Summary 删除消费记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.ConsumeRecord true "删除消费记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /consumerecord/deleteConsumeRecord [delete]
export const deleteConsumeRecord = (params) => {
  return service({
    url: '/consumerecord/deleteConsumeRecord',
    method: 'delete',
    params
  })
}

// @Tags ConsumeRecord
// @Summary 批量删除消费记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除消费记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /consumerecord/deleteConsumeRecord [delete]
export const deleteConsumeRecordByIds = (params) => {
  return service({
    url: '/consumerecord/deleteConsumeRecordByIds',
    method: 'delete',
    params
  })
}

// @Tags ConsumeRecord
// @Summary 更新消费记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.ConsumeRecord true "更新消费记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /consumerecord/updateConsumeRecord [put]
export const updateConsumeRecord = (data) => {
  return service({
    url: '/consumerecord/updateConsumeRecord',
    method: 'put',
    data
  })
}

// @Tags ConsumeRecord
// @Summary 用id查询消费记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.ConsumeRecord true "用id查询消费记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /consumerecord/findConsumeRecord [get]
export const findConsumeRecord = (params) => {
  return service({
    url: '/consumerecord/findConsumeRecord',
    method: 'get',
    params
  })
}

// @Tags ConsumeRecord
// @Summary 分页获取消费记录列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取消费记录列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /consumerecord/getConsumeRecordList [get]
export const getConsumeRecordList = (params) => {
  return service({
    url: '/consumerecord/getConsumeRecordList',
    method: 'get',
    params
  })
}
// @Tags ConsumeRecord
// @Summary 获取数据源
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /consumerecord/findConsumeRecordDataSource [get]
export const getConsumeRecordDataSource = () => {
  return service({
    url: '/consumerecord/getConsumeRecordDataSource',
    method: 'get',
  })
}

// @Tags ConsumeRecord
// @Summary 不需要鉴权的消费记录接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.ConsumeRecordSearch true "分页获取消费记录列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /consumerecord/getConsumeRecordPublic [get]
export const getConsumeRecordPublic = () => {
  return service({
    url: '/consumerecord/getConsumeRecordPublic',
    method: 'get',
  })
}
