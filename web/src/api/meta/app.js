import service from '@/utils/request'
// @Tags App
// @Summary 创建应用管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.App true "创建应用管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /app/createApp [post]
export const createApp = (data) => {
  return service({
    url: '/app/createApp',
    method: 'post',
    data
  })
}

// @Tags App
// @Summary 删除应用管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.App true "删除应用管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /app/deleteApp [delete]
export const deleteApp = (params) => {
  return service({
    url: '/app/deleteApp',
    method: 'delete',
    params
  })
}

// @Tags App
// @Summary 批量删除应用管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除应用管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /app/deleteApp [delete]
export const deleteAppByIds = (params) => {
  return service({
    url: '/app/deleteAppByIds',
    method: 'delete',
    params
  })
}

// @Tags App
// @Summary 更新应用管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.App true "更新应用管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /app/updateApp [put]
export const updateApp = (data) => {
  return service({
    url: '/app/updateApp',
    method: 'put',
    data
  })
}

// @Tags App
// @Summary 用id查询应用管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.App true "用id查询应用管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /app/findApp [get]
export const findApp = (params) => {
  return service({
    url: '/app/findApp',
    method: 'get',
    params
  })
}

// @Tags App
// @Summary 分页获取应用管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取应用管理列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /app/getAppList [get]
export const getAppList = (params) => {
  return service({
    url: '/app/getAppList',
    method: 'get',
    params
  })
}
// @Tags App
// @Summary 获取数据源
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /app/findAppDataSource [get]
export const getAppDataSource = () => {
  return service({
    url: '/app/getAppDataSource',
    method: 'get',
  })
}

// @Tags App
// @Summary 不需要鉴权的应用管理接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AppSearch true "分页获取应用管理列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /app/getAppPublic [get]
export const getAppPublic = () => {
  return service({
    url: '/app/getAppPublic',
    method: 'get',
  })
}
