import service from '@/utils/request'
// @Tags AdPosition
// @Summary 创建广告位置管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdPosition true "创建广告位置管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /adposition/createAdPosition [post]
export const createAdPosition = (data) => {
  return service({
    url: '/adposition/createAdPosition',
    method: 'post',
    data
  })
}

// @Tags AdPosition
// @Summary 删除广告位置管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdPosition true "删除广告位置管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adposition/deleteAdPosition [delete]
export const deleteAdPosition = (params) => {
  return service({
    url: '/adposition/deleteAdPosition',
    method: 'delete',
    params
  })
}

// @Tags AdPosition
// @Summary 批量删除广告位置管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除广告位置管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adposition/deleteAdPosition [delete]
export const deleteAdPositionByIds = (params) => {
  return service({
    url: '/adposition/deleteAdPositionByIds',
    method: 'delete',
    params
  })
}

// @Tags AdPosition
// @Summary 更新广告位置管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdPosition true "更新广告位置管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /adposition/updateAdPosition [put]
export const updateAdPosition = (data) => {
  return service({
    url: '/adposition/updateAdPosition',
    method: 'put',
    data
  })
}

// @Tags AdPosition
// @Summary 用id查询广告位置管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.AdPosition true "用id查询广告位置管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adposition/findAdPosition [get]
export const findAdPosition = (params) => {
  return service({
    url: '/adposition/findAdPosition',
    method: 'get',
    params
  })
}

// @Tags AdPosition
// @Summary 分页获取广告位置管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取广告位置管理列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adposition/getAdPositionList [get]
export const getAdPositionList = (params) => {
  return service({
    url: '/adposition/getAdPositionList',
    method: 'get',
    params
  })
}

// @Tags AdPosition
// @Summary 不需要鉴权的广告位置管理接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdPositionSearch true "分页获取广告位置管理列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adposition/getAdPositionPublic [get]
export const getAdPositionPublic = () => {
  return service({
    url: '/adposition/getAdPositionPublic',
    method: 'get',
  })
}
