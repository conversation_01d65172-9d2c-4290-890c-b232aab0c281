import service from '@/utils/request'
// @Tags AdImpression
// @Summary 创建广告展示记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdImpression true "创建广告展示记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /adimpression/createAdImpression [post]
export const createAdImpression = (data) => {
  return service({
    url: '/adimpression/createAdImpression',
    method: 'post',
    data
  })
}

// @Tags AdImpression
// @Summary 删除广告展示记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdImpression true "删除广告展示记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adimpression/deleteAdImpression [delete]
export const deleteAdImpression = (params) => {
  return service({
    url: '/adimpression/deleteAdImpression',
    method: 'delete',
    params
  })
}

// @Tags AdImpression
// @Summary 批量删除广告展示记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除广告展示记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adimpression/deleteAdImpression [delete]
export const deleteAdImpressionByIds = (params) => {
  return service({
    url: '/adimpression/deleteAdImpressionByIds',
    method: 'delete',
    params
  })
}

// @Tags AdImpression
// @Summary 更新广告展示记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdImpression true "更新广告展示记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /adimpression/updateAdImpression [put]
export const updateAdImpression = (data) => {
  return service({
    url: '/adimpression/updateAdImpression',
    method: 'put',
    data
  })
}

// @Tags AdImpression
// @Summary 用id查询广告展示记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.AdImpression true "用id查询广告展示记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adimpression/findAdImpression [get]
export const findAdImpression = (params) => {
  return service({
    url: '/adimpression/findAdImpression',
    method: 'get',
    params
  })
}

// @Tags AdImpression
// @Summary 分页获取广告展示记录列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取广告展示记录列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adimpression/getAdImpressionList [get]
export const getAdImpressionList = (params) => {
  return service({
    url: '/adimpression/getAdImpressionList',
    method: 'get',
    params
  })
}
// @Tags AdImpression
// @Summary 获取数据源
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adimpression/findAdImpressionDataSource [get]
export const getAdImpressionDataSource = () => {
  return service({
    url: '/adimpression/getAdImpressionDataSource',
    method: 'get',
  })
}

// @Tags AdImpression
// @Summary 不需要鉴权的广告展示记录接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdImpressionSearch true "分页获取广告展示记录列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adimpression/getAdImpressionPublic [get]
export const getAdImpressionPublic = () => {
  return service({
    url: '/adimpression/getAdImpressionPublic',
    method: 'get',
  })
}
