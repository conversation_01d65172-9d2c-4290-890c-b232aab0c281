import service from '@/utils/request'
// @Tags AdAction
// @Summary 创建广告行为管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdAction true "创建广告行为管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /adaction/createAdAction [post]
export const createAdAction = (data) => {
  return service({
    url: '/adaction/createAdAction',
    method: 'post',
    data
  })
}

// @Tags AdAction
// @Summary 删除广告行为管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdAction true "删除广告行为管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adaction/deleteAdAction [delete]
export const deleteAdAction = (params) => {
  return service({
    url: '/adaction/deleteAdAction',
    method: 'delete',
    params
  })
}

// @Tags AdAction
// @Summary 批量删除广告行为管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除广告行为管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adaction/deleteAdAction [delete]
export const deleteAdActionByIds = (params) => {
  return service({
    url: '/adaction/deleteAdActionByIds',
    method: 'delete',
    params
  })
}

// @Tags AdAction
// @Summary 更新广告行为管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdAction true "更新广告行为管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /adaction/updateAdAction [put]
export const updateAdAction = (data) => {
  return service({
    url: '/adaction/updateAdAction',
    method: 'put',
    data
  })
}

// @Tags AdAction
// @Summary 用id查询广告行为管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.AdAction true "用id查询广告行为管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adaction/findAdAction [get]
export const findAdAction = (params) => {
  return service({
    url: '/adaction/findAdAction',
    method: 'get',
    params
  })
}

// @Tags AdAction
// @Summary 分页获取广告行为管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取广告行为管理列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adaction/getAdActionList [get]
export const getAdActionList = (params) => {
  return service({
    url: '/adaction/getAdActionList',
    method: 'get',
    params
  })
}

// @Tags AdAction
// @Summary 不需要鉴权的广告行为管理接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdActionSearch true "分页获取广告行为管理列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adaction/getAdActionPublic [get]
export const getAdActionPublic = () => {
  return service({
    url: '/adaction/getAdActionPublic',
    method: 'get',
  })
}
