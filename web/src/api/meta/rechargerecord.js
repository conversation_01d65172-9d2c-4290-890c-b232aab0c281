import service from '@/utils/request'
// @Tags RechargeRecord
// @Summary 创建充值记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.RechargeRecord true "创建充值记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /rechargerecord/createRechargeRecord [post]
export const createRechargeRecord = (data) => {
  return service({
    url: '/rechargerecord/createRechargeRecord',
    method: 'post',
    data
  })
}

// @Tags RechargeRecord
// @Summary 删除充值记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.RechargeRecord true "删除充值记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /rechargerecord/deleteRechargeRecord [delete]
export const deleteRechargeRecord = (params) => {
  return service({
    url: '/rechargerecord/deleteRechargeRecord',
    method: 'delete',
    params
  })
}

// @Tags RechargeRecord
// @Summary 批量删除充值记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除充值记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /rechargerecord/deleteRechargeRecord [delete]
export const deleteRechargeRecordByIds = (params) => {
  return service({
    url: '/rechargerecord/deleteRechargeRecordByIds',
    method: 'delete',
    params
  })
}

// @Tags RechargeRecord
// @Summary 更新充值记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.RechargeRecord true "更新充值记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /rechargerecord/updateRechargeRecord [put]
export const updateRechargeRecord = (data) => {
  return service({
    url: '/rechargerecord/updateRechargeRecord',
    method: 'put',
    data
  })
}

// @Tags RechargeRecord
// @Summary 用id查询充值记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.RechargeRecord true "用id查询充值记录"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /rechargerecord/findRechargeRecord [get]
export const findRechargeRecord = (params) => {
  return service({
    url: '/rechargerecord/findRechargeRecord',
    method: 'get',
    params
  })
}

// @Tags RechargeRecord
// @Summary 分页获取充值记录列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取充值记录列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /rechargerecord/getRechargeRecordList [get]
export const getRechargeRecordList = (params) => {
  return service({
    url: '/rechargerecord/getRechargeRecordList',
    method: 'get',
    params
  })
}
// @Tags RechargeRecord
// @Summary 获取数据源
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /rechargerecord/findRechargeRecordDataSource [get]
export const getRechargeRecordDataSource = () => {
  return service({
    url: '/rechargerecord/getRechargeRecordDataSource',
    method: 'get',
  })
}

// @Tags RechargeRecord
// @Summary 不需要鉴权的充值记录接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.RechargeRecordSearch true "分页获取充值记录列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /rechargerecord/getRechargeRecordPublic [get]
export const getRechargeRecordPublic = () => {
  return service({
    url: '/rechargerecord/getRechargeRecordPublic',
    method: 'get',
  })
}
