import service from '@/utils/request'
// @Tags FinanceAccount
// @Summary 创建财务账户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.FinanceAccount true "创建财务账户"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /financeaccount/createFinanceAccount [post]
export const createFinanceAccount = (data) => {
  return service({
    url: '/financeaccount/createFinanceAccount',
    method: 'post',
    data
  })
}

// @Tags FinanceAccount
// @Summary 删除财务账户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.FinanceAccount true "删除财务账户"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /financeaccount/deleteFinanceAccount [delete]
export const deleteFinanceAccount = (params) => {
  return service({
    url: '/financeaccount/deleteFinanceAccount',
    method: 'delete',
    params
  })
}

// @Tags FinanceAccount
// @Summary 批量删除财务账户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除财务账户"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /financeaccount/deleteFinanceAccount [delete]
export const deleteFinanceAccountByIds = (params) => {
  return service({
    url: '/financeaccount/deleteFinanceAccountByIds',
    method: 'delete',
    params
  })
}

// @Tags FinanceAccount
// @Summary 更新财务账户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.FinanceAccount true "更新财务账户"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /financeaccount/updateFinanceAccount [put]
export const updateFinanceAccount = (data) => {
  return service({
    url: '/financeaccount/updateFinanceAccount',
    method: 'put',
    data
  })
}

// @Tags FinanceAccount
// @Summary 用id查询财务账户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.FinanceAccount true "用id查询财务账户"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /financeaccount/findFinanceAccount [get]
export const findFinanceAccount = (params) => {
  return service({
    url: '/financeaccount/findFinanceAccount',
    method: 'get',
    params
  })
}

// @Tags FinanceAccount
// @Summary 分页获取财务账户列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取财务账户列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /financeaccount/getFinanceAccountList [get]
export const getFinanceAccountList = (params) => {
  return service({
    url: '/financeaccount/getFinanceAccountList',
    method: 'get',
    params
  })
}
// @Tags FinanceAccount
// @Summary 获取数据源
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /financeaccount/findFinanceAccountDataSource [get]
export const getFinanceAccountDataSource = () => {
  return service({
    url: '/financeaccount/getFinanceAccountDataSource',
    method: 'get',
  })
}

// @Tags FinanceAccount
// @Summary 不需要鉴权的财务账户接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.FinanceAccountSearch true "分页获取财务账户列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /financeaccount/getFinanceAccountPublic [get]
export const getFinanceAccountPublic = () => {
  return service({
    url: '/financeaccount/getFinanceAccountPublic',
    method: 'get',
  })
}
