import service from '@/utils/request'
// @Tags AdPrice
// @Summary 创建广告价格管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdPrice true "创建广告价格管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /adprice/createAdPrice [post]
export const createAdPrice = (data) => {
  return service({
    url: '/adprice/createAdPrice',
    method: 'post',
    data
  })
}

// @Tags AdPrice
// @Summary 删除广告价格管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdPrice true "删除广告价格管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adprice/deleteAdPrice [delete]
export const deleteAdPrice = (params) => {
  return service({
    url: '/adprice/deleteAdPrice',
    method: 'delete',
    params
  })
}

// @Tags AdPrice
// @Summary 批量删除广告价格管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除广告价格管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adprice/deleteAdPrice [delete]
export const deleteAdPriceByIds = (params) => {
  return service({
    url: '/adprice/deleteAdPriceByIds',
    method: 'delete',
    params
  })
}

// @Tags AdPrice
// @Summary 更新广告价格管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdPrice true "更新广告价格管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /adprice/updateAdPrice [put]
export const updateAdPrice = (data) => {
  return service({
    url: '/adprice/updateAdPrice',
    method: 'put',
    data
  })
}

// @Tags AdPrice
// @Summary 用id查询广告价格管理
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.AdPrice true "用id查询广告价格管理"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adprice/findAdPrice [get]
export const findAdPrice = (params) => {
  return service({
    url: '/adprice/findAdPrice',
    method: 'get',
    params
  })
}

// @Tags AdPrice
// @Summary 分页获取广告价格管理列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取广告价格管理列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adprice/getAdPriceList [get]
export const getAdPriceList = (params) => {
  return service({
    url: '/adprice/getAdPriceList',
    method: 'get',
    params
  })
}
// @Tags AdPrice
// @Summary 获取数据源
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adprice/findAdPriceDataSource [get]
export const getAdPriceDataSource = () => {
  return service({
    url: '/adprice/getAdPriceDataSource',
    method: 'get',
  })
}

// @Tags AdPrice
// @Summary 不需要鉴权的广告价格管理接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdPriceSearch true "分页获取广告价格管理列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adprice/getAdPricePublic [get]
export const getAdPricePublic = () => {
  return service({
    url: '/adprice/getAdPricePublic',
    method: 'get',
  })
}
