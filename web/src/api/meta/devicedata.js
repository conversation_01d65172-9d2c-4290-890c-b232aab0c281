import service from '@/utils/request'
// @Tags DeviceData
// @Summary 创建设备数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.DeviceData true "创建设备数据"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /devicedata/createDeviceData [post]
export const createDeviceData = (data) => {
  return service({
    url: '/devicedata/createDeviceData',
    method: 'post',
    data
  })
}

// @Tags DeviceData
// @Summary 删除设备数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.DeviceData true "删除设备数据"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /devicedata/deleteDeviceData [delete]
export const deleteDeviceData = (params) => {
  return service({
    url: '/devicedata/deleteDeviceData',
    method: 'delete',
    params
  })
}

// @Tags DeviceData
// @Summary 批量删除设备数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除设备数据"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /devicedata/deleteDeviceData [delete]
export const deleteDeviceDataByIds = (params) => {
  return service({
    url: '/devicedata/deleteDeviceDataByIds',
    method: 'delete',
    params
  })
}

// @Tags DeviceData
// @Summary 更新设备数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.DeviceData true "更新设备数据"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /devicedata/updateDeviceData [put]
export const updateDeviceData = (data) => {
  return service({
    url: '/devicedata/updateDeviceData',
    method: 'put',
    data
  })
}

// @Tags DeviceData
// @Summary 用id查询设备数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.DeviceData true "用id查询设备数据"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /devicedata/findDeviceData [get]
export const findDeviceData = (params) => {
  return service({
    url: '/devicedata/findDeviceData',
    method: 'get',
    params
  })
}

// @Tags DeviceData
// @Summary 分页获取设备数据列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取设备数据列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /devicedata/getDeviceDataList [get]
export const getDeviceDataList = (params) => {
  return service({
    url: '/devicedata/getDeviceDataList',
    method: 'get',
    params
  })
}
// @Tags DeviceData
// @Summary 获取数据源
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /devicedata/findDeviceDataDataSource [get]
export const getDeviceDataDataSource = () => {
  return service({
    url: '/devicedata/getDeviceDataDataSource',
    method: 'get',
  })
}

// @Tags DeviceData
// @Summary 不需要鉴权的设备数据接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.DeviceDataSearch true "分页获取设备数据列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /devicedata/getDeviceDataPublic [get]
export const getDeviceDataPublic = () => {
  return service({
    url: '/devicedata/getDeviceDataPublic',
    method: 'get',
  })
}
