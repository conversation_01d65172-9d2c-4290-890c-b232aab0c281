import service from '@/utils/request'
// @Tags AuditStatus
// @Summary 创建审核状态配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AuditStatus true "创建审核状态配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /auditstatus/createAuditStatus [post]
export const createAuditStatus = (data) => {
  return service({
    url: '/auditstatus/createAuditStatus',
    method: 'post',
    data
  })
}

// @Tags AuditStatus
// @Summary 删除审核状态配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AuditStatus true "删除审核状态配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /auditstatus/deleteAuditStatus [delete]
export const deleteAuditStatus = (params) => {
  return service({
    url: '/auditstatus/deleteAuditStatus',
    method: 'delete',
    params
  })
}

// @Tags AuditStatus
// @Summary 批量删除审核状态配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除审核状态配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /auditstatus/deleteAuditStatus [delete]
export const deleteAuditStatusByIds = (params) => {
  return service({
    url: '/auditstatus/deleteAuditStatusByIds',
    method: 'delete',
    params
  })
}

// @Tags AuditStatus
// @Summary 更新审核状态配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AuditStatus true "更新审核状态配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /auditstatus/updateAuditStatus [put]
export const updateAuditStatus = (data) => {
  return service({
    url: '/auditstatus/updateAuditStatus',
    method: 'put',
    data
  })
}

// @Tags AuditStatus
// @Summary 用id查询审核状态配置
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.AuditStatus true "用id查询审核状态配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /auditstatus/findAuditStatus [get]
export const findAuditStatus = (params) => {
  return service({
    url: '/auditstatus/findAuditStatus',
    method: 'get',
    params
  })
}

// @Tags AuditStatus
// @Summary 分页获取审核状态配置列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取审核状态配置列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /auditstatus/getAuditStatusList [get]
export const getAuditStatusList = (params) => {
  return service({
    url: '/auditstatus/getAuditStatusList',
    method: 'get',
    params
  })
}

// @Tags AuditStatus
// @Summary 不需要鉴权的审核状态配置接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AuditStatusSearch true "分页获取审核状态配置列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /auditstatus/getAuditStatusPublic [get]
export const getAuditStatusPublic = () => {
  return service({
    url: '/auditstatus/getAuditStatusPublic',
    method: 'get',
  })
}
