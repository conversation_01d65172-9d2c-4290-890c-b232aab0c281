import service from '@/utils/request'
// @Tags AdStatistics
// @Summary 创建广告统计
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdStatistics true "创建广告统计"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /adstatistics/createAdStatistics [post]
export const createAdStatistics = (data) => {
  return service({
    url: '/adstatistics/createAdStatistics',
    method: 'post',
    data
  })
}

// @Tags AdStatistics
// @Summary 删除广告统计
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdStatistics true "删除广告统计"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adstatistics/deleteAdStatistics [delete]
export const deleteAdStatistics = (params) => {
  return service({
    url: '/adstatistics/deleteAdStatistics',
    method: 'delete',
    params
  })
}

// @Tags AdStatistics
// @Summary 批量删除广告统计
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除广告统计"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adstatistics/deleteAdStatistics [delete]
export const deleteAdStatisticsByIds = (params) => {
  return service({
    url: '/adstatistics/deleteAdStatisticsByIds',
    method: 'delete',
    params
  })
}

// @Tags AdStatistics
// @Summary 更新广告统计
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.AdStatistics true "更新广告统计"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /adstatistics/updateAdStatistics [put]
export const updateAdStatistics = (data) => {
  return service({
    url: '/adstatistics/updateAdStatistics',
    method: 'put',
    data
  })
}

// @Tags AdStatistics
// @Summary 用id查询广告统计
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.AdStatistics true "用id查询广告统计"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adstatistics/findAdStatistics [get]
export const findAdStatistics = (params) => {
  return service({
    url: '/adstatistics/findAdStatistics',
    method: 'get',
    params
  })
}

// @Tags AdStatistics
// @Summary 分页获取广告统计列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取广告统计列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adstatistics/getAdStatisticsList [get]
export const getAdStatisticsList = (params) => {
  return service({
    url: '/adstatistics/getAdStatisticsList',
    method: 'get',
    params
  })
}
// @Tags AdStatistics
// @Summary 获取数据源
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adstatistics/findAdStatisticsDataSource [get]
export const getAdStatisticsDataSource = () => {
  return service({
    url: '/adstatistics/getAdStatisticsDataSource',
    method: 'get',
  })
}

// @Tags AdStatistics
// @Summary 不需要鉴权的广告统计接口
// @Accept application/json
// @Produce application/json
// @Param data query metaReq.AdStatisticsSearch true "分页获取广告统计列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /adstatistics/getAdStatisticsPublic [get]
export const getAdStatisticsPublic = () => {
  return service({
    url: '/adstatistics/getAdStatisticsPublic',
    method: 'get',
  })
}
