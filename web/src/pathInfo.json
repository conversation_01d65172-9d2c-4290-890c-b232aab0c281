{"/src/view/about/index.vue": "About", "/src/view/crypto-recharge/components/CryptoPriceCard.vue": "CryptoPriceCard", "/src/view/crypto-recharge/components/QRCodeGenerator.vue": "QRCodeGenerator", "/src/view/crypto-recharge/components/RechargeStatus.vue": "RechargeStatus", "/src/view/crypto-recharge/index.vue": "CryptoRecharge", "/src/view/dashboard/components/banner.vue": "Banner", "/src/view/dashboard/components/card.vue": "Card", "/src/view/dashboard/components/charts-bar.vue": "ChartsBar", "/src/view/dashboard/components/charts-content-numbers.vue": "ChartsContentNumbers", "/src/view/dashboard/components/charts-line.vue": "ChartsLine", "/src/view/dashboard/components/charts-mini-line.vue": "ChartsMiniLine", "/src/view/dashboard/components/charts-multi.vue": "ChartsMulti", "/src/view/dashboard/components/charts-people-numbers.vue": "ChartsPeopleNumbers", "/src/view/dashboard/components/charts-pie.vue": "ChartsPie", "/src/view/dashboard/components/charts.vue": "Charts", "/src/view/dashboard/components/dashboard-card.vue": "DashboardCard", "/src/view/dashboard/components/dashboard-quick-actions.vue": "DashboardQuickActions", "/src/view/dashboard/components/notice.vue": "Notice", "/src/view/dashboard/components/pluginTable.vue": "PluginTable", "/src/view/dashboard/components/quickLinks.vue": "QuickLinks", "/src/view/dashboard/components/table.vue": "Table", "/src/view/dashboard/components/wiki.vue": "Wiki", "/src/view/dashboard/dashboard-new.vue": "DashboardNew", "/src/view/dashboard/index.vue": "Dashboard", "/src/view/error/index.vue": "Error", "/src/view/error/reload.vue": "Reload", "/src/view/example/breakpoint/breakpoint.vue": "BreakPoint", "/src/view/example/customer/customer.vue": "Customer", "/src/view/example/index.vue": "Example", "/src/view/example/upload/scanUpload.vue": "scanUpload", "/src/view/example/upload/upload.vue": "Upload", "/src/view/init/index.vue": "Init", "/src/view/layout/aside/asideComponent/asyncSubmenu.vue": "AsyncSubmenu", "/src/view/layout/aside/asideComponent/index.vue": "AsideComponent", "/src/view/layout/aside/asideComponent/menuItem.vue": "MenuItem", "/src/view/layout/aside/combinationMode.vue": "GvaAside", "/src/view/layout/aside/drawerMode.vue": "Drawer<PERSON><PERSON>", "/src/view/layout/aside/headMode.vue": "GvaAside", "/src/view/layout/aside/index.vue": "Index", "/src/view/layout/aside/normalMode.vue": "GvaAside", "/src/view/layout/aside/sidebarMode.vue": "SidebarMode", "/src/view/layout/header/index.vue": "Index", "/src/view/layout/header/tools.vue": "Tools", "/src/view/layout/iframe.vue": "GvaLayoutIframe", "/src/view/layout/index.vue": "GvaLayout", "/src/view/layout/screenfull/index.vue": "Screenfull", "/src/view/layout/search/search.vue": "BtnBox", "/src/view/layout/setting/index.vue": "GvaSetting", "/src/view/layout/setting/title.vue": "layoutSettingTitle", "/src/view/layout/tabs/index.vue": "HistoryComponent", "/src/view/login/index.vue": "<PERSON><PERSON>", "/src/view/meta/ad/AdCardView.vue": "AdCardView", "/src/view/meta/ad/ad.vue": "Ad", "/src/view/meta/ad/adForm.vue": "AdForm", "/src/view/meta/adaction/adaction.vue": "AdAction", "/src/view/meta/adaction/adactionForm.vue": "AdActionForm", "/src/view/meta/adclick/adclick.vue": "AdClick", "/src/view/meta/adclick/adclickForm.vue": "AdClickForm", "/src/view/meta/adimpression/adimpression.vue": "AdImpression", "/src/view/meta/adimpression/adimpressionForm.vue": "AdImpressionForm", "/src/view/meta/adposition/adposition.vue": "AdPosition", "/src/view/meta/adposition/adpositionForm.vue": "AdPositionForm", "/src/view/meta/adprice/adprice.vue": "AdPrice", "/src/view/meta/adprice/adpriceForm.vue": "AdPriceForm", "/src/view/meta/adreview/adreview.vue": "AdReview", "/src/view/meta/adreview/adreviewForm.vue": "AdReviewForm", "/src/view/meta/adstatistics/adstatistics.vue": "AdStatistics", "/src/view/meta/adstatistics/adstatisticsForm.vue": "AdStatisticsForm", "/src/view/meta/adstatistics/components/StatisticsCharts.vue": "StatisticsCharts", "/src/view/meta/adtype/adtype.vue": "AdType", "/src/view/meta/adtype/adtypeForm.vue": "AdTypeForm", "/src/view/meta/app/app.vue": "App", "/src/view/meta/app/appForm.vue": "AppForm", "/src/view/meta/auditstatus/auditstatus.vue": "AuditStatus", "/src/view/meta/auditstatus/auditstatusForm.vue": "AuditStatusForm", "/src/view/meta/campaign/campaign.vue": "Campaign", "/src/view/meta/campaign/campaignForm.vue": "CampaignForm", "/src/view/meta/campaign/components/CampaignCard.vue": "CampaignCard", "/src/view/meta/consumerecord/consumerecord.vue": "ConsumeRecord", "/src/view/meta/consumerecord/consumerecordForm.vue": "ConsumeRecordForm", "/src/view/meta/dashub/dashub.vue": "<PERSON><PERSON>", "/src/view/meta/dashub/dashubForm.vue": "DashubForm", "/src/view/meta/devicedata/devicedata.vue": "DeviceData", "/src/view/meta/devicedata/devicedataForm.vue": "DeviceDataForm", "/src/view/meta/financeaccount/financeaccount.vue": "FinanceAccount", "/src/view/meta/financeaccount/financeaccountForm.vue": "FinanceAccountForm", "/src/view/meta/mediafile/mediafile.vue": "MediaFile", "/src/view/meta/mediafile/mediafileForm.vue": "MediaFileForm", "/src/view/meta/rechargerecord/rechargerecord.vue": "RechargeRecord", "/src/view/meta/rechargerecord/rechargerecordForm.vue": "RechargeRecordForm", "/src/view/meta/resources/scanUpload.vue": "scanUpload", "/src/view/meta/resources/upload.vue": "Upload", "/src/view/mobile/ad-wizard/index.vue": "<PERSON><PERSON><PERSON><PERSON>", "/src/view/mobile/home/<USER>": "MobileHome", "/src/view/mobile/layout/index.vue": "MobileLayout", "/src/view/mobile/settings/index.vue": "MobileSettings", "/src/view/mobile/system/index.vue": "MobileSystem", "/src/view/mobile/test.vue": "MobileTest", "/src/view/person/person.vue": "Person", "/src/view/routerHolder.vue": "RouterHolder", "/src/view/superAdmin/api/api.vue": "Api", "/src/view/superAdmin/authority/authority.vue": "Authority", "/src/view/superAdmin/authority/components/apis.vue": "Apis", "/src/view/superAdmin/authority/components/datas.vue": "Datas", "/src/view/superAdmin/authority/components/menus.vue": "Menus", "/src/view/superAdmin/dictionary/sysDictionary.vue": "SysDictionary", "/src/view/superAdmin/dictionary/sysDictionaryDetail.vue": "SysDictionaryDetail", "/src/view/superAdmin/index.vue": "SuperAdmin", "/src/view/superAdmin/menu/components/components-cascader.vue": "ComponentsCascader", "/src/view/superAdmin/menu/icon.vue": "Icon", "/src/view/superAdmin/menu/menu.vue": "Menus", "/src/view/superAdmin/operation/sysOperationRecord.vue": "SysOperationRecord", "/src/view/superAdmin/params/sysParams.vue": "SysParams", "/src/view/superAdmin/user/user.vue": "User", "/src/view/system/state.vue": "State", "/src/view/systemTools/autoCode/component/fieldDialog.vue": "FieldDialog", "/src/view/systemTools/autoCode/component/previewCodeDialog.vue": "PreviewCodeDialog", "/src/view/systemTools/autoCode/index.vue": "AutoCode", "/src/view/systemTools/autoCode/mcp.vue": "Mcp", "/src/view/systemTools/autoCode/mcpTest.vue": "McpTest", "/src/view/systemTools/autoCode/picture.vue": "Picture", "/src/view/systemTools/autoCodeAdmin/index.vue": "AutoCodeAdmin", "/src/view/systemTools/autoPkg/autoPkg.vue": "AutoPkg", "/src/view/systemTools/exportTemplate/exportTemplate.vue": "ExportTemplate", "/src/view/systemTools/formCreate/index.vue": "FormGenerator", "/src/view/systemTools/index.vue": "System", "/src/view/systemTools/installPlugin/index.vue": "Index", "/src/view/systemTools/pubPlug/pubPlug.vue": "PubPlug", "/src/view/systemTools/system/system.vue": "Config", "/src/view/test/StatsChartTest.vue": "StatsChartTest", "/src/plugin/announcement/form/info.vue": "InfoForm", "/src/plugin/announcement/view/info.vue": "Info", "/src/plugin/email/view/index.vue": "Email"}