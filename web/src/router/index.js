import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/init',
    name: 'Init',
    component: () => import('@/view/init/index.vue')
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/view/login/index.vue')
  },
  {
    path: '/scanUpload',
    name: 'ScanUpload',
    meta: {
      title: '扫码上传',
      client: true
    },
    component: () => import('@/view/example/upload/scanUpload.vue')
  },
  {
    path: '/crypto-recharge',
    name: 'CryptoRecharge',
    meta: {
      title: '加密货币充值',
      client: true
    },
    component: () => import('@/view/crypto-recharge/index.vue')
  },
  // 移动端路由
  {
    path: '/mobile',
    redirect: '/mobile/home'
  },
  {
    path: '/mobile/home',
    name: 'MobileHome',
    meta: {
      title: '首页',
      mobile: true
    },
    component: () => import('@/view/mobile/home/<USER>')
  },
  {
    path: '/mobile/system',
    name: 'MobileSystem',
    meta: {
      title: '系统',
      mobile: true
    },
    component: () => import('@/view/mobile/system/index.vue')
  },
  {
    path: '/mobile/settings',
    name: 'MobileSettings',
    meta: {
      title: '设置',
      mobile: true
    },
    component: () => import('@/view/mobile/settings/index.vue')
  },
  {
    path: '/mobile/ad-wizard',
    name: 'MobileAdWizard',
    meta: {
      title: '快速添加广告',
      mobile: true
    },
    component: () => import('@/view/mobile/ad-wizard/index.vue')
  },
  {
    path: '/mobile/test',
    name: 'MobileTest',
    meta: {
      title: '移动端测试',
      mobile: true
    },
    component: () => import('@/view/mobile/test.vue')
  },
  {
    path: '/test/stats-chart',
    name: 'StatsChartTest',
    meta: {
      title: '统计图表测试'
    },
    component: () => import('@/view/test/StatsChartTest.vue')
  },
  {
    path: '/:catchAll(.*)',
    meta: {
      closeTab: true
    },
    component: () => import('@/view/error/index.vue')
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
