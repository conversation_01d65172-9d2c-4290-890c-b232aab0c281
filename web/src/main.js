import './style/element_visiable.scss'
import 'element-plus/theme-chalk/dark/css-vars.css'
import { createApp } from 'vue'
import ElementPlus from 'element-plus'

import 'element-plus/dist/index.css'
// 引入gin-vue-admin前端初始化相关内容
import './core/gin-vue-admin'
// 引入封装的router
import router from '@/router/index'
import '@/permission'
import run from '@/core/gin-vue-admin.js'
import auth from '@/directive/auth'
import { store } from '@/pinia'
import App from './App.vue'
// 引入自定义组件
import ElDrawerdialog from '@/components/el-drawerdialog/index.vue'
// 引入设备检测工具
import { initDeviceDetection } from '@/utils/device'

const app = createApp(App)
app.config.productionTip = false

// 全局注册自定义组件
app.component('ElDrawerdialog', ElDrawerdialog)

// 初始化设备检测
initDeviceDetection()

app.use(run).use(ElementPlus).use(store).use(auth).use(router).mount('#app')
export default app
