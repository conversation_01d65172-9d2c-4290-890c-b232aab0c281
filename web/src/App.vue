<template>
  <div
    id="app"
    class="bg-gray-50 text-slate-700 dark:text-slate-500 dark:bg-slate-800"
    :class="{ 'mobile-app': isMobile }"
  >
    <el-config-provider :locale="zhCn">
      <router-view />

      <!-- 移动端底部导航 -->
      <!-- <BottomNavigation v-if="isMobile && shouldShowBottomNav" /> -->
    </el-config-provider>
  </div>
</template>

<script setup>
  import { computed } from 'vue'
  import { useRoute } from 'vue-router'
  import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
  import { useAppStore } from '@/pinia'
  import { useIsMobile } from '@/utils/device'
  import BottomNavigation from '@/components/mobile/BottomNavigation.vue'

  const isMobile = useIsMobile()
  const route = useRoute()

  useAppStore()

  defineOptions({
    name: 'App'
  })

  // 检查是否应该显示底部导航（排除登录页等特殊页面）
  const shouldShowBottomNav = computed(() => {
    const excludeRoutes = ['/login', '/init', '/scanUpload']
    return !excludeRoutes.some(excludeRoute => route.path.startsWith(excludeRoute))
  })
</script>
<style lang="scss">
  // 引入初始化样式
  #app {
    height: 100vh;
    overflow: hidden;
    font-weight: 400 !important;
  }
  .el-button {
    font-weight: 400 !important;
  }

  .gva-body-h {
    min-height: calc(100% - 3rem);
  }

  .gva-container {
    height: calc(100% - 2.5rem);
  }
  .gva-container2 {
    height: calc(100% - 4.5rem);
  }

  // 移动端样式
  .mobile-app {
    height: auto !important;
    overflow: auto !important;

    // 为移动端页面内容添加底部内边距，避免被底部导航遮挡
    .art-layouts,
    .gva-container,
    .gva-container2,
    .art-page-content,
    .art-content-wrapper {
      padding-bottom: 70px;
    }
  }

  // 移动端内容区域
  .mobile-content {
    padding-bottom: 70px; /* 为底部导航留出空间 */
  }
</style>
