<!--
  新的Dashboard页面
  根据用户角色显示不同的统计数据
-->

<template>
  <div class="gva-container2">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
        系统概况
      </h1>
      <p class="text-gray-600 dark:text-gray-400 mt-1">
        欢迎回来，{{ userInfo.nickName }}！这里是您的数据概览。
      </p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center h-64">
      <el-icon class="is-loading text-4xl text-primary">
        <Loading />
      </el-icon>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <el-icon class="text-6xl text-red-500 mb-4">
        <Warning />
      </el-icon>
      <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
      <el-button type="primary" @click="fetchDashboardData">
        重新加载
      </el-button>
    </div>

    <!-- Dashboard内容 -->
    <div v-else class="dashboard-grid">
      <dashboard-card
        v-for="item in dashboardData.items"
        :key="item.id"
        :item="item"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="!loading && !error && (!dashboardData.items || dashboardData.items.length === 0)" 
         class="text-center py-12">
      <el-icon class="text-6xl text-gray-400 mb-4">
        <Document />
      </el-icon>
      <p class="text-gray-600 dark:text-gray-400">暂无数据</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Warning, Document } from '@element-plus/icons-vue'
import { getDashboardData } from '@/api/dashboard'
import { useUserStore } from '@/pinia'
import DashboardCard from './components/dashboard-card.vue'

defineOptions({
  name: 'DashboardNew'
})

const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const error = ref('')
const dashboardData = ref({
  items: []
})

// 计算属性
const userInfo = computed(() => userStore.userInfo)

// 获取Dashboard数据
const fetchDashboardData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const res = await getDashboardData()
    if (res.code === 0) {
      dashboardData.value = res.data
    } else {
      error.value = res.msg || '获取数据失败'
      ElMessage.error(error.value)
    }
  } catch (err) {
    console.error('获取Dashboard数据失败:', err)
    error.value = '网络错误，请稍后重试'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchDashboardData()
})
</script>

<style scoped lang="scss">
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  
  // 响应式布局
  @media (max-width: 1200px) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
  
  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}

// 确保卡片在网格中正确显示
:deep(.col-span-1) {
  grid-column: span 1;
}

:deep(.col-span-2) {
  grid-column: span 2;
}

:deep(.col-span-3) {
  grid-column: span 3;
}

:deep(.col-span-4) {
  grid-column: span 4;
}

// 移动端适配
@media (max-width: 768px) {
  :deep(.col-span-2),
  :deep(.col-span-3),
  :deep(.col-span-4) {
    grid-column: span 2;
  }
}

@media (max-width: 480px) {
  :deep(.col-span-2),
  :deep(.col-span-3),
  :deep(.col-span-4) {
    grid-column: span 1;
  }
}
</style>
