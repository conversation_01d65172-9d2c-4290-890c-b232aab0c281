<!--
  柱状图组件
-->

<template>
  <Chart :height="height" :option="chartOption" />
</template>

<script setup>
import Chart from '@/components/charts/index.vue'
import useChartOption from '@/hooks/charts'
import { graphic } from 'echarts'
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useAppStore } from '@/pinia'

const appStore = useAppStore()
const { config } = storeToRefs(appStore)

const props = defineProps({
  data: {
    type: Object,
    default: () => ({})
  },
  title: {
    type: String,
    default: ''
  },
  height: {
    type: String,
    default: '100%'
  }
})

const isDark = computed(() => appStore.isDark)

const { chartOption } = useChartOption(() => {
  const xAxisData = props.data?.labels || props.data?.xAxis || []
  const seriesData = props.data?.data || []
  const datasets = props.data?.datasets || []

  // 颜色配置
  const colors = [
    config.value.primaryColor,
    '#67C23A',
    '#E6A23C',
    '#F56C6C',
    '#909399',
    '#00D2FF',
    '#FF6B6B'
  ]

  // 构建系列数据
  let series = []

  if (datasets.length > 0) {
    // 多系列数据
    series = datasets.map((dataset, index) => ({
      name: dataset.label,
      type: 'bar',
      data: dataset.data,
      barWidth: '20%',
      itemStyle: {
        color: colors[index % colors.length],
        borderRadius: [4, 4, 0, 0]
      }
    }))
  } else if (seriesData.length > 0) {
    // 单系列数据
    series = [{
      name: props.title,
      type: 'bar',
      data: seriesData,
      barWidth: '40%',
      itemStyle: {
        color: new graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: config.value.primaryColor
          },
          {
            offset: 1,
            color: `${config.value.primaryColor}80`
          }
        ]),
        borderRadius: [4, 4, 0, 0]
      }
    }]
  }

  const option = {
    grid: {
      left: '3%',
      right: '4%',
      bottom: datasets.length > 1 ? '15%' : '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: {
        color: isDark.value ? '#9ca3af' : '#6b7280'
      },
      axisLine: {
        lineStyle: {
          color: isDark.value ? '#374151' : '#e5e7eb'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: isDark.value ? '#9ca3af' : '#6b7280'
      },
      splitLine: {
        lineStyle: {
          color: isDark.value ? '#374151' : '#e5e7eb'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: isDark.value ? '#1f2937' : '#ffffff',
      borderColor: isDark.value ? '#374151' : '#e5e7eb',
      textStyle: {
        color: isDark.value ? '#f9fafb' : '#111827'
      },
      axisPointer: {
        type: 'shadow'
      }
    },
    series
  }

  // 如果有多个系列，添加图例
  if (datasets.length > 1) {
    option.legend = {
      data: datasets.map(d => d.label),
      bottom: 0,
      icon: 'rect',
      itemWidth: 14,
      itemHeight: 10,
      itemGap: 15,
      textStyle: {
        fontSize: 12,
        color: isDark.value ? '#9ca3af' : '#6b7280'
      }
    }
  }

  return option
})
</script>

<style scoped lang="scss"></style>
