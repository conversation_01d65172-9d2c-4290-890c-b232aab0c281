<!--
  Dashboard统计卡片组件
  支持显示数值、趋势、图表等信息
  使用与 stat-card 一致的UI样式
-->

<template>
  <div
    class="dashboard-item stat-card"
    :class="[
      `col-span-${item.colSpan || 1}`,
      item.type === 'card'
        ? 'dashboard-card'
        : item.type === 'multi_chart'
        ? 'dashboard-multi-chart'
        : 'dashboard-chart'
    ]"
  >
    <!-- 数值类型卡片 - 使用 stat-card 布局 -->
    <template v-if="item.type === 'card'">
      <!-- 图标区域 -->
      <div
        class="stat-icon"
        :class="getCardColorScheme()"
      >
        <el-icon>
          <component :is="getCardIcon()" />
        </el-icon>
      </div>

      <!-- 内容区域 -->
      <div class="stat-content">
        <!-- 主要数值 -->
        <div class="stat-value">
          {{ formatValue(item.value) }}
          <span v-if="item.unit" class="text-sm text-gray-500 ml-1">{{
            item.unit
          }}</span>
        </div>

        <!-- 标题/标签 -->
        <div class="stat-label">{{ item.title }}</div>

        <!-- 趋势信息 -->
        <div
          v-if="item.trend"
          class="stat-trend"
          :class="getTrendClass(item.trend.percent)"
        >
          <el-icon>
            <TopRight v-if="item.trend.isUp" />
            <BottomRight v-else />
          </el-icon>
          {{ formatTrendPercent(item.trend.percent) }}
        </div>

        <!-- 描述信息 -->
        <!-- <div v-else-if="item.description" class="stat-trend text-gray-500">
          {{ item.description }}
        </div> -->
      </div>

      <!-- 小图表区域 -->
      <div v-if="item.chartData" class="mini-chart-container">
        <charts-mini-line :data="item.chartData" />
      </div>
    </template>

    <!-- 图表类型卡片 - 保持原有布局但使用 stat-card 样式基础 -->
    <template v-else>
      <!-- 卡片标题 -->
      <div class="chart-header">
        <div class="chart-title">
          {{ item.title }}
        </div>
        <div v-if="item.description" class="chart-description">
          {{ item.description }}
        </div>
      </div>

      <!-- 图表内容 -->
      <div class="chart-content">
        <charts-line
          v-if="item.type === 'line_chart'"
          :data="item.chartData"
          :title="item.title"
        />
        <charts-bar
          v-else-if="item.type === 'bar_chart'"
          :data="item.chartData"
          :title="item.title"
        />
        <charts-pie
          v-else-if="item.type === 'pie_chart'"
          :data="item.chartData"
          :title="item.title"
        />
        <charts-multi
          v-else-if="item.type === 'multi_chart'"
          :title="item.title"
          :charts="item.charts || []"
          :initial-data="item.chartData"
        />
      </div>
    </template>
  </div>
</template>

<script setup>
  import {
    TopRight,
    BottomRight,
    Grid,
    // 图标列表
    TrendCharts,
    DataAnalysis,
    Money,
    User,
    ShoppingCart,
    View,
    Clock,
    Star,
    Trophy,
    Wallet,
    VideoPlay,
    Mouse,
    Warning,
    Document,
    Setting,
    Monitor
  } from '@element-plus/icons-vue'
  import ChartsMiniLine from './charts-mini-line.vue'
  import ChartsLine from './charts-line.vue'
  import ChartsBar from './charts-bar.vue'
  import ChartsPie from './charts-pie.vue'
  import ChartsMulti from './charts-multi.vue'

  const props = defineProps({
    item: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      default: 0
    }
  })

  // 图标列表 - 用于循环选择
  const iconList = [
    TrendCharts,
    DataAnalysis,
    Money,
    User,
    ShoppingCart,
    View,
    Clock,
    Star,
    Trophy,
    Wallet,
    VideoPlay,
    Mouse,
    Warning,
    Document,
    Setting,
    Monitor
  ]

  // 配色方案列表 - 用于循环选择
  const colorSchemes = [
    'bg-blue-100 text-blue-600',
    'bg-green-100 text-green-600',
    'bg-purple-100 text-purple-600',
    'bg-orange-100 text-orange-600',
    'bg-red-100 text-red-600',
    'bg-indigo-100 text-indigo-600',
    'bg-pink-100 text-pink-600',
    'bg-teal-100 text-teal-600',
    'bg-yellow-100 text-yellow-600',
    'bg-cyan-100 text-cyan-600',
    'bg-emerald-100 text-emerald-600',
    'bg-violet-100 text-violet-600',
    'bg-rose-100 text-rose-600',
    'bg-amber-100 text-amber-600',
    'bg-lime-100 text-lime-600',
    'bg-sky-100 text-sky-600'
  ]

  // 根据index获取图标
  const getCardIcon = () => {
    if (props.item.icon) {
      return props.item.icon
    }
    return iconList[props.index % iconList.length]
  }

  // 根据index获取配色
  const getCardColorScheme = () => {
    if (props.item.iconClass) {
      return props.item.iconClass
    }
    return colorSchemes[props.index % colorSchemes.length]
  }

  // 格式化数值显示
  const formatValue = (value) => {
    if (typeof value !== 'number') return value

    if (value >= 10000) {
      return (value / 10000).toFixed(1) + '万'
    } else if (value >= 1000) {
      return (value / 1000).toFixed(1) + 'k'
    }
    return value.toLocaleString()
  }

  // 格式化趋势百分比
  const formatTrendPercent = (percent) => {
    if (typeof percent !== 'number') return ''

    const sign = percent >= 0 ? '+' : ''
    return `${sign}${percent.toFixed(1)}%`
  }

  // 获取趋势样式类
  const getTrendClass = (trend) => {
    if (typeof trend === 'number') {
      return trend > 0
        ? 'text-green-500'
        : trend < 0
        ? 'text-red-500'
        : 'text-gray-500'
    }
    if (typeof trend === 'object' && trend.isUp !== undefined) {
      return trend.isUp ? 'text-green-500' : 'text-red-500'
    }
    return 'text-gray-500'
  }
</script>

<style scoped lang="scss">
  // 基础网格布局
  .col-span-1 {
    grid-column: span 1;
  }
  .col-span-2 {
    grid-column: span 2;
  }
  .col-span-3 {
    grid-column: span 3;
  }
  .col-span-4 {
    grid-column: span 4;
  }

  // Dashboard项目样式 - 基于 stat-card 样式
  .dashboard-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .dark & {
      background: #1f2937;
      border-color: #374151;
    }

    // Card类型样式 - 使用 stat-card 的 flex 布局
    &.dashboard-card {
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;
      min-height: 30px;
      max-height: 120px;

      .stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        flex-shrink: 0;
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 1.5rem;
          font-weight: 700;
          color: #1f2937;
          line-height: 1.2;

          .dark & {
            color: #f9fafb;
          }
        }

        .stat-label {
          font-size: 0.75rem;
          color: #6b7280;
          margin: 0.25rem 0;

          .dark & {
            color: #9ca3af;
          }
        }

        .stat-trend {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-size: 0.75rem;
          font-weight: 500;
        }
      }

      .mini-chart-container {
        width: 160px;
        height: 40px;
        flex-shrink: 0;
      }
    }

    // Chart类型样式 - 垂直布局
    &.dashboard-chart {
      display: block;
      min-height: 320px;

      .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;

        .chart-title {
          font-size: 1rem;
          font-weight: 600;
          color: #1f2937;

          .dark & {
            color: #f9fafb;
          }
        }

        .chart-description {
          font-size: 0.75rem;
          color: #6b7280;

          .dark & {
            color: #9ca3af;
          }
        }
      }

      .chart-content {
        height: 280px;
      }
    }

    // Multi Chart类型样式 - 需要更多空间容纳切换按钮
    &.dashboard-multi-chart {
      display: block;
      min-height: 380px;

      .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 1rem;

        .chart-title {
          font-size: 1rem;
          font-weight: 600;
          color: #1f2937;

          .dark & {
            color: #f9fafb;
          }
        }

        .chart-description {
          font-size: 0.75rem;
          color: #6b7280;

          .dark & {
            color: #9ca3af;
          }
        }
      }

      .chart-content {
        height: 320px;
      }
    }
  }

  // 响应式适配
  @media (max-width: 768px) {
    .dashboard-item {
      &.dashboard-card {
        min-height: 80px;
        max-height: 120px;
        padding: 0.5rem;
        gap: 0.5rem;

        .stat-icon {
          width: 32px;
          height: 32px;
          font-size: 1rem;
        }

        .stat-content {
          .stat-value {
            font-size: 1.125rem;
          }

          .stat-label {
            font-size: 0.6875rem;
            margin: 0.125rem 0;
          }

          .stat-trend {
            font-size: 0.6875rem;
            gap: 0.125rem;
          }
        }

        .mini-chart-container {
          width: 40px;
          height: 28px;
        }
      }

      &.dashboard-chart {
        min-height: 280px;

        .chart-content {
          height: 240px;
        }
      }

      &.dashboard-multi-chart {
        min-height: 340px;

        .chart-content {
          height: 280px;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .dashboard-item {
      &.dashboard-card {
        min-height: 70px;
        max-height: 100px;
        padding: 0.375rem;
        gap: 0.375rem;

        .stat-icon {
          width: 28px;
          height: 28px;
          font-size: 0.875rem;
        }

        .stat-content {
          .stat-value {
            font-size: 1rem;
            line-height: 1.1;
          }

          .stat-label {
            font-size: 0.625rem;
            margin: 0.0625rem 0;
          }

          .stat-trend {
            font-size: 0.625rem;
            gap: 0.0625rem;
          }
        }

        .mini-chart-container {
          width: 35px;
          height: 24px;
        }
      }

      &.dashboard-chart {
        min-height: 260px;

        .chart-content {
          height: 220px;
        }
      }

      &.dashboard-multi-chart {
        min-height: 320px;

        .chart-content {
          height: 260px;
        }
      }
    }
  }

  // 超小屏幕优化（320px以下）- 为3列布局进一步优化
  @media (max-width: 320px) {
    .dashboard-item {
      &.dashboard-card {
        min-height: 65px;
        max-height: 90px;
        padding: 0.25rem;
        gap: 0.25rem;

        .stat-icon {
          width: 24px;
          height: 24px;
          font-size: 0.75rem;
        }

        .stat-content {
          .stat-value {
            font-size: 0.875rem;
            line-height: 1;
          }

          .stat-label {
            font-size: 0.5rem;
            margin: 0;
          }

          .stat-trend {
            font-size: 0.5rem;
            gap: 0;
          }
        }

        .mini-chart-container {
          width: 30px;
          height: 20px;
        }
      }
    }
  }
</style>
