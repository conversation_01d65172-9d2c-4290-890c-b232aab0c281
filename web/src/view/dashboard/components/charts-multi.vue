<!--
  多图表组合组件
  支持在同一个图表容器中切换显示不同的图表
-->

<template>
  <div class="multi-chart-container">
    <!-- 图表标题和切换按钮 -->
    <div class="chart-header">
      <h3 class="chart-title"></h3>
      <div class="chart-tabs">
        <button
          v-for="chart in charts"
          :key="chart.id"
          class="chart-tab"
          :class="{ active: activeChartId === chart.id }"
          @click="switchChart(chart.id)"
        >
          {{ chart.title }}
        </button>
      </div>
    </div>

    <!-- 图表内容 -->
    <div class="chart-content">
      <div v-if="loading" class="chart-loading">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <span>加载中...</span>
      </div>
      
      <div v-else-if="error" class="chart-error">
        <el-icon>
          <Warning />
        </el-icon>
        <span>{{ error }}</span>
      </div>
      
      <ChartsLine
        v-else
        :data="currentChartData"
        :title="currentChartTitle"
        height="100%"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElIcon, ElMessage } from 'element-plus'
import { Loading, Warning } from '@element-plus/icons-vue'
import ChartsLine from './charts-line.vue'
import { getChartData } from '@/api/dashub-dashboard'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  charts: {
    type: Array,
    required: true
  },
  initialData: {
    type: Object,
    default: () => ({})
  }
})

// 响应式数据
const activeChartId = ref(null)
const currentChartData = ref({})
const loading = ref(false)
const error = ref('')

// 计算属性
const currentChartTitle = computed(() => {
  const chart = props.charts.find(c => c.id === activeChartId.value)
  return chart ? chart.title : ''
})

// 切换图表
const switchChart = async (chartId) => {
  if (activeChartId.value === chartId) return
  
  activeChartId.value = chartId
  await loadChartData(chartId)
}

// 加载图表数据
const loadChartData = async (chartId) => {
  loading.value = true
  error.value = ''
  
  try {
    const res = await getChartData(chartId)
    if (res.code === 0) {
      currentChartData.value = res.data
    } else {
      error.value = res.msg || '获取图表数据失败'
      ElMessage.error(error.value)
    }
  } catch (err) {
    error.value = '网络请求失败'
    ElMessage.error(error.value)
    console.error('加载图表数据失败:', err)
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  if (props.charts.length > 0) {
    // 设置默认激活的图表
    activeChartId.value = props.charts[0].id
    
    // 如果有初始数据，使用初始数据，否则加载数据
    if (props.initialData && Object.keys(props.initialData).length > 0) {
      currentChartData.value = props.initialData
    } else {
      loadChartData(activeChartId.value)
    }
  }
})

// 监听charts变化
watch(() => props.charts, (newCharts) => {
  if (newCharts.length > 0 && !activeChartId.value) {
    activeChartId.value = newCharts[0].id
    loadChartData(activeChartId.value)
  }
}, { immediate: true })
</script>

<style scoped lang="scss">
.multi-chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
  
  .chart-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    flex-shrink: 0;
  }
  
  .chart-tabs {
    display: flex;
    gap: 0.5rem;
  }
  
  .chart-tab {
    padding: 0.375rem 0.75rem;
    border: 1px solid #d1d5db;
    background: #f9fafb;
    color: #6b7280;
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.8rem;
    white-space: nowrap;
    
    &:hover {
      background: #f3f4f6;
      border-color: #9ca3af;
    }
    
    &.active {
      background: #3b82f6;
      border-color: #3b82f6;
      color: white;
    }
  }
}

.chart-content {
  flex: 1;
  min-height: 0; // 允许flex子项收缩
  position: relative;
  overflow: hidden;
  
  .chart-loading,
  .chart-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #6b7280;
    
    .el-icon {
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }
  }
  
  .chart-error {
    color: #ef4444;
  }
}

// 暗色主题适配
.dark {
  .chart-header {
    border-bottom-color: #374151;
    
    .chart-title {
      color: #f9fafb;
    }
    
    .chart-tab {
      background: #374151;
      border-color: #4b5563;
      color: #d1d5db;
      
      &:hover {
        background: #4b5563;
        border-color: #6b7280;
      }
      
      &.active {
        background: #3b82f6;
        border-color: #3b82f6;
        color: white;
      }
    }
  }
  
  .chart-loading,
  .chart-error {
    color: #9ca3af;
  }
  
  .chart-error {
    color: #f87171;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    padding-bottom: 0.375rem;

    .chart-title {
      font-size: 0.9rem;
    }

    .chart-tabs {
      width: 100%;
      overflow-x: auto;
      padding-bottom: 0.25rem;
      gap: 0.375rem;

      .chart-tab {
        white-space: nowrap;
        flex-shrink: 0;
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
      }
    }
  }

  .chart-content {
    min-height: 0;
  }
}
</style>
