<!--
  Dashboard快捷功能区组件
  包含快捷操作按钮和系统统计数据
-->

<template>
  <div class="dashboard-quick-actions">
    <!-- 快捷操作按钮区域 -->
    <div class="quick-actions-section">
      <div class="section-header">
        <h3 class="section-title">
          <el-icon class="mr-2"><Lightning /></el-icon>
          快捷操作
        </h3>
      </div>

      <div class="actions-grid">
        <!-- 快速创建广告 -->
        <div class="action-card" @click="handleQuickCreateAd">
          <div class="action-icon bg-blue-500">
            <el-icon><Plus /></el-icon>
          </div>
          <div class="action-content">
            <h4>创建广告</h4>
            <p>快速创建新的广告</p>
          </div>
        </div>

        <!-- 查看统计 -->
        <div class="action-card" @click="handleViewStatistics">
          <div class="action-icon bg-green-500">
            <el-icon><DataAnalysis /></el-icon>
          </div>
          <div class="action-content">
            <h4>查看统计</h4>
            <p>广告数据统计分析</p>
          </div>
        </div>

        <!-- 广告管理 -->
        <div class="action-card" @click="handleManageAds">
          <div class="action-icon bg-purple-500">
            <el-icon><Grid /></el-icon>
          </div>
          <div class="action-content">
            <h4>广告管理</h4>
            <p>管理所有广告内容</p>
          </div>
        </div>

        <!-- 投放计划 -->
        <div class="action-card" @click="handleManageCampaigns">
          <div class="action-icon bg-orange-500">
            <el-icon><Calendar /></el-icon>
          </div>
          <div class="action-content">
            <h4>投放计划</h4>
            <p>管理广告投放计划</p>
          </div>
        </div>

        <!-- 广告审核 -->
        <div class="action-card" @click="handleAdReview">
          <div class="action-icon bg-red-500">
            <el-icon><Document /></el-icon>
          </div>
          <div class="action-content">
            <h4>广告审核</h4>
            <p>审核待审广告内容</p>
          </div>
        </div>

        <!-- 财务管理 -->
        <div class="action-card" @click="handleFinanceManage">
          <div class="action-icon bg-yellow-500">
            <el-icon><Wallet /></el-icon>
          </div>
          <div class="action-content">
            <h4>财务管理</h4>
            <p>查看收支和充值</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue'
  import { useRouter } from 'vue-router'
  import { ElMessage } from 'element-plus'
  import {
    Lightning,
    Plus,
    DataAnalysis,
    Grid,
    Calendar,
    Document,
    Wallet,
    DataBoard,
    Refresh,
    VideoPlay,
    Mouse,
    Money,
    Warning,
    Clock,
    TopRight,
    BottomRight,
    Minus
  } from '@element-plus/icons-vue'
  // 导入API
  import {
    getDashboardStats,
    getAdOverview,
    getFinanceStats,
    getTodayStats
  } from '@/api/dashboard-stats'

  // 路由
  const router = useRouter()


  // 快捷操作方法
  const handleQuickCreateAd = () => {
    router.push('/layout/publish/ad')
  }

  const handleViewStatistics = () => {
    router.push('/layout/statistics/adstatistics')
  }

  const handleManageAds = () => {
    router.push('/layout/publish/ad')
  }

  const handleManageCampaigns = () => {
    router.push('/layout/publish/campaign')
  }

  const handleAdReview = () => {
    router.push('/layout/review/adreview')
  }

  const handleFinanceManage = () => {
    router.push('/layout/recharge/consumerecord')
  }

  
</script>

<style scoped lang="scss">
  .dashboard-quick-actions {
    margin-bottom: 2rem;
  }

  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
      margin: 0;

      .dark & {
        color: #f9fafb;
      }
    }
  }

  // 快捷操作区域
  .quick-actions-section {
    margin-bottom: 2rem;

    .actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;

      @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
      }

      @media (max-width: 480px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
      }

      @media (max-width: 320px) {
        gap: 0.375rem;
      }
    }

    .action-card {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 1rem;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 0.75rem;

      &:hover {
        border-color: #3b82f6;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        transform: translateY(-2px);
      }

      .dark & {
        background: #1f2937;
        border-color: #374151;

        &:hover {
          border-color: #3b82f6;
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
        }
      }

      .action-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
        flex-shrink: 0;
      }

      .action-content {
        flex: 1;

        h4 {
          margin: 0 0 0.25rem 0;
          font-size: 0.875rem;
          font-weight: 600;
          color: #1f2937;

          .dark & {
            color: #f9fafb;
          }
        }

        p {
          margin: 0;
          font-size: 0.75rem;
          color: #6b7280;

          .dark & {
            color: #9ca3af;
          }
        }
      }
    }
  }

  // 系统统计区域
  .system-stats-section {
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;

      @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
      }

      @media (max-width: 480px) {
        grid-template-columns: 1fr;
      }
    }

    .stat-card {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 1rem;
      display: flex;
      align-items: flex-start;
      gap: 0.75rem;

      .dark & {
        background: #1f2937;
        border-color: #374151;
      }

      .stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        flex-shrink: 0;
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 1.5rem;
          font-weight: 700;
          color: #1f2937;
          line-height: 1.2;

          .dark & {
            color: #f9fafb;
          }
        }

        .stat-label {
          font-size: 0.75rem;
          color: #6b7280;
          margin: 0.25rem 0;

          .dark & {
            color: #9ca3af;
          }
        }

        .stat-trend {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-size: 0.75rem;
          font-weight: 500;
        }
      }
    }
  }

  // 响应式优化
  @media (max-width: 768px) {
    .dashboard-quick-actions {
      margin-bottom: 1.5rem;
    }

    .quick-actions-section {
      margin-bottom: 1.5rem;
    }

    .section-header {
      .section-title {
        font-size: 1rem;
      }
    }
  }

  @media (max-width: 480px) {
    .dashboard-quick-actions {
      margin-bottom: 1rem;
    }

    .quick-actions-section {
      margin-bottom: 1rem;
    }

    .action-card {
      padding: 0.5rem !important;
      gap: 0.5rem;

      .action-icon {
        width: 28px !important;
        height: 28px !important;
        font-size: 0.875rem !important;
      }

      .action-content {
        h4 {
          font-size: 0.75rem;
          margin-bottom: 0.125rem;
        }

        p {
          font-size: 0.625rem;
          line-height: 1.2;
        }
      }
    }
  }

  @media (max-width: 320px) {
    .action-card {
      padding: 0.375rem !important;
      gap: 0.375rem;

      .action-icon {
        width: 24px !important;
        height: 24px !important;
        font-size: 0.75rem !important;
      }

      .action-content {
        h4 {
          font-size: 0.6875rem;
          margin-bottom: 0.0625rem;
        }

        p {
          font-size: 0.5625rem;
          line-height: 1.1;
        }
      }
    }

    .stat-card {
      padding: 0.75rem !important;

      .stat-icon {
        width: 32px !important;
        height: 32px !important;
        font-size: 1rem !important;
      }

      .stat-content {
        .stat-value {
          font-size: 1.25rem;
        }
      }
    }
  }
</style>
