<!--
  迷你折线图组件
  用于在卡片中显示简单的趋势图
-->

<template>
  <Chart :height="height" :option="chartOption" />
</template>

<script setup>
import Chart from '@/components/charts/index.vue'
import useChartOption from '@/hooks/charts'
import { graphic } from 'echarts'
import { storeToRefs } from 'pinia'
import { useAppStore } from '@/pinia'

const appStore = useAppStore()
const { config } = storeToRefs(appStore)

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  height: {
    type: String,
    default: '100%'
  }
})

const { chartOption } = useChartOption(() => {
  return {
    grid: {
      left: '0',
      right: '0',
      top: '0',
      bottom: '0'
    },
    xAxis: {
      type: 'category',
      show: false,
      boundaryGap: false,
      data: props.data.map((_, index) => index)
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [
      {
        data: props.data,
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: {
          width: 2,
          color: config.value.primaryColor
        },
        areaStyle: {
          opacity: 0.3,
          color: new graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: `${config.value.primaryColor}40`
            },
            {
              offset: 1,
              color: `${config.value.primaryColor}10`
            }
          ])
        }
      }
    ]
  }
})
</script>

<style scoped lang="scss"></style>
