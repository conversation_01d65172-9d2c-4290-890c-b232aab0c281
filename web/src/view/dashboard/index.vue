<!--
  新的Dashboard页面
  根据用户角色显示不同的统计数据
-->

<template>
  <div class="gva-container2">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white"></h1>
      <!--
      <p class="text-gray-600 dark:text-gray-400 mt-1">
        欢迎回来，{{ userInfo.nickName }}！这里是您的数据概览。
      </p>
    --></div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center h-64">
      <el-icon class="is-loading text-4xl text-primary">
        <Loading />
      </el-icon>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <el-icon class="text-6xl text-red-500 mb-4">
        <Warning />
      </el-icon>
      <p class="text-gray-600 dark:text-gray-400 mb-4">{{ error }}</p>
      <el-button type="primary" @click="fetchDashboardData">
        重新加载
      </el-button>
    </div>

    <!-- Dashboard内容 -->
    <div v-else class="dashboard-container">
      <!-- 快捷功能区 -->
      <dashboard-quick-actions />

      <div class="section-header">
        <h3 class="section-title">
          <el-icon class="mr-2"><DataBoard /></el-icon>
          系统概览
        </h3>
        <el-button
          size="small"
          type="primary"
          link
          @click="refreshStats"
          :loading="statsLoading"
        >
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>

      <!-- Card区域 - 优先显示 -->
      <div
        v-if="cardItems.length > 0"
        class="dashboard-cards"
        :style="{ gridTemplateColumns: cardGridColumns }"
      >
        <dashboard-card
          v-for="(item, index) in cardItems"
          :key="item.id"
          :item="item"
          :index="index"
        />
      </div>

      <!-- Chart区域 -->
      <div
        v-if="chartItems.length > 0"
        class="dashboard-charts"
        :style="{ gridTemplateColumns: chartGridColumns }"
      >
        <dashboard-card
          v-for="item in chartItems"
          :key="item.id"
          :item="item"
        />
      </div>
    </div>

    <!-- 空状态 -->
    <div
      v-if="
        !loading &&
        !error &&
        (!dashboardData.items || dashboardData.items.length === 0)
      "
      class="text-center py-12"
    >
      <el-icon class="text-6xl text-gray-400 mb-4">
        <Document />
      </el-icon>
      <p class="text-gray-600 dark:text-gray-400">暂无数据</p>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, computed } from 'vue'
  import { ElMessage } from 'element-plus'
  import { Loading, Warning, Document } from '@element-plus/icons-vue'
  import { getDashubDashboardData } from '@/api/dashub-dashboard'
  import { useUserStore } from '@/pinia'
  import DashboardCard from './components/dashboard-card.vue'
  import DashboardQuickActions from './components/dashboard-quick-actions.vue'

  defineOptions({
    name: 'Dashboard'
  })

  const userStore = useUserStore()

  // 响应式数据
  const loading = ref(false)
  const error = ref('')
  const dashboardData = ref({
    items: []
  })

  // 计算属性
  const userInfo = computed(() => userStore.userInfo)

  // 分离Card和Chart数据，Card优先显示
  const cardItems = computed(() => {
    return (
      dashboardData.value.items?.filter((item) => item.type === 'card') || []
    )
  })

  const chartItems = computed(() => {
    return (
      dashboardData.value.items?.filter((item) => item.type !== 'card') || []
    )
  })

  // 响应式屏幕尺寸检测
  const screenWidth = ref(window.innerWidth)

  // 监听窗口大小变化
  const updateScreenWidth = () => {
    screenWidth.value = window.innerWidth
  }

  // 计算Card区域的网格列数 - 根据屏幕尺寸动态调整
  const cardGridColumns = computed(() => {
    const items = cardItems.value
    console.log('=== Card Grid Debug ===')
    console.log('Card items:', items)
    console.log('Screen width:', screenWidth.value)

    // 根据屏幕尺寸决定列数 - 优化移动端显示更多卡片
    let result
    if (screenWidth.value <= 320) {
      result = 'repeat(2, 1fr)' // 超小屏手机：2列
    } else if (screenWidth.value <= 480) {
      result = 'repeat(3, 1fr)' // 小屏手机：3列（紧凑布局）
    } else if (screenWidth.value <= 768) {
      result = 'repeat(3, 1fr)' // 手机端：3列
    } else if (screenWidth.value <= 1200) {
      result = 'repeat(4, 1fr)' // 平板端：4列
    } else {
      result = 'repeat(4, 1fr)' // 电脑端：4列
    }

    console.log('Card grid columns result:', result)
    return result
  })

  // 计算Chart区域的网格列数 - 根据屏幕尺寸动态调整
  const chartGridColumns = computed(() => {
    const items = chartItems.value
    console.log('=== Chart Grid Debug ===')
    console.log('Chart items:', items)
    console.log('Screen width:', screenWidth.value)

    // 根据屏幕尺寸决定列数
    let result
    if (screenWidth.value <= 768) {
      result = '1fr' // 手机端：1列
    } else if (screenWidth.value <= 1200) {
      result = 'repeat(2, 1fr)' // 平板端：2列
    } else {
      result = 'repeat(4, 1fr)' // 电脑端：4列
    }

    console.log('Chart grid columns result:', result)
    return result
  })

  // 获取Dashboard数据
  const fetchDashboardData = async () => {
    loading.value = true
    error.value = ''

    try {
      const res = await getDashubDashboardData()
      if (res.code === 0) {
        dashboardData.value = res.data
      } else {
        error.value = res.msg || '获取数据失败'
        ElMessage.error(error.value)
      }
    } catch (err) {
      console.error('获取Dashboard数据失败:', err)
      error.value = '网络错误，请稍后重试'
      ElMessage.error(error.value)
    } finally {
      loading.value = false
    }
  }

    // 刷新统计数据
  const refreshStats = () => {
    fetchDashboardData()
  }

  // 组件挂载时获取数据和监听窗口变化
  onMounted(() => {
    fetchDashboardData()

    // 监听窗口大小变化
    window.addEventListener('resize', updateScreenWidth)
  })

  // 组件卸载时移除监听器
  onUnmounted(() => {
    window.removeEventListener('resize', updateScreenWidth)
  })
</script>

<style scoped lang="scss">
  .dashboard-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  // Card区域样式 - 动态网格布局，由JS控制列数
  .dashboard-cards {
    display: grid;
    gap: 1rem;
    margin-bottom: 0.5rem;
    width: 100%;
    // grid-template-columns 由 :style 动态设置

    // 移动端减小间距以容纳更多卡片
    @media (max-width: 768px) {
      gap: 0.75rem;
    }

    @media (max-width: 480px) {
      gap: 0.5rem;
    }

    @media (max-width: 320px) {
      gap: 0.375rem;
    }
  }

  // Chart区域样式 - 动态网格布局，由JS控制列数
  .dashboard-charts {
    display: grid;
    gap: 1rem;
    width: 100%;
    // grid-template-columns 由 :style 动态设置

    // 移动端减小间距
    @media (max-width: 768px) {
      gap: 0.75rem;
    }

    @media (max-width: 480px) {
      gap: 0.5rem;
    }

    @media (max-width: 320px) {
      gap: 0.375rem;
    }
  }

  // 智能colSpan处理 - 根据可用空间自动调整
  :deep(.col-span-1) {
    grid-column: span 1;
  }

  :deep(.col-span-2) {
    grid-column: span 2;
    // 在小屏幕上占满整行
    @media (max-width: 768px) {
      grid-column: 1 / -1;
    }
  }

  :deep(.col-span-3) {
    grid-column: span 3;
    // 在平板端占2列
    @media (max-width: 1200px) and (min-width: 769px) {
      grid-column: span 2;
    }
    // 在手机端占满整行
    @media (max-width: 768px) {
      grid-column: 1 / -1;
    }
  }

  :deep(.col-span-4) {
    grid-column: 1 / -1; // 总是占满整行
  }

  // 移动端列跨度优化 - 适应新的3列布局
  @media (max-width: 768px) {
    :deep(.col-span-1) {
      grid-column: span 1; // 保持单列
    }

    // 超小屏幕（320px以下）回到2列布局
    @media (max-width: 320px) {
      :deep(.col-span-1) {
        grid-column: span 1;
      }
    }
  }

  // 优化间距和视觉效果
  .dashboard-container {
    padding: 0;

    @media (max-width: 768px) {
      gap: 1rem;
    }

    @media (max-width: 480px) {
      gap: 0.75rem;
    }

    @media (max-width: 320px) {
      gap: 0.5rem;
    }
  }
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;
      margin: 0;

      .dark & {
        color: #f9fafb;
      }
    }
  }
</style>
