
<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" @keyup.enter="onSubmit">
      <el-form-item label="创建日期" prop="createdAtRange">
      <template #label>
        <span>
          创建日期
          <el-tooltip content="搜索范围是开始日期（包含）至结束日期（不包含）">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </span>
      </template>

      <el-date-picker
            v-model="searchInfo.createdAtRange"
            :class="isMobile ? 'w-full' : 'w-[380px]'"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
       </el-form-item>
      

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
        @selection-change="handleSelectionChange"
        @sort-change="sortChange"
        >
        <el-table-column type="selection" width="55" />
        
        <el-table-column sortable align="left" label="日期" prop="CreatedAt"width="180">
            <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>
        
            <el-table-column align="left" label="设备ID" prop="device_id" width="120" />

            <el-table-column align="left" label="APP ID" prop="app_id" width="120">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.app_id,scope.row.app_id) }}</span>
    </template>
</el-table-column>
            <el-table-column align="left" label="平台" prop="platform" width="120" />

            <el-table-column align="left" label="设备类型" prop="device_type" width="120" />

            <el-table-column align="left" label="设备品牌" prop="device_brand" width="120" />

            <el-table-column align="left" label="设备型号" prop="device_model" width="120" />

            <el-table-column align="left" label="系统版本" prop="os_version" width="120" />

            <el-table-column align="left" label="屏幕宽度" prop="screen_width" width="120" />

            <el-table-column align="left" label="屏幕高度" prop="screen_height" width="120" />

            <el-table-column align="left" label="系统语言" prop="language" width="120" />

            <el-table-column align="left" label="国家" prop="country" width="120" />

            <el-table-column align="left" label="省份" prop="province" width="120" />

            <el-table-column align="left" label="城市" prop="city" width="120" />

            <el-table-column align="left" label="网络类型" prop="network_type" width="120" />

            <el-table-column align="left" label="运营商" prop="carrier" width="120" />

            <el-table-column sortable align="left" label="首次出现时间" prop="first_seen_time" width="180">
   <template #default="scope">{{ formatDate(scope.row.first_seen_time) }}</template>
</el-table-column>
            <el-table-column sortable align="left" label="最后出现时间" prop="last_seen_time" width="180">
   <template #default="scope">{{ formatDate(scope.row.last_seen_time) }}</template>
</el-table-column>
            <el-table-column align="left" label="是否活跃" prop="is_active" width="120">
    <template #default="scope">{{ formatBoolean(scope.row.is_active) }}</template>
</el-table-column>
        <el-table-column align="left" label="操作" :fixed="isMobile ? false : 'right'" :min-width="isMobile ? '210px' : appStore.operateMinWith">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateDeviceDataFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawerdialog destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
            </div>
          </template>

          <el-form :model="formData" label-position="right" ref="elFormRef" :rules="rule" label-width="100px">
          </el-form>
          <template #footer>
              <div>
                <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                <el-button @click="closeDialog">取 消</el-button>
              </div>
          </template>
    </el-drawerdialog>

    <el-drawerdialog destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="设备ID">
    {{ detailFrom.device_id }}
</el-descriptions-item>
                    <el-descriptions-item label="APP ID">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.app_id,detailFrom.app_id) }}</span>
    </template>
</el-descriptions-item>
                    <el-descriptions-item label="用户ID">
    {{ detailFrom.user_id }}
</el-descriptions-item>
                    <el-descriptions-item label="平台">
    {{ detailFrom.platform }}
</el-descriptions-item>
                    <el-descriptions-item label="设备类型">
    {{ detailFrom.device_type }}
</el-descriptions-item>
                    <el-descriptions-item label="设备品牌">
    {{ detailFrom.device_brand }}
</el-descriptions-item>
                    <el-descriptions-item label="设备型号">
    {{ detailFrom.device_model }}
</el-descriptions-item>
                    <el-descriptions-item label="系统版本">
    {{ detailFrom.os_version }}
</el-descriptions-item>
                    <el-descriptions-item label="屏幕宽度">
    {{ detailFrom.screen_width }}
</el-descriptions-item>
                    <el-descriptions-item label="屏幕高度">
    {{ detailFrom.screen_height }}
</el-descriptions-item>
                    <el-descriptions-item label="系统语言">
    {{ detailFrom.language }}
</el-descriptions-item>
                    <el-descriptions-item label="国家">
    {{ detailFrom.country }}
</el-descriptions-item>
                    <el-descriptions-item label="省份">
    {{ detailFrom.province }}
</el-descriptions-item>
                    <el-descriptions-item label="城市">
    {{ detailFrom.city }}
</el-descriptions-item>
                    <el-descriptions-item label="网络类型">
    {{ detailFrom.network_type }}
</el-descriptions-item>
                    <el-descriptions-item label="运营商">
    {{ detailFrom.carrier }}
</el-descriptions-item>
                    <el-descriptions-item label="首次出现时间">
    {{ detailFrom.first_seen_time }}
</el-descriptions-item>
                    <el-descriptions-item label="最后出现时间">
    {{ detailFrom.last_seen_time }}
</el-descriptions-item>
                    <el-descriptions-item label="是否活跃">
    {{ detailFrom.is_active }}
</el-descriptions-item>
            </el-descriptions>
    </el-drawerdialog>

  </div>
</template>

<script setup>
import {
    getDeviceDataDataSource,
  createDeviceData,
  deleteDeviceData,
  deleteDeviceDataByIds,
  updateDeviceData,
  findDeviceData,
  getDeviceDataList
} from '@/api/meta/devicedata'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive } from 'vue'
import { useAppStore } from "@/pinia"

import { useIsMobile } from '@/utils/device'
const isMobile = useIsMobile()





defineOptions({
    name: 'DeviceData'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
        })
  const dataSource = ref([])
  const getDataSourceFunc = async()=>{
    const res = await getDeviceDataDataSource()
    if (res.code === 0) {
      dataSource.value = res.data
    }
  }
  getDataSourceFunc()



// 验证规则
const rule = reactive({
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
// 排序
const sortChange = ({ prop, order }) => {
  const sortMap = {
    CreatedAt:"CreatedAt",
    ID:"ID",
            first_seen_time: 'first_seen_time',
            last_seen_time: 'last_seen_time',
  }

  let sort = sortMap[prop]
  if(!sort){
   sort = prop.replace(/[A-Z]/g, match => `_${match.toLowerCase()}`)
  }

  searchInfo.value.sort = sort
  searchInfo.value.order = order
  getTableData()
}
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    if (searchInfo.value.is_active === ""){
        searchInfo.value.is_active=null
    }
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getDeviceDataList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteDeviceDataFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const IDs = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          IDs.push(item.ID)
        })
      const res = await deleteDeviceDataByIds({ IDs })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === IDs.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateDeviceDataFunc = async(row) => {
    const res = await findDeviceData({ ID: row.ID })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteDeviceDataFunc = async (row) => {
    const res = await deleteDeviceData({ ID: row.ID })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createDeviceData(formData.value)
                  break
                case 'update':
                  res = await updateDeviceData(formData.value)
                  break
                default:
                  res = await createDeviceData(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findDeviceData({ ID: row.ID })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}


</script>

<style>

</style>
