
<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" @keyup.enter="onSubmit">
      <el-form-item label="创建日期" prop="createdAtRange">
      <template #label>
        <span>
          创建日期
          <el-tooltip content="搜索范围是开始日期（包含）至结束日期（不包含）">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </span>
      </template>

      <el-date-picker
            v-model="searchInfo.createdAtRange"
            :class="isMobile ? 'w-full' : 'w-[380px]'"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
       </el-form-item>
      

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            <!-- 视图切换按钮 -->
            <div class="view-switch-buttons" style="margin-left: 10px;">
              <el-button-group>
                <el-button
                  :type="viewMode === 'table' ? 'primary' : ''"
                  @click="switchViewMode('table')"
                  :class="{ 'is-active': viewMode === 'table' }"
                >
                  <el-icon><Grid /></el-icon>
                  表格
                </el-button>
                <el-button
                  :type="viewMode === 'chart' ? 'primary' : ''"
                  @click="switchViewMode('chart')"
                  :class="{ 'is-active': viewMode === 'chart' }"
                >
                  <el-icon><TrendCharts /></el-icon>
                  图表
                </el-button>
              </el-button-group>
            </div>
        </div>
        <!-- 表格视图 -->
        <el-table
        v-if="viewMode === 'table'"
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
        @selection-change="handleSelectionChange"
        @sort-change="sortChange"
        >
        <el-table-column type="selection" width="55" />

        <el-table-column sortable align="left" label="日期" prop="CreatedAt"width="180">
            <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>

            <el-table-column sortable align="left" label="统计日期" prop="stat_date" width="180">
   <template #default="scope">{{ formatDate(scope.row.stat_date) }}</template>
</el-table-column>
            <el-table-column align="left" label="广告名称" prop="ad_id" width="120">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.ad_id,scope.row.ad_id) }}</span>
    </template>
</el-table-column>
            <el-table-column align="left" label="计划ID" prop="campaign_id" width="120">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.campaign_id,scope.row.campaign_id) }}</span>
    </template>
</el-table-column>
            <el-table-column align="left" label="APP ID" prop="app_id" width="120">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.app_id,scope.row.app_id) }}</span>
    </template>
</el-table-column>
            <el-table-column align="left" label="用户ID" prop="user_id" width="120">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.user_id,scope.row.user_id) }}</span>
    </template>
</el-table-column>
            <el-table-column align="left" label="广告位ID" prop="ad_position_id" width="120">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.ad_position_id,scope.row.ad_position_id) }}</span>
    </template>
</el-table-column>
            <el-table-column sortable align="left" label="展示次数" prop="impressions" width="120" />

            <el-table-column sortable align="left" label="点击次数" prop="clicks" width="120" />

            <el-table-column align="left" label="转化次数" prop="conversions" width="120" />

            <el-table-column align="left" label="独立用户数" prop="unique_users" width="120" />

            <el-table-column sortable align="left" label="收入金额" prop="revenue" width="120" />

            <el-table-column align="left" label="成本金额" prop="cost" width="120" />

            <el-table-column align="left" label="点击率" prop="ctr" width="120" />

            <el-table-column align="left" label="转化率" prop="cvr" width="120" />

            <el-table-column align="left" label="千次展示收入" prop="ecpm" width="120" />

            <el-table-column align="left" label="平均点击成本" prop="cpc" width="120" />

            <el-table-column align="left" label="平均转化成本" prop="cpa" width="120" />

        <el-table-column align="left" label="操作" :fixed="isMobile ? false : 'right'" :min-width="isMobile ? '210px' : appStore.operateMinWith">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateAdStatisticsFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>

        <!-- 图表视图 -->
        <StatisticsCharts
          v-if="viewMode === 'chart'"
          :data="tableData"
          :loading="chartLoading"
          :data-source="dataSource"
        />
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawerdialog destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
            </div>
          </template>

          <el-form :model="formData" label-position="right" ref="elFormRef" :rules="rule" label-width="100px">
          </el-form>
          <template #footer>
              <div>
                <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                <el-button @click="closeDialog">取 消</el-button>
              </div>
          </template>
    </el-drawerdialog>

    <el-drawerdialog destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="统计日期">
    {{ detailFrom.stat_date }}
</el-descriptions-item>
                    <el-descriptions-item label="广告名称">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.ad_id,detailFrom.ad_id) }}</span>
    </template>
</el-descriptions-item>
                    <el-descriptions-item label="计划ID">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.campaign_id,detailFrom.campaign_id) }}</span>
    </template>
</el-descriptions-item>
                    <el-descriptions-item label="APP ID">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.app_id,detailFrom.app_id) }}</span>
    </template>
</el-descriptions-item>
                    <el-descriptions-item label="用户ID">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.user_id,detailFrom.user_id) }}</span>
    </template>
</el-descriptions-item>
                    <el-descriptions-item label="广告位ID">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.ad_position_id,detailFrom.ad_position_id) }}</span>
    </template>
</el-descriptions-item>
                    <el-descriptions-item label="展示次数">
    {{ detailFrom.impressions }}
</el-descriptions-item>
                    <el-descriptions-item label="点击次数">
    {{ detailFrom.clicks }}
</el-descriptions-item>
                    <el-descriptions-item label="转化次数">
    {{ detailFrom.conversions }}
</el-descriptions-item>
                    <el-descriptions-item label="独立用户数">
    {{ detailFrom.unique_users }}
</el-descriptions-item>
                    <el-descriptions-item label="收入金额">
    {{ detailFrom.revenue }}
</el-descriptions-item>
                    <el-descriptions-item label="成本金额">
    {{ detailFrom.cost }}
</el-descriptions-item>
                    <el-descriptions-item label="点击率">
    {{ detailFrom.ctr }}
</el-descriptions-item>
                    <el-descriptions-item label="转化率">
    {{ detailFrom.cvr }}
</el-descriptions-item>
                    <el-descriptions-item label="千次展示收入">
    {{ detailFrom.ecpm }}
</el-descriptions-item>
                    <el-descriptions-item label="平均点击成本">
    {{ detailFrom.cpc }}
</el-descriptions-item>
                    <el-descriptions-item label="平均转化成本">
    {{ detailFrom.cpa }}
</el-descriptions-item>
            </el-descriptions>
    </el-drawerdialog>

  </div>
</template>

<script setup>
import {
    getAdStatisticsDataSource,
  createAdStatistics,
  deleteAdStatistics,
  deleteAdStatisticsByIds,
  updateAdStatistics,
  findAdStatistics,
  getAdStatisticsList
} from '@/api/meta/adstatistics'
// 图表组件
import StatisticsCharts from './components/StatisticsCharts.vue'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted } from 'vue'
import { useAppStore } from "@/pinia"
import { Grid, TrendCharts } from '@element-plus/icons-vue'

import { useIsMobile } from '@/utils/device'
const isMobile = useIsMobile()





defineOptions({
    name: 'AdStatistics'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 视图模式控制
const viewMode = ref('table') // 'table' 或 'chart'
const chartLoading = ref(false)

// 从本地存储加载视图模式偏好
const loadViewModePreference = () => {
  const savedViewMode = localStorage.getItem('adstatistics-view-mode')
  if (savedViewMode && ['table', 'chart'].includes(savedViewMode)) {
    viewMode.value = savedViewMode
  }
}

// 保存视图模式偏好到本地存储
const saveViewModePreference = (mode) => {
  localStorage.setItem('adstatistics-view-mode', mode)
}

// 切换视图模式
const switchViewMode = (mode) => {
  viewMode.value = mode
  saveViewModePreference(mode)

  // 如果切换到图表视图，可能需要重新加载数据
  if (mode === 'chart') {
    chartLoading.value = true
    // 模拟加载延迟
    setTimeout(() => {
      chartLoading.value = false
    }, 500)
  }
}

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
        })
  const dataSource = ref([])
  const getDataSourceFunc = async()=>{
    const res = await getAdStatisticsDataSource()
    if (res.code === 0) {
      dataSource.value = res.data
    }
  }
  getDataSourceFunc()



// 验证规则
const rule = reactive({
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
// 排序
const sortChange = ({ prop, order }) => {
  const sortMap = {
    CreatedAt:"CreatedAt",
    ID:"ID",
            stat_date: 'stat_date',
            impressions: 'impressions',
            clicks: 'clicks',
            revenue: 'revenue',
  }

  let sort = sortMap[prop]
  if(!sort){
   sort = prop.replace(/[A-Z]/g, match => `_${match.toLowerCase()}`)
  }

  searchInfo.value.sort = sort
  searchInfo.value.order = order
  getTableData()
}
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getAdStatisticsList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// 组件挂载时加载视图模式偏好
onMounted(() => {
  loadViewModePreference()
})

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteAdStatisticsFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const IDs = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          IDs.push(item.ID)
        })
      const res = await deleteAdStatisticsByIds({ IDs })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === IDs.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateAdStatisticsFunc = async(row) => {
    const res = await findAdStatistics({ ID: row.ID })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteAdStatisticsFunc = async (row) => {
    const res = await deleteAdStatistics({ ID: row.ID })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createAdStatistics(formData.value)
                  break
                case 'update':
                  res = await updateAdStatistics(formData.value)
                  break
                default:
                  res = await createAdStatistics(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findAdStatistics({ ID: row.ID })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}


</script>

<style scoped>
/* 视图切换按钮样式 */
.view-switch-buttons {
  display: inline-flex;
  align-items: center;
}

.view-switch-buttons .el-button-group .el-button.is-active {
  background-color: var(--el-color-primary);
  color: white;
}

/* 图表视图容器 */
.chart-view-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.dark .chart-view-container {
  background: #1d1e1f;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .view-switch-buttons .el-button-group {
    flex-direction: column;
    width: 100%;
  }

  .view-switch-buttons .el-button {
    width: 100%;
    margin-bottom: 4px;
  }
}
</style>
