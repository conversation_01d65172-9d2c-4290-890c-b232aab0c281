<template>
  <div class="statistics-charts">
    <!-- 图表维度切换按钮 -->
    <div class="chart-dimension-selector">
      <div class="dimension-header">
        <h3>数据分析</h3>
        <div class="dimension-buttons">
          <el-button-group>
            <el-button
              v-for="dimension in chartDimensions"
              :key="dimension.key"
              :type="activeDimension === dimension.key ? 'primary' : ''"
              @click="switchDimension(dimension.key)"
              :class="{ 'is-active': activeDimension === dimension.key }"
            >
              <el-icon><component :is="dimension.icon" /></el-icon>
              {{ dimension.label }}
            </el-button>
          </el-button-group>
        </div>
      </div>
    </div>

    <!-- 图表容器 -->
    <div class="charts-container">
      <!-- 日期趋势图表 -->
      <div v-if="activeDimension === 'date'" class="chart-section">
        <div class="chart-header">
          <h4>日期趋势分析</h4>
          <div class="chart-controls">
            <el-select v-model="dateMetric" @change="updateDateChart" size="small">
              <el-option label="展示次数" value="impressions" />
              <el-option label="点击次数" value="clicks" />
              <el-option label="收入金额" value="revenue" />
              <el-option label="成本金额" value="cost" />
            </el-select>
          </div>
        </div>
        <div class="chart-wrapper">
          <v-chart :option="dateChartOption" :style="{ height: '400px' }" />
        </div>
      </div>

      <!-- 展示分析图表 -->
      <div v-if="activeDimension === 'impressions'" class="chart-section">
        <div class="chart-header">
          <h4>展示数据分析</h4>
        </div>
        <div class="charts-grid">
          <div class="chart-item">
            <h5>展示次数分布</h5>
            <v-chart :option="impressionsDistributionOption" :style="{ height: '300px' }" />
          </div>
          <div class="chart-item">
            <h5>展示趋势</h5>
            <v-chart :option="impressionsTrendOption" :style="{ height: '300px' }" />
          </div>
        </div>
      </div>

      <!-- 点击分析图表 -->
      <div v-if="activeDimension === 'clicks'" class="chart-section">
        <div class="chart-header">
          <h4>点击数据分析</h4>
        </div>
        <div class="charts-grid">
          <div class="chart-item">
            <h5>点击率分析</h5>
            <v-chart :option="clickRateOption" :style="{ height: '300px' }" />
          </div>
          <div class="chart-item">
            <h5>点击次数分布</h5>
            <v-chart :option="clicksDistributionOption" :style="{ height: '300px' }" />
          </div>
        </div>
      </div>

      <!-- 收入分析图表 -->
      <div v-if="activeDimension === 'revenue'" class="chart-section">
        <div class="chart-header">
          <h4>收入数据分析</h4>
        </div>
        <div class="charts-grid">
          <div class="chart-item">
            <h5>收入趋势</h5>
            <v-chart :option="revenueTrendOption" :style="{ height: '300px' }" />
          </div>
          <div class="chart-item">
            <h5>收入成本对比</h5>
            <v-chart :option="revenueCostOption" :style="{ height: '300px' }" />
          </div>
        </div>
      </div>

      <!-- 转化分析图表 -->
      <div v-if="activeDimension === 'conversion'" class="chart-section">
        <div class="chart-header">
          <h4>转化数据分析</h4>
        </div>
        <div class="charts-grid">
          <div class="chart-item">
            <h5>转化率趋势</h5>
            <v-chart :option="conversionRateOption" :style="{ height: '300px' }" />
          </div>
          <div class="chart-item">
            <h5>转化漏斗</h5>
            <v-chart :option="conversionFunnelOption" :style="{ height: '300px' }" />
          </div>
        </div>
      </div>

      <!-- 综合分析图表 -->
      <div v-if="activeDimension === 'overview'" class="chart-section">
        <div class="chart-header">
          <h4>综合数据分析</h4>
        </div>
        <div class="overview-grid">
          <div class="overview-item">
            <h5>关键指标概览</h5>
            <v-chart :option="overviewMetricsOption" :style="{ height: '250px' }" />
          </div>
          <div class="overview-item">
            <h5>性能雷达图</h5>
            <v-chart :option="performanceRadarOption" :style="{ height: '250px' }" />
          </div>
          <div class="overview-item">
            <h5>广告效果排名</h5>
            <v-chart :option="adRankingOption" :style="{ height: '250px' }" />
          </div>
          <div class="overview-item">
            <h5>时段分析</h5>
            <v-chart :option="timeAnalysisOption" :style="{ height: '250px' }" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, BarChart, PieChart, RadarChart, FunnelChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { 
  Calendar, 
  View, 
  Mouse, 
  Money, 
  TrendCharts, 
  DataAnalysis 
} from '@element-plus/icons-vue'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  LineChart,
  BarChart,
  PieChart,
  RadarChart,
  FunnelChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  DataZoomComponent,
  ToolboxComponent
])

// Props
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  dataSource: {
    type: Object,
    default: () => ({})
  }
})

// 图表维度配置
const chartDimensions = [
  { key: 'date', label: '日期趋势', icon: 'Calendar' },
  { key: 'impressions', label: '展示分析', icon: 'View' },
  { key: 'clicks', label: '点击分析', icon: 'Mouse' },
  { key: 'revenue', label: '收入分析', icon: 'Money' },
  { key: 'conversion', label: '转化分析', icon: 'TrendCharts' },
  { key: 'overview', label: '综合分析', icon: 'DataAnalysis' }
]

// 当前激活的维度
const activeDimension = ref('date')
const dateMetric = ref('impressions')

// 切换维度
const switchDimension = (dimension) => {
  activeDimension.value = dimension
  // 保存用户选择到本地存储
  localStorage.setItem('statistics-chart-dimension', dimension)
}

// 从本地存储加载维度偏好
const loadDimensionPreference = () => {
  const saved = localStorage.getItem('statistics-chart-dimension')
  if (saved && chartDimensions.some(d => d.key === saved)) {
    activeDimension.value = saved
  }
}

// 处理数据
const processedData = computed(() => {
  if (!props.data || props.data.length === 0) return []

  return props.data.map(item => {
    // 处理日期格式
    const statDate = item.stat_date || item.CreatedAt || new Date().toISOString()
    const formattedDate = statDate.split('T')[0] // 取日期部分

    // 获取广告名称
    const adName = getAdName(item.ad_id)
    const campaignName = getCampaignName(item.campaign_id)
    const appName = getAppName(item.app_id)

    return {
      ...item,
      stat_date: formattedDate,
      ad_name: adName,
      campaign_name: campaignName,
      app_name: appName,
      impressions: Number(item.impressions) || 0,
      clicks: Number(item.clicks) || 0,
      revenue: Number(item.revenue) || 0,
      cost: Number(item.cost) || 0,
      conversions: Number(item.conversions) || 0,
      unique_users: Number(item.unique_users) || 0,
      ctr: Number(item.ctr) || 0,
      cvr: Number(item.cvr) || 0,
      ecpm: Number(item.ecpm) || 0,
      cpc: Number(item.cpc) || 0,
      cpa: Number(item.cpa) || 0
    }
  })
})

// 获取广告名称
const getAdName = (adId) => {
  if (!props.dataSource?.ad_id || !adId) return `广告${adId || 'Unknown'}`
  const ad = props.dataSource.ad_id.find(item => item.value === adId)
  return ad ? ad.label : `广告${adId}`
}

// 获取计划名称
const getCampaignName = (campaignId) => {
  if (!props.dataSource?.campaign_id || !campaignId) return `计划${campaignId || 'Unknown'}`
  const campaign = props.dataSource.campaign_id.find(item => item.value === campaignId)
  return campaign ? campaign.label : `计划${campaignId}`
}

// 获取应用名称
const getAppName = (appId) => {
  if (!props.dataSource?.app_id || !appId) return `应用${appId || 'Unknown'}`
  const app = props.dataSource.app_id.find(item => item.value === appId)
  return app ? app.label : `应用${appId}`
}

// 日期趋势图表配置
const dateChartOption = computed(() => {
  if (!processedData.value.length) {
    return {
      title: { text: '暂无数据', left: 'center' },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: []
    }
  }

  // 按日期聚合数据
  const dateAggregated = processedData.value.reduce((acc, item) => {
    const date = item.stat_date
    if (!acc[date]) {
      acc[date] = { impressions: 0, clicks: 0, revenue: 0, cost: 0 }
    }
    acc[date].impressions += item.impressions
    acc[date].clicks += item.clicks
    acc[date].revenue += item.revenue
    acc[date].cost += item.cost
    return acc
  }, {})

  // 排序日期并取最近30天
  const sortedDates = Object.keys(dateAggregated).sort().slice(-30)
  const values = sortedDates.map(date => dateAggregated[date][dateMetric.value] || 0)

  return {
    title: {
      text: `${getMetricLabel(dateMetric.value)}趋势`,
      left: 'center',
      textStyle: { fontSize: 16, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const param = params[0]
        const value = param.value
        const unit = dateMetric.value.includes('revenue') || dateMetric.value.includes('cost') ? '元' : ''
        return `${param.axisValue}<br/>${param.seriesName}: ${value.toLocaleString()}${unit}`
      }
    },
    xAxis: {
      type: 'category',
      data: sortedDates,
      axisLabel: {
        rotate: 45,
        formatter: (value) => value.split('-').slice(1).join('-') // 显示月-日
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + 'w'
          }
          return value.toLocaleString()
        }
      }
    },
    series: [{
      name: getMetricLabel(dateMetric.value),
      type: 'line',
      data: values,
      smooth: true,
      lineStyle: { color: '#409EFF', width: 3 },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ]
        }
      },
      markPoint: {
        data: [
          { type: 'max', name: '最大值' },
          { type: 'min', name: '最小值' }
        ]
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    dataZoom: [{
      type: 'slider',
      start: Math.max(0, 100 - (30 / sortedDates.length) * 100),
      end: 100
    }]
  }
})

// 获取指标标签
const getMetricLabel = (metric) => {
  const labels = {
    impressions: '展示次数',
    clicks: '点击次数',
    revenue: '收入金额',
    cost: '成本金额'
  }
  return labels[metric] || metric
}

// 更新日期图表
const updateDateChart = () => {
  // 图表会自动更新，因为使用了computed
}

// 展示次数分布图表
const impressionsDistributionOption = computed(() => {
  if (!processedData.value.length) {
    return {
      title: { text: '暂无数据', left: 'center' },
      series: []
    }
  }

  const adData = processedData.value.reduce((acc, item) => {
    const adName = item.ad_name || `广告${item.ad_id}`
    acc[adName] = (acc[adName] || 0) + item.impressions
    return acc
  }, {})

  // 取前10个广告，其余归为"其他"
  const sortedAds = Object.entries(adData)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)

  const otherSum = Object.entries(adData)
    .slice(10)
    .reduce((sum, [,value]) => sum + value, 0)

  if (otherSum > 0) {
    sortedAds.push(['其他', otherSum])
  }

  return {
    title: {
      text: '各广告展示次数分布',
      left: 'center',
      textStyle: { fontSize: 14, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      textStyle: { fontSize: 12 }
    },
    series: [{
      name: '展示次数',
      type: 'pie',
      radius: ['40%', '70%'],
      center: ['60%', '50%'],
      data: sortedAds.map(([name, value]) => ({ name, value })),
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        formatter: '{b}: {d}%'
      }
    }]
  }
})

// 展示趋势图表
const impressionsTrendOption = computed(() => {
  if (!processedData.value.length) {
    return {
      title: { text: '暂无数据', left: 'center' },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: []
    }
  }

  // 按日期聚合展示数据
  const dateAggregated = processedData.value.reduce((acc, item) => {
    const date = item.stat_date
    acc[date] = (acc[date] || 0) + item.impressions
    return acc
  }, {})

  const sortedDates = Object.keys(dateAggregated).sort().slice(-15)
  const impressions = sortedDates.map(date => dateAggregated[date])

  return {
    title: {
      text: '展示次数趋势',
      left: 'center',
      textStyle: { fontSize: 14, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const param = params[0]
        return `${param.axisValue}<br/>展示次数: ${param.value.toLocaleString()}`
      }
    },
    xAxis: {
      type: 'category',
      data: sortedDates.map(date => date.split('-').slice(1).join('-')),
      axisLabel: { rotate: 45 }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + 'w'
          }
          return value.toLocaleString()
        }
      }
    },
    series: [{
      name: '展示次数',
      type: 'bar',
      data: impressions,
      itemStyle: {
        color: '#67C23A',
        borderRadius: [4, 4, 0, 0]
      },
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    }
  }
})

// 点击率分析图表
const clickRateOption = computed(() => {
  if (!processedData.value.length) {
    return {
      title: { text: '暂无数据', left: 'center' },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: []
    }
  }

  // 按日期聚合点击率数据
  const dateAggregated = processedData.value.reduce((acc, item) => {
    const date = item.stat_date
    if (!acc[date]) {
      acc[date] = { totalImpressions: 0, totalClicks: 0 }
    }
    acc[date].totalImpressions += item.impressions
    acc[date].totalClicks += item.clicks
    return acc
  }, {})

  const sortedDates = Object.keys(dateAggregated).sort().slice(-15)
  const ctr = sortedDates.map(date => {
    const data = dateAggregated[date]
    return data.totalImpressions > 0 ?
      ((data.totalClicks / data.totalImpressions) * 100).toFixed(2) : 0
  })

  return {
    title: {
      text: '点击率趋势 (%)',
      left: 'center',
      textStyle: { fontSize: 14, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const param = params[0]
        return `${param.axisValue}<br/>点击率: ${param.value}%`
      }
    },
    xAxis: {
      type: 'category',
      data: sortedDates.map(date => date.split('-').slice(1).join('-')),
      axisLabel: { rotate: 45 }
    },
    yAxis: {
      type: 'value',
      axisLabel: { formatter: '{value}%' },
      min: 0,
      max: (value) => Math.ceil(value.max * 1.2)
    },
    series: [{
      name: '点击率',
      type: 'line',
      data: ctr,
      smooth: true,
      lineStyle: { color: '#E6A23C', width: 3 },
      areaStyle: { color: 'rgba(230, 162, 60, 0.2)' },
      markPoint: {
        data: [
          { type: 'max', name: '最高点击率' },
          { type: 'min', name: '最低点击率' }
        ]
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    }
  }
})

// 点击次数分布图表
const clicksDistributionOption = computed(() => {
  if (!processedData.value.length) {
    return {
      title: { text: '暂无数据', left: 'center' },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: []
    }
  }

  const adData = processedData.value.reduce((acc, item) => {
    const adName = item.ad_name || `广告${item.ad_id}`
    acc[adName] = (acc[adName] || 0) + item.clicks
    return acc
  }, {})

  // 取前10个广告
  const sortedAds = Object.entries(adData)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)

  return {
    title: {
      text: '各广告点击次数',
      left: 'center',
      textStyle: { fontSize: 14, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const param = params[0]
        return `${param.axisValue}<br/>点击次数: ${param.value.toLocaleString()}`
      }
    },
    xAxis: {
      type: 'category',
      data: sortedAds.map(([name]) => name),
      axisLabel: {
        rotate: 45,
        formatter: (value) => value.length > 8 ? value.substring(0, 8) + '...' : value
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + 'w'
          }
          return value.toLocaleString()
        }
      }
    },
    series: [{
      name: '点击次数',
      type: 'bar',
      data: sortedAds.map(([,value]) => value),
      itemStyle: {
        color: '#409EFF',
        borderRadius: [4, 4, 0, 0]
      },
      markLine: {
        data: [{ type: 'average', name: '平均值' }]
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '20%',
      containLabel: true
    }
  }
})

// 收入趋势图表
const revenueTrendOption = computed(() => {
  if (!processedData.value.length) {
    return {
      title: { text: '暂无数据', left: 'center' },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: []
    }
  }

  // 按日期聚合收入数据
  const dateAggregated = processedData.value.reduce((acc, item) => {
    const date = item.stat_date
    acc[date] = (acc[date] || 0) + item.revenue
    return acc
  }, {})

  const sortedDates = Object.keys(dateAggregated).sort().slice(-15)
  const revenue = sortedDates.map(date => dateAggregated[date])

  return {
    title: {
      text: '收入趋势',
      left: 'center',
      textStyle: { fontSize: 14, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const param = params[0]
        return `${param.axisValue}<br/>收入: ¥${param.value.toLocaleString()}`
      }
    },
    xAxis: {
      type: 'category',
      data: sortedDates.map(date => date.split('-').slice(1).join('-')),
      axisLabel: { rotate: 45 }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          if (value >= 10000) {
            return '¥' + (value / 10000).toFixed(1) + 'w'
          }
          return '¥' + value.toLocaleString()
        }
      }
    },
    series: [{
      name: '收入',
      type: 'line',
      data: revenue,
      smooth: true,
      lineStyle: { color: '#F56C6C', width: 3 },
      areaStyle: { color: 'rgba(245, 108, 108, 0.2)' },
      markPoint: {
        data: [
          { type: 'max', name: '最高收入' },
          { type: 'min', name: '最低收入' }
        ]
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    }
  }
})

// 收入成本对比图表
const revenueCostOption = computed(() => {
  if (!processedData.value.length) {
    return {
      title: { text: '暂无数据', left: 'center' },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: []
    }
  }

  // 按日期聚合收入和成本数据
  const dateAggregated = processedData.value.reduce((acc, item) => {
    const date = item.stat_date
    if (!acc[date]) {
      acc[date] = { revenue: 0, cost: 0 }
    }
    acc[date].revenue += item.revenue
    acc[date].cost += item.cost
    return acc
  }, {})

  const sortedDates = Object.keys(dateAggregated).sort().slice(-10)
  const revenue = sortedDates.map(date => dateAggregated[date].revenue)
  const cost = sortedDates.map(date => dateAggregated[date].cost)

  return {
    title: {
      text: '收入成本对比',
      left: 'center',
      textStyle: { fontSize: 14, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        let result = params[0].axisValue + '<br/>'
        params.forEach(param => {
          result += `${param.seriesName}: ¥${param.value.toLocaleString()}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['收入', '成本'],
      top: 30,
      textStyle: { fontSize: 12 }
    },
    xAxis: {
      type: 'category',
      data: sortedDates.map(date => date.split('-').slice(1).join('-')),
      axisLabel: { rotate: 45 }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          if (value >= 10000) {
            return '¥' + (value / 10000).toFixed(1) + 'w'
          }
          return '¥' + value.toLocaleString()
        }
      }
    },
    series: [
      {
        name: '收入',
        type: 'bar',
        data: revenue,
        itemStyle: {
          color: '#67C23A',
          borderRadius: [4, 4, 0, 0]
        }
      },
      {
        name: '成本',
        type: 'bar',
        data: cost,
        itemStyle: {
          color: '#F56C6C',
          borderRadius: [4, 4, 0, 0]
        }
      }
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    }
  }
})

// 转化率趋势图表
const conversionRateOption = computed(() => {
  if (!processedData.value.length) {
    return {
      title: { text: '暂无数据', left: 'center' },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: []
    }
  }

  // 按日期聚合转化率数据
  const dateAggregated = processedData.value.reduce((acc, item) => {
    const date = item.stat_date
    if (!acc[date]) {
      acc[date] = { totalClicks: 0, totalConversions: 0 }
    }
    acc[date].totalClicks += item.clicks
    acc[date].totalConversions += item.conversions
    return acc
  }, {})

  const sortedDates = Object.keys(dateAggregated).sort().slice(-15)
  const cvr = sortedDates.map(date => {
    const data = dateAggregated[date]
    return data.totalClicks > 0 ?
      ((data.totalConversions / data.totalClicks) * 100).toFixed(2) : 0
  })

  return {
    title: {
      text: '转化率趋势 (%)',
      left: 'center',
      textStyle: { fontSize: 14, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const param = params[0]
        return `${param.axisValue}<br/>转化率: ${param.value}%`
      }
    },
    xAxis: {
      type: 'category',
      data: sortedDates.map(date => date.split('-').slice(1).join('-')),
      axisLabel: { rotate: 45 }
    },
    yAxis: {
      type: 'value',
      axisLabel: { formatter: '{value}%' },
      min: 0
    },
    series: [{
      name: '转化率',
      type: 'line',
      data: cvr,
      smooth: true,
      lineStyle: { color: '#909399', width: 3 },
      areaStyle: { color: 'rgba(144, 147, 153, 0.2)' },
      markPoint: {
        data: [
          { type: 'max', name: '最高转化率' },
          { type: 'min', name: '最低转化率' }
        ]
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    }
  }
})

// 转化漏斗图表
const conversionFunnelOption = computed(() => {
  if (!processedData.value.length) {
    return {
      title: { text: '暂无数据', left: 'center' },
      series: []
    }
  }

  const totalImpressions = processedData.value.reduce((sum, item) => sum + item.impressions, 0)
  const totalClicks = processedData.value.reduce((sum, item) => sum + item.clicks, 0)
  const totalConversions = processedData.value.reduce((sum, item) => sum + item.conversions, 0)
  const totalUniqueUsers = processedData.value.reduce((sum, item) => sum + item.unique_users, 0)

  return {
    title: {
      text: '转化漏斗',
      left: 'center',
      textStyle: { fontSize: 14, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        const percentage = params.data.value > 0 ?
          ((params.data.value / totalImpressions) * 100).toFixed(2) : 0
        return `${params.data.name}<br/>数量: ${params.data.value.toLocaleString()}<br/>占比: ${percentage}%`
      }
    },
    series: [{
      type: 'funnel',
      left: '10%',
      width: '80%',
      minSize: '0%',
      maxSize: '100%',
      sort: 'descending',
      gap: 2,
      data: [
        { value: totalImpressions, name: '展示' },
        { value: totalUniqueUsers, name: '独立用户' },
        { value: totalClicks, name: '点击' },
        { value: totalConversions, name: '转化' }
      ].filter(item => item.value > 0),
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 1
      },
      label: {
        show: true,
        position: 'inside',
        formatter: '{b}: {c}'
      },
      emphasis: {
        label: {
          fontSize: 16
        }
      }
    }]
  }
})

// 综合指标概览
const overviewMetricsOption = computed(() => {
  if (!processedData.value.length) {
    return {
      title: { text: '暂无数据', left: 'center' },
      series: []
    }
  }

  const totalImpressions = processedData.value.reduce((sum, item) => sum + item.impressions, 0)
  const totalClicks = processedData.value.reduce((sum, item) => sum + item.clicks, 0)
  const totalRevenue = processedData.value.reduce((sum, item) => sum + item.revenue, 0)
  const totalCost = processedData.value.reduce((sum, item) => sum + item.cost, 0)
  const totalConversions = processedData.value.reduce((sum, item) => sum + item.conversions, 0)

  // 计算关键指标
  const avgCtr = totalImpressions > 0 ? (totalClicks / totalImpressions * 100).toFixed(2) : 0
  const avgCvr = totalClicks > 0 ? (totalConversions / totalClicks * 100).toFixed(2) : 0
  const profit = totalRevenue - totalCost
  const roi = totalCost > 0 ? ((profit / totalCost) * 100).toFixed(2) : 0

  return {
    title: {
      text: '关键指标概览',
      left: 'center',
      textStyle: { fontSize: 14, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'item',
      formatter: (params) => {
        let unit = ''
        if (params.name.includes('收入') || params.name.includes('成本') || params.name.includes('利润')) {
          unit = '元'
        } else if (params.name.includes('率')) {
          unit = '%'
        }
        return `${params.name}<br/>数值: ${params.value.toLocaleString()}${unit}`
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      textStyle: { fontSize: 11 }
    },
    series: [{
      type: 'pie',
      radius: ['30%', '60%'],
      center: ['60%', '50%'],
      data: [
        { value: totalImpressions, name: '展示次数', itemStyle: { color: '#5470c6' } },
        { value: totalClicks, name: '点击次数', itemStyle: { color: '#91cc75' } },
        { value: totalConversions, name: '转化次数', itemStyle: { color: '#fac858' } },
        { value: Math.abs(profit), name: profit >= 0 ? '利润' : '亏损', itemStyle: { color: profit >= 0 ? '#ee6666' : '#73c0de' } }
      ].filter(item => item.value > 0),
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
      label: {
        formatter: '{b}: {d}%'
      }
    }]
  }
})

// 性能雷达图
const performanceRadarOption = computed(() => {
  if (!processedData.value.length) {
    return {
      title: { text: '暂无数据', left: 'center' },
      radar: { indicator: [] },
      series: []
    }
  }

  const totalImpressions = processedData.value.reduce((sum, item) => sum + item.impressions, 0)
  const totalClicks = processedData.value.reduce((sum, item) => sum + item.clicks, 0)
  const totalConversions = processedData.value.reduce((sum, item) => sum + item.conversions, 0)
  const totalRevenue = processedData.value.reduce((sum, item) => sum + item.revenue, 0)
  const totalCost = processedData.value.reduce((sum, item) => sum + item.cost, 0)

  const avgCtr = totalImpressions > 0 ? (totalClicks / totalImpressions * 100) : 0
  const avgCvr = totalClicks > 0 ? (totalConversions / totalClicks * 100) : 0
  const avgEcpm = totalImpressions > 0 ? (totalRevenue / totalImpressions * 1000) : 0
  const avgCpc = totalClicks > 0 ? (totalCost / totalClicks) : 0
  const roi = totalCost > 0 ? ((totalRevenue - totalCost) / totalCost * 100) : 0

  return {
    title: {
      text: '性能雷达图',
      left: 'center',
      textStyle: { fontSize: 14, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'item'
    },
    radar: {
      indicator: [
        { name: '点击率(%)', max: Math.max(10, avgCtr * 1.5) },
        { name: '转化率(%)', max: Math.max(5, avgCvr * 1.5) },
        { name: 'ECPM', max: Math.max(100, avgEcpm * 1.5) },
        { name: 'CPC', max: Math.max(10, avgCpc * 1.5) },
        { name: 'ROI(%)', max: Math.max(100, Math.abs(roi) * 1.5) }
      ],
      radius: '60%'
    },
    series: [{
      type: 'radar',
      data: [{
        value: [avgCtr, avgCvr, avgEcpm, avgCpc, roi],
        name: '广告性能',
        itemStyle: { color: '#409EFF' },
        areaStyle: { color: 'rgba(64, 158, 255, 0.2)' }
      }],
      emphasis: {
        lineStyle: { width: 4 }
      }
    }]
  }
})

// 广告效果排名
const adRankingOption = computed(() => {
  if (!processedData.value.length) {
    return {
      title: { text: '暂无数据', left: 'center' },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: []
    }
  }

  const adPerformance = processedData.value.reduce((acc, item) => {
    const adName = item.ad_name || `广告${item.ad_id}`
    if (!acc[adName]) {
      acc[adName] = { revenue: 0, cost: 0, impressions: 0, clicks: 0 }
    }
    acc[adName].revenue += item.revenue
    acc[adName].cost += item.cost
    acc[adName].impressions += item.impressions
    acc[adName].clicks += item.clicks
    return acc
  }, {})

  const ranking = Object.entries(adPerformance)
    .map(([name, data]) => ({
      name,
      profit: data.revenue - data.cost,
      roi: data.cost > 0 ? ((data.revenue - data.cost) / data.cost * 100) : 0
    }))
    .sort((a, b) => b.profit - a.profit)
    .slice(0, 8)

  return {
    title: {
      text: '广告效果排名 (按利润)',
      left: 'center',
      textStyle: { fontSize: 14, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const param = params[0]
        const index = param.dataIndex
        const data = ranking[index]
        return `${param.axisValue}<br/>利润: ¥${param.value.toLocaleString()}<br/>ROI: ${data.roi.toFixed(2)}%`
      }
    },
    xAxis: {
      type: 'category',
      data: ranking.map(item => item.name.length > 6 ? item.name.substring(0, 6) + '...' : item.name),
      axisLabel: { rotate: 45 }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          if (value >= 10000) {
            return '¥' + (value / 10000).toFixed(1) + 'w'
          }
          return '¥' + value.toLocaleString()
        }
      }
    },
    series: [{
      name: '利润',
      type: 'bar',
      data: ranking.map(item => item.profit),
      itemStyle: {
        color: (params) => {
          return params.value >= 0 ? '#67C23A' : '#F56C6C'
        },
        borderRadius: [4, 4, 0, 0]
      },
      markLine: {
        data: [{ yAxis: 0, lineStyle: { color: '#909399' } }]
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '20%',
      containLabel: true
    }
  }
})

// 时段分析
const timeAnalysisOption = computed(() => {
  if (!processedData.value.length) {
    return {
      title: { text: '暂无数据', left: 'center' },
      xAxis: { type: 'category', data: [] },
      yAxis: { type: 'value' },
      series: []
    }
  }

  // 按小时统计展示数据
  const hours = Array.from({ length: 24 }, (_, i) => i)
  const hourlyData = hours.map(hour => {
    // 由于我们只有日期数据，这里模拟时段分析
    // 实际应用中需要包含具体时间的数据
    const dayCount = new Set(processedData.value.map(item => item.stat_date)).size
    const totalImpressions = processedData.value.reduce((sum, item) => sum + item.impressions, 0)

    // 模拟不同时段的分布（实际应用中应该使用真实的小时数据）
    const hourlyDistribution = [
      0.02, 0.01, 0.01, 0.01, 0.02, 0.03, 0.05, 0.07, // 0-7点
      0.08, 0.09, 0.08, 0.07, 0.06, 0.05, 0.06, 0.07, // 8-15点
      0.08, 0.09, 0.08, 0.07, 0.06, 0.05, 0.04, 0.03  // 16-23点
    ]

    return Math.round(totalImpressions * hourlyDistribution[hour] / dayCount)
  })

  return {
    title: {
      text: '24小时展示分布',
      left: 'center',
      textStyle: { fontSize: 14, fontWeight: 'bold' }
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params) => {
        const param = params[0]
        return `${param.axisValue}<br/>平均展示: ${param.value.toLocaleString()}`
      }
    },
    xAxis: {
      type: 'category',
      data: hours.map(h => `${h.toString().padStart(2, '0')}:00`),
      axisLabel: {
        interval: 2 // 每隔2小时显示一个标签
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value) => {
          if (value >= 10000) {
            return (value / 10000).toFixed(1) + 'w'
          }
          return value.toLocaleString()
        }
      }
    },
    series: [{
      name: '展示次数',
      type: 'line',
      data: hourlyData,
      smooth: true,
      lineStyle: { color: '#67C23A', width: 3 },
      areaStyle: { color: 'rgba(103, 194, 58, 0.2)' },
      markPoint: {
        data: [
          { type: 'max', name: '峰值时段' },
          { type: 'min', name: '低谷时段' }
        ]
      }
    }],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    }
  }
})

// 组件挂载时加载偏好
onMounted(() => {
  loadDimensionPreference()
})
</script>

<style scoped>
.statistics-charts {
  padding: 20px;
}

.chart-dimension-selector {
  margin-bottom: 24px;
}

.dimension-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.dimension-header h3 {
  margin: 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.dark .dimension-header h3 {
  color: #e5eaf3;
}

.dimension-buttons .el-button-group .el-button.is-active {
  background-color: var(--el-color-primary);
  color: white;
}

.charts-container {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark .charts-container {
  background: #1d1e1f;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.chart-section {
  width: 100%;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f2f5;
}

.dark .chart-header {
  border-bottom-color: #414243;
}

.chart-header h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.dark .chart-header h4 {
  color: #e5eaf3;
}

.chart-wrapper {
  width: 100%;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
}

.dark .chart-item {
  background: #2a2b2c;
}

.chart-item h5 {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.dark .chart-item h5 {
  color: #a3a6ad;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.overview-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
}

.dark .overview-item {
  background: #2a2b2c;
}

.overview-item h5 {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 13px;
  font-weight: 500;
}

.dark .overview-item h5 {
  color: #a3a6ad;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .dimension-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .overview-grid {
    grid-template-columns: 1fr;
  }
  
  .dimension-buttons .el-button-group {
    flex-wrap: wrap;
  }
  
  .dimension-buttons .el-button {
    margin-bottom: 8px;
  }
}
</style>
