
<template>
  <div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新增</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            
        </div>
        <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
        @sort-change="sortChange"
        >
        <el-table-column type="selection" width="55" />
        
            <el-table-column align="left" label="ID" prop="id" width="120" />

            <el-table-column align="left" label="广告位置" prop="ad_position_id" width="120">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.ad_position_id,scope.row.ad_position_id) }}</span>
    </template>
</el-table-column>
            <el-table-column align="left" label="广告类型" prop="ad_type_id" width="120">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.ad_type_id,scope.row.ad_type_id) }}</span>
    </template>
</el-table-column>
            <el-table-column align="left" label="广告行为" prop="ad_action_id" width="120">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.ad_action_id,scope.row.ad_action_id) }}</span>
    </template>
</el-table-column>
            <el-table-column align="left" label="APP应用" prop="app_id" width="120">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.app_id,scope.row.app_id) }}</span>
    </template>
</el-table-column>
            <el-table-column align="left" label="计费方式" prop="bid_type" width="120" />

            <el-table-column sortable align="left" label="基础价格" prop="base_price" width="120" />

            <el-table-column align="left" label="最低价格" prop="min_price" width="120" />

            <el-table-column align="left" label="最高价格" prop="max_price" width="120" />

            <el-table-column sortable align="left" label="生效时间" prop="effective_time" width="180">
   <template #default="scope">{{ formatDate(scope.row.effective_time) }}</template>
</el-table-column>
            <el-table-column align="left" label="过期时间" prop="expire_time" width="180">
   <template #default="scope">{{ formatDate(scope.row.expire_time) }}</template>
</el-table-column>
            <el-table-column align="left" label="状态" prop="status" width="120">
    <template #default="scope">{{ formatBoolean(scope.row.status) }}</template>
</el-table-column>
        <el-table-column align="left" label="操作" :fixed="isMobile ? false : 'right'" :min-width="isMobile ? '210px' : appStore.operateMinWith">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateAdPriceFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawerdialog destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
            </div>
          </template>

          <el-form :model="formData" label-position="right" ref="elFormRef" :rules="rule" label-width="100px">
            <el-form-item label="广告位置:" prop="ad_position_id">
    <el-select v-model="formData.ad_position_id" placeholder="请选择广告位置" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.ad_position_id" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
            <el-form-item label="广告类型:" prop="ad_type_id">
    <el-select v-model="formData.ad_type_id" placeholder="请选择广告类型" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.ad_type_id" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
            <el-form-item label="广告行为:" prop="ad_action_id">
    <el-select v-model="formData.ad_action_id" placeholder="请选择广告行为" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.ad_action_id" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
            <el-form-item label="APP应用:" prop="app_id">
    <el-select v-model="formData.app_id" placeholder="请选择APP应用" filterable style="width:100%" :clearable="true">
        <el-option v-for="(item,key) in dataSource.app_id" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
            <el-form-item label="计费方式:" prop="bid_type">
    <el-input v-model="formData.bid_type" :clearable="false" placeholder="请输入计费方式" />
</el-form-item>
            <el-form-item label="基础价格:" prop="base_price">
    <el-input-number v-model="formData.base_price" style="width:100%" :precision="2" :clearable="false" />
</el-form-item>
            <el-form-item label="最低价格:" prop="min_price">
    <el-input-number v-model="formData.min_price" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
            <el-form-item label="最高价格:" prop="max_price">
    <el-input-number v-model="formData.max_price" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
            <el-form-item label="生效时间:" prop="effective_time">
    <el-date-picker v-model="formData.effective_time" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
            <el-form-item label="过期时间:" prop="expire_time">
    <el-date-picker v-model="formData.expire_time" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
            <el-form-item label="状态:" prop="status">
    <el-switch v-model="formData.status" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
</el-form-item>
            <el-form-item label="备注:" prop="remark">
    <el-input v-model="formData.remark" :clearable="true" placeholder="请输入备注" />
</el-form-item>
          </el-form>
          <template #footer>
              <div>
                <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                <el-button @click="closeDialog">取 消</el-button>
              </div>
          </template>
    </el-drawerdialog>

    <el-drawerdialog destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="ID">
    {{ detailFrom.id }}
</el-descriptions-item>
                    <el-descriptions-item label="广告位置">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.ad_position_id,detailFrom.ad_position_id) }}</span>
    </template>
</el-descriptions-item>
                    <el-descriptions-item label="广告类型">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.ad_type_id,detailFrom.ad_type_id) }}</span>
    </template>
</el-descriptions-item>
                    <el-descriptions-item label="广告行为">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.ad_action_id,detailFrom.ad_action_id) }}</span>
    </template>
</el-descriptions-item>
                    <el-descriptions-item label="APP应用">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.app_id,detailFrom.app_id) }}</span>
    </template>
</el-descriptions-item>
                    <el-descriptions-item label="计费方式">
    {{ detailFrom.bid_type }}
</el-descriptions-item>
                    <el-descriptions-item label="基础价格">
    {{ detailFrom.base_price }}
</el-descriptions-item>
                    <el-descriptions-item label="最低价格">
    {{ detailFrom.min_price }}
</el-descriptions-item>
                    <el-descriptions-item label="最高价格">
    {{ detailFrom.max_price }}
</el-descriptions-item>
                    <el-descriptions-item label="生效时间">
    {{ detailFrom.effective_time }}
</el-descriptions-item>
                    <el-descriptions-item label="过期时间">
    {{ detailFrom.expire_time }}
</el-descriptions-item>
                    <el-descriptions-item label="状态">
    {{ detailFrom.status }}
</el-descriptions-item>
                    <el-descriptions-item label="备注">
    {{ detailFrom.remark }}
</el-descriptions-item>
            </el-descriptions>
    </el-drawerdialog>

  </div>
</template>

<script setup>
import {
    getAdPriceDataSource,
  createAdPrice,
  deleteAdPrice,
  deleteAdPriceByIds,
  updateAdPrice,
  findAdPrice,
  getAdPriceList
} from '@/api/meta/adprice'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive } from 'vue'
import { useAppStore } from "@/pinia"

import { useIsMobile } from '@/utils/device'
const isMobile = useIsMobile()





defineOptions({
    name: 'AdPrice'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            ad_position_id: undefined,
            ad_type_id: undefined,
            ad_action_id: undefined,
            app_id: undefined,
            bid_type: '',
            base_price: 0,
            min_price: 0,
            max_price: 0,
            effective_time: new Date(),
            expire_time: new Date(),
            status: false,
            remark: '',
        })
  const dataSource = ref([])
  const getDataSourceFunc = async()=>{
    const res = await getAdPriceDataSource()
    if (res.code === 0) {
      dataSource.value = res.data
    }
  }
  getDataSourceFunc()



// 验证规则
const rule = reactive({
               ad_position_id : [{
                   required: true,
                   message: '请选择广告位置',
                   trigger: ['input','blur'],
               },
              ],
               ad_type_id : [{
                   required: true,
                   message: '请选择广告类型',
                   trigger: ['input','blur'],
               },
              ],
               ad_action_id : [{
                   required: true,
                   message: '请选择广告行为',
                   trigger: ['input','blur'],
               },
              ],
               bid_type : [{
                   required: true,
                   message: '请选择计费方式',
                   trigger: ['input','blur'],
               },
               {
                   whitespace: true,
                   message: '不能只输入空格',
                   trigger: ['input', 'blur'],
              }
              ],
               base_price : [{
                   required: true,
                   message: '请输入基础价格',
                   trigger: ['input','blur'],
               },
              ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
// 排序
const sortChange = ({ prop, order }) => {
  const sortMap = {
    CreatedAt:"CreatedAt",
    ID:"ID",
            base_price: 'base_price',
            effective_time: 'effective_time',
  }

  let sort = sortMap[prop]
  if(!sort){
   sort = prop.replace(/[A-Z]/g, match => `_${match.toLowerCase()}`)
  }

  searchInfo.value.sort = sort
  searchInfo.value.order = order
  getTableData()
}
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    if (searchInfo.value.status === ""){
        searchInfo.value.status=null
    }
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getAdPriceList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteAdPriceFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const ids = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          ids.push(item.id)
        })
      const res = await deleteAdPriceByIds({ ids })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === ids.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateAdPriceFunc = async(row) => {
    const res = await findAdPrice({ id: row.id })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteAdPriceFunc = async (row) => {
    const res = await deleteAdPrice({ id: row.id })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        ad_position_id: undefined,
        ad_type_id: undefined,
        ad_action_id: undefined,
        app_id: undefined,
        bid_type: '',
        base_price: 0,
        min_price: 0,
        max_price: 0,
        effective_time: new Date(),
        expire_time: new Date(),
        status: false,
        remark: '',
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createAdPrice(formData.value)
                  break
                case 'update':
                  res = await updateAdPrice(formData.value)
                  break
                default:
                  res = await createAdPrice(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findAdPrice({ id: row.id })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}


</script>

<style>

</style>
