
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="广告位置:" prop="ad_position_id">
    <el-select v-model="formData.ad_position_id" placeholder="请选择广告位置" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.ad_position_id" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
        <el-form-item label="广告类型:" prop="ad_type_id">
    <el-select v-model="formData.ad_type_id" placeholder="请选择广告类型" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.ad_type_id" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
        <el-form-item label="广告行为:" prop="ad_action_id">
    <el-select v-model="formData.ad_action_id" placeholder="请选择广告行为" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.ad_action_id" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
        <el-form-item label="APP应用:" prop="app_id">
    <el-select v-model="formData.app_id" placeholder="请选择APP应用" filterable style="width:100%" :clearable="true">
        <el-option v-for="(item,key) in dataSource.app_id" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
        <el-form-item label="计费方式:" prop="bid_type">
    <el-input v-model="formData.bid_type" :clearable="false" placeholder="请输入计费方式" />
</el-form-item>
        <el-form-item label="基础价格:" prop="base_price">
    <el-input-number v-model="formData.base_price" style="width:100%" :precision="2" :clearable="false" />
</el-form-item>
        <el-form-item label="最低价格:" prop="min_price">
    <el-input-number v-model="formData.min_price" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
        <el-form-item label="最高价格:" prop="max_price">
    <el-input-number v-model="formData.max_price" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
        <el-form-item label="生效时间:" prop="effective_time">
    <el-date-picker v-model="formData.effective_time" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item label="过期时间:" prop="expire_time">
    <el-date-picker v-model="formData.expire_time" type="date" style="width:100%" placeholder="选择日期" :clearable="true" />
</el-form-item>
        <el-form-item label="状态:" prop="status">
    <el-switch v-model="formData.status" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
</el-form-item>
        <el-form-item label="备注:" prop="remark">
    <el-input v-model="formData.remark" :clearable="true" placeholder="请输入备注" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
    getAdPriceDataSource,
  createAdPrice,
  updateAdPrice,
  findAdPrice
} from '@/api/meta/adprice'

defineOptions({
    name: 'AdPriceForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            ad_position_id: undefined,
            ad_type_id: undefined,
            ad_action_id: undefined,
            app_id: undefined,
            bid_type: '',
            base_price: 0,
            min_price: 0,
            max_price: 0,
            effective_time: new Date(),
            expire_time: new Date(),
            status: false,
            remark: '',
        })
// 验证规则
const rule = reactive({
               id : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
               ad_position_id : [{
                   required: true,
                   message: '请选择广告位置',
                   trigger: ['input','blur'],
               }],
               ad_type_id : [{
                   required: true,
                   message: '请选择广告类型',
                   trigger: ['input','blur'],
               }],
               ad_action_id : [{
                   required: true,
                   message: '请选择广告行为',
                   trigger: ['input','blur'],
               }],
               bid_type : [{
                   required: true,
                   message: '请选择计费方式',
                   trigger: ['input','blur'],
               }],
               base_price : [{
                   required: true,
                   message: '请输入基础价格',
                   trigger: ['input','blur'],
               }],
})

const elFormRef = ref()
  const dataSource = ref([])
  const getDataSourceFunc = async()=>{
    const res = await getAdPriceDataSource()
    if (res.code === 0) {
      dataSource.value = res.data
    }
  }
  getDataSourceFunc()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findAdPrice({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createAdPrice(formData.value)
               break
             case 'update':
               res = await updateAdPrice(formData.value)
               break
             default:
               res = await createAdPrice(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
