
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="充值金额:" prop="amount">
    <el-input-number v-model="formData.amount" style="width:100%" :precision="2" :clearable="false" />
</el-form-item>
        <el-form-item label="货币类型:" prop="currency">
    <el-input v-model="formData.currency" :clearable="false" placeholder="请输入货币类型" />
</el-form-item>
        <el-form-item label="支付方式:" prop="payment_method">
    <el-input v-model="formData.payment_method" :clearable="false" placeholder="请输入支付方式" />
</el-form-item>
        <el-form-item label="状态:" prop="status">
    <el-input v-model="formData.status" :clearable="false" placeholder="请输入状态" />
</el-form-item>
        <el-form-item label="备注:" prop="remark">
    <el-input v-model="formData.remark" :clearable="true" placeholder="请输入备注" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
    getRechargeRecordDataSource,
  createRechargeRecord,
  updateRechargeRecord,
  findRechargeRecord
} from '@/api/meta/rechargerecord'

defineOptions({
    name: 'RechargeRecordForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            amount: 0,
            currency: '',
            payment_method: '',
            status: '',
            remark: '',
        })
// 验证规则
const rule = reactive({
               order_no : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
               amount : [{
                   required: true,
                   message: '请输入充值金额',
                   trigger: ['input','blur'],
               }],
               payment_method : [{
                   required: true,
                   message: '请选择支付方式',
                   trigger: ['input','blur'],
               }],
               request_time : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
})

const elFormRef = ref()
  const dataSource = ref([])
  const getDataSourceFunc = async()=>{
    const res = await getRechargeRecordDataSource()
    if (res.code === 0) {
      dataSource.value = res.data
    }
  }
  getDataSourceFunc()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findRechargeRecord({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createRechargeRecord(formData.value)
               break
             case 'update':
               res = await updateRechargeRecord(formData.value)
               break
             default:
               res = await createRechargeRecord(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
