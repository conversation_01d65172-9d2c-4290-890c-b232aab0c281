<template>
  <div class="ad-card-container">
    <div class="ad-card-grid">
      <div
        v-for="item in tableData"
        :key="item.ID"
        class="ad-card"
        :class="{ 'selected': isCardSelected(item) }"
        @click="toggleCardSelection(item)"
      >
        <!-- 选择框 -->
        <div class="card-selection">
          <el-checkbox
            :model-value="isCardSelected(item)"
            @change="toggleCardSelection(item)"
            @click.stop
          />
        </div>

        <!-- 媒体文件 -->
        <div class="card-media">
          <el-image
            v-if="item.media_url"
            :src="getUrl(item.media_url)"
            fit="cover"
            class="media-image"
            preview-teleported
          />
          <div v-else class="media-placeholder">
            <el-icon><Picture /></el-icon>
            <span>暂无图片</span>
          </div>
        </div>

        <!-- 卡片内容 -->
        <div class="card-content">
          <div class="card-header">
            <h4 class="card-title">{{ item.name }}</h4>
            <!-- <el-tag
              :type="getStatusTagType(item.status)"
              size="small"
            >
              {{ filterDataSource(dataSource.status, item.status) }}
            </el-tag> -->
          </div>

          <div class="card-info">
            <div class="info-item">
              <span class="info-label">类型：</span>
              <span class="info-value">{{ filterDataSource(dataSource.ad_type_id, item.ad_type_id) }}</span>
            </div>
            <!-- <div class="info-item">
              <span class="info-label">行为：</span>
              <span class="info-value">{{ filterDataSource(dataSource.ad_action_id, item.ad_action_id) }}</span>
            </div> -->
            <div class="info-item">
              <span class="info-label">标题：</span>
              <span class="info-value">{{ item.title || '暂无标题' }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">创建时间：</span>
              <span class="info-value">{{ formatDate(item.CreatedAt) }}</span>
            </div>
          </div>

          <!-- 广告位置标签 -->
          <!-- <div class="card-positions" v-if="item.ad_positions && item.ad_positions.length">
            <el-tag
              v-for="(position, key) in filterDataSource(dataSource.ad_positions, item.ad_positions)"
              :key="key"
              size="small"
              class="position-tag"
            >
              {{ position }}
            </el-tag>
          </div> -->

          <!-- 操作按钮 -->
          <div class="card-actions">
            <el-button size="small" type="primary" link @click.stop="getDetails(item)">
              <el-icon><InfoFilled /></el-icon>
              查看
            </el-button>
            <el-button size="small" type="primary" link @click.stop="updateAdFunc(item)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button size="small" type="danger" link @click.stop="deleteRow(item)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { getUrl } from '@/utils/image'
import { formatDate, filterDataSource } from '@/utils/format'
import { Picture, Edit, Delete, InfoFilled } from '@element-plus/icons-vue'

defineOptions({
  name: 'AdCardView'
})

const props = defineProps({
  tableData: {
    type: Array,
    required: true
  },
  dataSource: {
    type: Object,
    required: true
  },
  multipleSelection: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['update:multipleSelection', 'getDetails', 'updateAdFunc', 'deleteRow'])

// 卡片选择相关
const isCardSelected = (item) => {
  return props.multipleSelection.some(selected => selected.ID === item.ID)
}

const toggleCardSelection = (item) => {
  const newSelection = [...props.multipleSelection]
  const index = newSelection.findIndex(selected => selected.ID === item.ID)
  
  if (index > -1) {
    newSelection.splice(index, 1)
  } else {
    newSelection.push(item)
  }
  
  emit('update:multipleSelection', newSelection)
}

// 获取状态标签类型
const getStatusTagType = (status) => {
  // 根据状态返回不同的标签类型
  const statusMap = {
    '1': 'success',  // 已上线
    '2': 'warning',  // 待审核
    '3': 'danger',   // 已下线
    '0': 'info'      // 草稿
  }
  return statusMap[status] || 'info'
}

// 转发事件到父组件
const getDetails = (item) => {
  emit('getDetails', item)
}

const updateAdFunc = (item) => {
  emit('updateAdFunc', item)
}

const deleteRow = (item) => {
  emit('deleteRow', item)
}
</script>

<style scoped>
/* 卡片容器样式 */
.ad-card-container {
  width: 100%;
}

.ad-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  padding: 16px 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .ad-card-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 12px;
  }
}

/* 卡片样式 */
.ad-card {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.ad-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--el-color-primary);
}

.ad-card.selected {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 暗色主题适配 */
.dark .ad-card {
  background: #1d1e1f;
  border-color: #414243;
}

.dark .ad-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 选择框样式 */
.card-selection {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.dark .card-selection {
  background: rgba(29, 30, 31, 0.9);
}

.card-selection .el-checkbox {
  margin: 0;
}

.card-selection .el-checkbox__input {
  line-height: 1;
}

.card-selection .el-checkbox__inner {
  width: 16px;
  height: 16px;
}

/* 媒体文件样式 */
.card-media {
  height: 180px;
  overflow: hidden;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.media-image {
  width: 100%;
  height: 100%;
}

.media-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
}

.media-placeholder .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

/* 卡片内容样式 */
.card-content {
  padding: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #303133;
  flex: 1;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dark .card-title {
  color: #e5eaf3;
}

/* 信息项样式 */
.card-info {
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  margin-bottom: 6px;
  font-size: 13px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  color: #909399;
  min-width: 60px;
  flex-shrink: 0;
}

.info-value {
  color: #606266;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dark .info-value {
  color: #a3a6ad;
}

/* 位置标签样式 */
.card-positions {
  margin-bottom: 12px;
}

.position-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

/* 操作按钮样式 */
.card-actions {
  display: flex;
  justify-content: flex-start;
  gap: 8px;
  padding-top: 8px;
  border-top: 1px solid #f0f2f5;
}

.dark .card-actions {
  border-top-color: #414243;
}

.card-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}
</style>