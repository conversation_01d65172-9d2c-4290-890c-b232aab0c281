
<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" @keyup.enter="onSubmit">
      <el-form-item label="创建日期" prop="createdAtRange">
      <template #label>
        <span>
          创建日期
          <el-tooltip content="搜索范围是开始日期（包含）至结束日期（不包含）">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </span>
      </template>

      <el-date-picker
            v-model="searchInfo.createdAtRange"
            :class="isMobile ? 'w-full' : 'w-[380px]'"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
       </el-form-item>
      
            <el-form-item label="状态" prop="status">
  <el-select v-model="searchInfo.status" filterable placeholder="请选择状态" :clearable="false">
    <el-option v-for="(item,key) in dataSource.status" :key="key" :label="item.label" :value="item.value" />
  </el-select>
</el-form-item>
            

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新广告</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            <!-- 视图切换按钮 -->
            <div class="view-switch-buttons" style="margin-left: 10px;">
              <el-button-group>
                <el-button
                  :type="viewMode === 'table' ? 'primary' : ''"
                  @click="switchViewMode('table')"
                  :class="{ 'is-active': viewMode === 'table' }"
                >
                  <el-icon><Grid /></el-icon>
                  表格
                </el-button>
                <el-button
                  :type="viewMode === 'card' ? 'primary' : ''"
                  @click="switchViewMode('card')"
                  :class="{ 'is-active': viewMode === 'card' }"
                >
                  <el-icon><Postcard /></el-icon>
                  卡片
                </el-button>
              </el-button-group>
            </div>
        </div>
        <!-- 表格视图 -->
        <el-table
        v-if="viewMode === 'table'"
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
        @selection-change="handleSelectionChange"
        @sort-change="sortChange"
        >
        <el-table-column type="selection" width="55" />

        <el-table-column sortable align="left" label="日期" prop="CreatedAt"width="180">
            <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>

            <el-table-column sortable align="left" label="名称" prop="name" width="120" />

            <el-table-column align="left" label="类型" prop="ad_type_id" width="120">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.ad_type_id,scope.row.ad_type_id) }}</span>
    </template>
</el-table-column>
<el-table-column align="left" label="广告标题" prop="title" width="120" />
            <!-- <el-table-column align="left" label="广告行为" prop="ad_action_id" width="120">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.ad_action_id,scope.row.ad_action_id) }}</span>
    </template>
</el-table-column> -->
      <el-table-column label="媒体文件" prop="media_url" width="200">
          <template #default="scope">
            <el-image preview-teleported style="width: 178px; height: 100px" :src="getUrl(scope.row.media_url)" fit="cover"/>
          </template>
      </el-table-column>
            <el-table-column align="left" label="状态" prop="status" width="120">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.status,scope.row.status) }}</span>
    </template>
</el-table-column>
            <!-- <el-table-column align="left" label="广告位置" prop="ad_positions" width="120">
    <template #default="scope">
        <el-tag v-for="(item,key) in filterDataSource(dataSource.ad_positions,scope.row.ad_positions)" :key="key">
             {{ item }}
        </el-tag>
    </template>
</el-table-column> -->
        <el-table-column align="left" label="操作" :fixed="isMobile ? false : 'right'" :min-width="isMobile ? '210px' : appStore.operateMinWith">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateAdFunc(scope.row)">编辑</el-button>
            <el-button   type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>

        <!-- 卡片视图 -->
        <AdCardView 
          v-if="viewMode === 'card'" 
          :tableData="tableData" 
          :dataSource="dataSource" 
          v-model:multipleSelection="multipleSelection" 
          @getDetails="getDetails" 
          @updateAdFunc="updateAdFunc" 
          @deleteRow="deleteRow"
        />
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawerdialog destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
            </div>
          </template>

          <el-form :model="formData" label-position="right" ref="elFormRef" :rules="rule" label-width="100px">
            <el-form-item label="名称:" prop="name">
    <el-input v-model="formData.name" :clearable="false" placeholder="请输入名称" />
</el-form-item>
            <el-form-item label="类型:" prop="ad_type_id">
    <el-select v-model="formData.ad_type_id" placeholder="请选择类型" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.ad_type_id" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
            <el-form-item v-if="showExtarOption" label="广告行为:" prop="ad_action_id">
    <el-select v-model="formData.ad_action_id" placeholder="请选择广告行为" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.ad_action_id" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
            <el-form-item label="广告标题:" prop="title">
    <el-input v-model="formData.title" :clearable="true" placeholder="请输入广告标题" />
</el-form-item>
            <el-form-item label="广告描述:" prop="description">
    <el-input v-model="formData.description" :clearable="true" placeholder="请输入广告描述" />
</el-form-item>
            <el-form-item v-if="showExtarOption" label="广告位置:" prop="ad_positions">
    <el-select multiple v-model="formData.ad_positions" placeholder="请选择广告位置" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.ad_positions" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
            <el-form-item label="媒体文件:" prop="media_url">
    <SelectImage
     v-model="formData.media_url"
     file-type="image"
    />
</el-form-item>
            <el-form-item label="点击链接:" prop="click_url">
    <el-input v-model="formData.click_url" :clearable="true" placeholder="请输入点击链接" />
</el-form-item>
          </el-form>
          <template #footer>
              <div>
                <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                <el-button @click="closeDialog">取 消</el-button>
              </div>
          </template>
    </el-drawerdialog>

    <el-drawerdialog destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="名称">
    {{ detailFrom.name }}
</el-descriptions-item>
                    <!-- <el-descriptions-item label="广告主">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.user_id,detailFrom.user_id) }}</span>
    </template>
</el-descriptions-item> -->
                    <el-descriptions-item label="类型">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.ad_type_id,detailFrom.ad_type_id) }}</span>
    </template>
</el-descriptions-item>
                    <!-- <el-descriptions-item label="广告行为">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.ad_action_id,detailFrom.ad_action_id) }}</span>
    </template>
</el-descriptions-item> -->
                    <el-descriptions-item label="状态">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.status,detailFrom.status) }}</span>
    </template>
</el-descriptions-item>
                    <el-descriptions-item label="广告标题">
    {{ detailFrom.title }}
</el-descriptions-item>
                    <el-descriptions-item label="广告描述">
    {{ detailFrom.description }}
</el-descriptions-item>
                    <!-- <el-descriptions-item label="广告位置">
    <template #default="scope">
        <el-tag v-for="(item,key) in filterDataSource(dataSource.ad_positions,detailFrom.ad_positions)" :key="key">
             {{ item }}
        </el-tag>
    </template>
</el-descriptions-item> -->
                    <el-descriptions-item label="媒体文件">
    <el-image style="width: 89px; height: 50px" :preview-src-list="returnArrImg(detailFrom.media_url)" :src="getUrl(detailFrom.media_url)" fit="cover" />
</el-descriptions-item>
                    <el-descriptions-item label="点击链接">
    {{ detailFrom.click_url }}
</el-descriptions-item>
                    <el-descriptions-item label="审核备注">
    {{ detailFrom.review_note }}
</el-descriptions-item>
                    <el-descriptions-item label="审核时间">
    {{ detailFrom.reviewed_at }}
</el-descriptions-item>
                    <el-descriptions-item label="审核人">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.reviewer_id,detailFrom.reviewer_id) }}</span>
    </template>
</el-descriptions-item>
            </el-descriptions>
    </el-drawerdialog>

  </div>
</template>

<script setup>
import {
    getAdDataSource,
  createAd,
  deleteAd,
  deleteAdByIds,
  updateAd,
  findAd,
  getAdList
} from '@/api/meta/ad'
import { getUrl } from '@/utils/image'
// 图片选择组件
import SelectImage from '@/components/selectImage/selectImage.vue'
// 数组控制组件
import ArrayCtrl from '@/components/arrayCtrl/arrayCtrl.vue'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted } from 'vue'
import { useAppStore } from "@/pinia"
import { Grid, Postcard } from '@element-plus/icons-vue'
import AdCardView from './AdCardView.vue'

import { useIsMobile } from '@/utils/device'
const isMobile = useIsMobile()





defineOptions({
    name: 'Ad'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 视图模式控制
const viewMode = ref('card') // 'table' 或 'card'

// 从本地存储加载视图模式偏好
const loadViewModePreference = () => {
  const savedViewMode = localStorage.getItem('ad-view-mode')
  if (savedViewMode && ['table', 'card'].includes(savedViewMode)) {
    viewMode.value = savedViewMode
  }
}

// 保存视图模式偏好到本地存储
const saveViewModePreference = (mode) => {
  localStorage.setItem('ad-view-mode', mode)
}

// 切换视图模式
const switchViewMode = (mode) => {
  viewMode.value = mode
  saveViewModePreference(mode)
  // 清空多选状态，因为不同视图的选择机制可能不同
  multipleSelection.value = []
}

// 卡片视图相关函数已移至AdCardView组件

// 自动化生成的字典（可能为空）以及字段
const formData = ref({
            name: '',
            ad_type_id: undefined,
            ad_action_id: 1,
            title: '',
            description: '',
            ad_positions: [1],
            media_url: "",
            click_url: '',
        })
  const dataSource = ref([])
  const getDataSourceFunc = async()=>{
    const res = await getAdDataSource()
    if (res.code === 0) {
      dataSource.value = res.data
    }
  }
  getDataSourceFunc()



// 验证规则
const rule = reactive({
               name : [{
                   required: true,
                   message: '请输入广告名称',
                   trigger: ['input','blur'],
               },
               {
                   whitespace: true,
                   message: '不能只输入空格',
                   trigger: ['input', 'blur'],
              }
              ],
               ad_type_id : [{
                   required: true,
                   message: '请选择广告类型',
                   trigger: ['input','blur'],
               },
              ],
               ad_action_id : [{
                   required: true,
                   message: '请选择广告行为',
                   trigger: ['input','blur'],
               },
              ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(12)
const tableData = ref([])
const searchInfo = ref({})
// 排序
const sortChange = ({ prop, order }) => {
  const sortMap = {
    CreatedAt:"CreatedAt",
    ID:"ID",
            name: 'name',
  }

  let sort = sortMap[prop]
  if(!sort){
   sort = prop.replace(/[A-Z]/g, match => `_${match.toLowerCase()}`)
  }

  searchInfo.value.sort = sort
  searchInfo.value.order = order
  getTableData()
}
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getAdList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// 组件挂载时加载视图模式偏好
onMounted(() => {
  loadViewModePreference()
})

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])

const showExtarOption = ref(false)

// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteAdFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const IDs = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          IDs.push(item.ID)
        })
      const res = await deleteAdByIds({ IDs })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === IDs.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateAdFunc = async(row) => {
    const res = await findAd({ ID: row.ID })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteAdFunc = async (row) => {
    const res = await deleteAd({ ID: row.ID })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        name: '',
        ad_type_id: undefined,
        ad_action_id: undefined,
        title: '',
        description: '',
        ad_positions: [],
        media_url: "",
        click_url: '',
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createAd(formData.value)
                  break
                case 'update':
                  res = await updateAd(formData.value)
                  break
                default:
                  res = await createAd(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findAd({ ID: row.ID })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}


</script>

<style scoped>
/* 视图切换按钮样式 */
.view-switch-buttons {
  display: inline-flex;
  align-items: center;
}

.view-switch-buttons .el-button-group .el-button.is-active {
  background-color: var(--el-color-primary);
  color: white;
}
</style>
