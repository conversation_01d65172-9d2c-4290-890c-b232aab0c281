
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="名称:" prop="name">
    <el-input v-model="formData.name" :clearable="false" placeholder="请输入名称" />
</el-form-item>
        <el-form-item label="类型:" prop="ad_type_id">
    <el-select v-model="formData.ad_type_id" placeholder="请选择类型" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.ad_type_id" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
        <el-form-item label="广告行为:" prop="ad_action_id">
    <el-select v-model="formData.ad_action_id" placeholder="请选择广告行为" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.ad_action_id" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
        <el-form-item label="广告标题:" prop="title">
    <el-input v-model="formData.title" :clearable="true" placeholder="请输入广告标题" />
</el-form-item>
        <el-form-item label="广告描述:" prop="description">
    <el-input v-model="formData.description" :clearable="true" placeholder="请输入广告描述" />
</el-form-item>
        <el-form-item label="广告位置:" prop="ad_positions">
    <el-select multiple v-model="formData.ad_positions" placeholder="请选择广告位置" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.ad_positions" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
        <el-form-item label="媒体文件:" prop="media_url">
    <SelectImage
     v-model="formData.media_url"
     file-type="image"
    />
</el-form-item>
        <el-form-item label="点击链接:" prop="click_url">
    <el-input v-model="formData.click_url" :clearable="true" placeholder="请输入点击链接" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
    getAdDataSource,
  createAd,
  updateAd,
  findAd
} from '@/api/meta/ad'

defineOptions({
    name: 'AdForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'
// 图片选择组件
import SelectImage from '@/components/selectImage/selectImage.vue'
// 数组控制组件
import ArrayCtrl from '@/components/arrayCtrl/arrayCtrl.vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            name: '',
            ad_type_id: undefined,
            ad_action_id: undefined,
            title: '',
            description: '',
            ad_positions: [],
            media_url: "",
            click_url: '',
        })
// 验证规则
const rule = reactive({
               name : [{
                   required: true,
                   message: '请输入广告名称',
                   trigger: ['input','blur'],
               }],
               ad_type_id : [{
                   required: true,
                   message: '请选择广告类型',
                   trigger: ['input','blur'],
               }],
               ad_action_id : [{
                   required: true,
                   message: '请选择广告行为',
                   trigger: ['input','blur'],
               }],
})

const elFormRef = ref()
  const dataSource = ref([])
  const getDataSourceFunc = async()=>{
    const res = await getAdDataSource()
    if (res.code === 0) {
      dataSource.value = res.data
    }
  }
  getDataSourceFunc()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findAd({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createAd(formData.value)
               break
             case 'update':
               res = await updateAd(formData.value)
               break
             default:
               res = await createAd(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
