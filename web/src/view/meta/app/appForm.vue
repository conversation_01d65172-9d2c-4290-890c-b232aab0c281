
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="应用名称:" prop="name">
    <el-input v-model="formData.name" :clearable="false" placeholder="请输入应用名称" />
</el-form-item>
        <el-form-item label="包名:" prop="package_id">
    <el-input v-model="formData.package_id" :clearable="false" placeholder="请输入包名" />
</el-form-item>
        <el-form-item label="广告位:" prop="ad_positions">
    <el-select multiple v-model="formData.ad_positions" placeholder="请选择广告位" filterable style="width:100%" :clearable="true">
        <el-option v-for="(item,key) in dataSource.ad_positions" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
        <el-form-item label="是否上架:" prop="status">
    <el-switch v-model="formData.status" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
</el-form-item>
        <el-form-item label="允许广告:" prop="ad_enabled">
    <el-switch v-model="formData.ad_enabled" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
</el-form-item>
        <el-form-item label="应用描述:" prop="description">
    <el-input v-model="formData.description" :clearable="true" placeholder="请输入应用描述" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
    getAppDataSource,
  createApp,
  updateApp,
  findApp
} from '@/api/meta/app'

defineOptions({
    name: 'AppForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'
// 图片选择组件
import SelectImage from '@/components/selectImage/selectImage.vue'
// 数组控制组件
import ArrayCtrl from '@/components/arrayCtrl/arrayCtrl.vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            name: '',
            package_id: '',
            ad_positions: [],
            status: false,
            ad_enabled: false,
            description: '',
        })
// 验证规则
const rule = reactive({
               name : [{
                   required: true,
                   message: '请输入应用名称',
                   trigger: ['input','blur'],
               }],
               package_id : [{
                   required: true,
                   message: '请输入包名',
                   trigger: ['input','blur'],
               }],
})

const elFormRef = ref()
  const dataSource = ref([])
  const getDataSourceFunc = async()=>{
    const res = await getAppDataSource()
    if (res.code === 0) {
      dataSource.value = res.data
    }
  }
  getDataSourceFunc()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findApp({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createApp(formData.value)
               break
             case 'update':
               res = await updateApp(formData.value)
               break
             default:
               res = await createApp(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
