
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="审核类型:" prop="review_type">
    <el-input v-model="formData.review_type" :clearable="false" placeholder="请输入审核类型" />
</el-form-item>
        <el-form-item label="审核状态:" prop="review_status">
    <el-input v-model="formData.review_status" :clearable="false" placeholder="请输入审核状态" />
</el-form-item>
        <el-form-item label="审核结果:" prop="review_result">
    <el-input v-model="formData.review_result" :clearable="true" placeholder="请输入审核结果" />
</el-form-item>
        <el-form-item label="审核人:" prop="reviewer_id">
    <el-select v-model="formData.reviewer_id" placeholder="请选择审核人" filterable style="width:100%" :clearable="true">
        <el-option v-for="(item,key) in dataSource.reviewer_id" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
        <el-form-item label="审核备注:" prop="review_note">
    <el-input v-model="formData.review_note" :clearable="true" placeholder="请输入审核备注" />
</el-form-item>
        <el-form-item label="拒绝原因:" prop="reject_reason">
    <el-input v-model="formData.reject_reason" :clearable="true" placeholder="请输入拒绝原因" />
</el-form-item>
        <el-form-item label="风险等级:" prop="risk_level">
    <el-input v-model="formData.risk_level" :clearable="true" placeholder="请输入风险等级" />
</el-form-item>
        <el-form-item label="优先级:" prop="priority">
    <el-input v-model.number="formData.priority" :clearable="true" placeholder="请输入优先级" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
    getAdReviewDataSource,
  createAdReview,
  updateAdReview,
  findAdReview
} from '@/api/meta/adreview'

defineOptions({
    name: 'AdReviewForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            review_type: '',
            review_status: '',
            review_result: '',
            reviewer_id: undefined,
            review_note: '',
            reject_reason: '',
            risk_level: '',
            priority: undefined,
        })
// 验证规则
const rule = reactive({
               ad_id : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
               review_type : [{
                   required: true,
                   message: '请选择审核类型',
                   trigger: ['input','blur'],
               }],
               submit_time : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
})

const elFormRef = ref()
  const dataSource = ref([])
  const getDataSourceFunc = async()=>{
    const res = await getAdReviewDataSource()
    if (res.code === 0) {
      dataSource.value = res.data
    }
  }
  getDataSourceFunc()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findAdReview({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createAdReview(formData.value)
               break
             case 'update':
               res = await updateAdReview(formData.value)
               break
             default:
               res = await createAdReview(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
