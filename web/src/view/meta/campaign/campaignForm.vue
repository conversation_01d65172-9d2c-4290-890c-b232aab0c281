
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="计划名称:" prop="name">
    <el-input v-model="formData.name" :clearable="false" placeholder="请输入计划名称" />
</el-form-item>
        <el-form-item label="关联广告:" prop="ad_ids">
    <el-select multiple v-model="formData.ad_ids" placeholder="请选择关联广告" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.ad_ids" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
        <el-form-item label="计费方式:" prop="bid_type">
    <el-select v-model="formData.bid_type" placeholder="请选择计费方式" style="width:100%" filterable :clearable="false">
        <el-option v-for="(item,key) in billing_typeOptions" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
        <el-form-item label="投放目标:" prop="target_apps">
    <el-select multiple v-model="formData.target_apps" placeholder="请选择投放目标" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.target_apps" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
        <el-form-item label="开始时间:" prop="start_time">
    <el-date-picker v-model="formData.start_time" type="date" style="width:100%" placeholder="选择日期" :clearable="false" />
</el-form-item>
        <el-form-item label="结束时间:" prop="end_time">
    <el-date-picker v-model="formData.end_time" type="date" style="width:100%" placeholder="选择日期" :clearable="false" />
</el-form-item>
        <el-form-item label="总预算:" prop="total_budget">
    <el-input-number v-model="formData.total_budget" style="width:100%" :precision="2" :clearable="false" />
</el-form-item>
        <el-form-item label="日预算:" prop="daily_budget">
    <el-input-number v-model="formData.daily_budget" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
        <el-form-item label="出价金额:" prop="bid_amount">
    <el-input-number v-model="formData.bid_amount" style="width:100%" :precision="2" :clearable="false" />
</el-form-item>
        <el-form-item label="最大展示次数:" prop="max_impressions">
    <el-input v-model.number="formData.max_impressions" :clearable="true" placeholder="请输入最大展示次数" />
</el-form-item>
        <el-form-item label="最大点击次数:" prop="max_clicks">
    <el-input v-model.number="formData.max_clicks" :clearable="true" placeholder="请输入最大点击次数" />
</el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
    getCampaignDataSource,
  createCampaign,
  updateCampaign,
  findCampaign
} from '@/api/meta/campaign'

defineOptions({
    name: 'CampaignForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'
// 数组控制组件
import ArrayCtrl from '@/components/arrayCtrl/arrayCtrl.vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const billing_typeOptions = ref([])
const plan_statusOptions = ref([])
const formData = ref({
            name: '',
            ad_ids: [],
            bid_type: '',
            target_apps: [],
            start_time: new Date(),
            end_time: new Date(),
            total_budget: 0,
            daily_budget: 0,
            bid_amount: 0,
            max_impressions: undefined,
            max_clicks: undefined,
        })
// 验证规则
const rule = reactive({
               name : [{
                   required: true,
                   message: '请输入计划名称',
                   trigger: ['input','blur'],
               }],
               ad_ids : [{
                   required: true,
                   message: '请选择要投放的广告',
                   trigger: ['input','blur'],
               }],
               bid_type : [{
                   required: true,
                   message: '请选择计费方式',
                   trigger: ['input','blur'],
               }],
               start_time : [{
                   required: true,
                   message: '请选择开始时间',
                   trigger: ['input','blur'],
               }],
               end_time : [{
                   required: true,
                   message: '请选择结束时间',
                   trigger: ['input','blur'],
               }],
               total_budget : [{
                   required: true,
                   message: '请输入总预算',
                   trigger: ['input','blur'],
               }],
               bid_amount : [{
                   required: true,
                   message: '请输入出价金额',
                   trigger: ['input','blur'],
               }],
})

const elFormRef = ref()
  const dataSource = ref([])
  const getDataSourceFunc = async()=>{
    const res = await getCampaignDataSource()
    if (res.code === 0) {
      dataSource.value = res.data
    }
  }
  getDataSourceFunc()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findCampaign({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
    billing_typeOptions.value = await getDictFunc('billing_type')
    plan_statusOptions.value = await getDictFunc('plan_status')
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createCampaign(formData.value)
               break
             case 'update':
               res = await updateCampaign(formData.value)
               break
             default:
               res = await createCampaign(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
