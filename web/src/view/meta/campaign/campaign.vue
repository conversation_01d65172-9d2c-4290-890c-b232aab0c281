
<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" @keyup.enter="onSubmit">
      <el-form-item label="创建日期" prop="createdAtRange">
      <template #label>
        <span>
          创建日期
          <el-tooltip content="搜索范围是开始日期（包含）至结束日期（不包含）">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </span>
      </template>

      <el-date-picker
            v-model="searchInfo.createdAtRange"
            :class="isMobile ? 'w-full' : 'w-[380px]'"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
       </el-form-item>
      

        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
        <div class="gva-btn-list">
            <el-button  type="primary" icon="plus" @click="openDialog()">新投放</el-button>
            <el-button  icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
            <!-- 视图切换按钮 -->
            <div class="view-switch-buttons" style="margin-left: 10px;">
              <el-button-group>
                <el-button
                  :type="viewMode === 'table' ? 'primary' : ''"
                  @click="switchViewMode('table')"
                  :class="{ 'is-active': viewMode === 'table' }"
                >
                  <el-icon><Grid /></el-icon>
                  表格
                </el-button>
                <el-button
                  :type="viewMode === 'card' ? 'primary' : ''"
                  @click="switchViewMode('card')"
                  :class="{ 'is-active': viewMode === 'card' }"
                >
                  <el-icon><Postcard /></el-icon>
                  卡片
                </el-button>
              </el-button-group>
            </div>
        </div>
        <!-- 表格视图 -->
        <el-table
        v-if="viewMode === 'table'"
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
        @selection-change="handleSelectionChange"
        @sort-change="sortChange"
        >
        <el-table-column type="selection" width="55" />

        <el-table-column sortable align="left" label="日期" prop="CreatedAt"width="180">
            <template #default="scope">{{ formatDate(scope.row.CreatedAt) }}</template>
        </el-table-column>

            <el-table-column sortable align="left" label="计划名称" prop="name" width="120">
                <template #default="scope">
                    <el-button type="text" @click="openStatsChart(scope.row)" class="campaign-name-link">
                        {{ scope.row.name }}
                    </el-button>
                </template>
            </el-table-column>

            <el-table-column align="left" label="状态" prop="status" width="120">
    <template #default="scope">
    {{ filterDict(scope.row.status,plan_statusOptions) }}
    </template>
</el-table-column>
            <el-table-column align="left" label="计费方式" prop="bid_type" width="150">
    <template #default="scope">
    {{ filterDict(scope.row.bid_type,billing_typeOptions) }}
    </template>
</el-table-column>
            <el-table-column sortable align="left" label="开始时间" prop="start_time" width="180">
   <template #default="scope">{{ formatDate(scope.row.start_time) }}</template>
</el-table-column>
            <el-table-column sortable align="left" label="结束时间" prop="end_time" width="180">
   <template #default="scope">{{ formatDate(scope.row.end_time) }}</template>
</el-table-column>
            <el-table-column sortable align="left" label="总预算" prop="total_budget" width="120" />

            <el-table-column align="left" label="日预算" prop="daily_budget" width="120" />

            <el-table-column align="left" label="出价金额" prop="bid_amount" width="120" />

            <el-table-column align="left" label="最大展示次数" prop="max_impressions" width="120" />

            <el-table-column align="left" label="最大点击次数" prop="max_clicks" width="120" />

        <el-table-column align="left" label="操作" :fixed="isMobile ? false : 'right'" :min-width="isMobile ? '210px' : '280px'">
            <template #default="scope">
            <el-button  type="primary" link class="table-button" @click="getDetails(scope.row)"><el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看</el-button>
            <el-button  type="primary" link icon="edit" class="table-button" @click="updateCampaignFunc(scope.row)">编辑</el-button>
            <el-button  type="primary" link icon="edit" @click="pauseRow(scope.row)">暂停</el-button>
            <el-button  type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
            </template>
        </el-table-column>
        </el-table>

        <!-- 卡片视图 -->
        <div v-if="viewMode === 'card'" class="campaign-card-container">
          <div class="campaign-card-grid">
            <CampaignCard
              v-for="item in tableData"
              :key="item.ID"
              :campaign="item"
              :is-selected="isCardSelected(item)"
              :data-source="dataSource"
              :billing-type-options="billing_typeOptions"
              :plan-status-options="plan_statusOptions"
              @selection-change="handleCardSelectionChange"
              @view="getDetails"
              @edit="updateCampaignFunc"
              @delete="deleteRow"
              @pause="pauseRow"
              @card-click="toggleCardSelection"
            />
          </div>
        </div>
        <div class="gva-pagination">
            <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            />
        </div>
    </div>
    <el-drawerdialog destroy-on-close :size="appStore.drawerSize" v-model="dialogFormVisible" :show-close="false" :before-close="closeDialog">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="text-lg">{{type==='create'?'新增':'编辑'}}</span>
            </div>
          </template>

          <el-form :model="formData" label-position="right" ref="elFormRef" :rules="rule" label-width="100px">
            <el-form-item label="计划名称:" prop="name">
    <el-input v-model="formData.name" :clearable="false" placeholder="请输入计划名称" />
</el-form-item>
            <el-form-item label="关联广告:" prop="ad_ids">
    <el-select multiple v-model="formData.ad_ids" placeholder="请选择关联广告" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.ad_ids" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
            <el-form-item label="计费方式:" prop="bid_type">
    <el-select v-model="formData.bid_type" placeholder="请选择计费方式" style="width:100%" filterable :clearable="false">
        <el-option v-for="(item,key) in billing_typeOptions" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item>
            <!-- <el-form-item label="投放目标:" prop="target_apps">
    <el-select multiple v-model="formData.target_apps" placeholder="请选择投放目标" filterable style="width:100%" :clearable="false">
        <el-option v-for="(item,key) in dataSource.target_apps" :key="key" :label="item.label" :value="item.value" />
    </el-select>
</el-form-item> -->
            <el-form-item label="开始时间:" prop="start_time">
    <el-date-picker v-model="formData.start_time" type="date" style="width:100%" placeholder="选择日期" :clearable="false" />
</el-form-item>
            <el-form-item label="结束时间:" prop="end_time">
    <el-date-picker v-model="formData.end_time" type="date" style="width:100%" placeholder="选择日期" :clearable="false" />
</el-form-item>
            <el-form-item label="总预算:" prop="total_budget">
    <el-input-number v-model="formData.total_budget" style="width:100%" :precision="2" :clearable="false" />
</el-form-item>
            <el-form-item label="日预算:" prop="daily_budget">
    <el-input-number v-model="formData.daily_budget" style="width:100%" :precision="2" :clearable="true" />
</el-form-item>
            <el-form-item label="出价金额:" prop="bid_amount">
    <el-input-number v-model="formData.bid_amount" style="width:100%" :precision="2" :clearable="false" />
</el-form-item>
            <el-form-item label="最大展示次数:" prop="max_impressions">
    <el-input v-model.number="formData.max_impressions" :clearable="true" placeholder="请输入最大展示次数" />
</el-form-item>
            <el-form-item label="最大点击次数:" prop="max_clicks">
    <el-input v-model.number="formData.max_clicks" :clearable="true" placeholder="请输入最大点击次数" />
</el-form-item>
          </el-form>
          <template #footer>
              <div>
                <el-button :loading="btnLoading" type="primary" @click="enterDialog">确 定</el-button>
                <el-button @click="closeDialog">取 消</el-button>
              </div>
          </template>
    </el-drawerdialog>

    <el-drawerdialog destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="计划名称">
    {{ detailFrom.name }}
</el-descriptions-item>
                    <!-- <el-descriptions-item label="广告主">
    <template #default="scope">
        <span>{{ filterDataSource(dataSource.user_id,detailFrom.user_id) }}</span>
    </template>
</el-descriptions-item> -->
                    <el-descriptions-item label="状态">
                      {{ filterDict(detailFrom.status,plan_statusOptions) }}
</el-descriptions-item>
                    <el-descriptions-item label="关联广告">
    <template #default="scope">
        <el-tag v-for="(item,key) in filterDataSource(dataSource.ad_ids,detailFrom.ad_ids)" :key="key">
             {{ item }}
        </el-tag>
    </template>
</el-descriptions-item>
                    <el-descriptions-item label="计费方式">
    {{ detailFrom.bid_type }}
</el-descriptions-item>
                    <el-descriptions-item label="投放目标">
    <template #default="scope">
      <template v-if="!detailFrom.target_apps || detailFrom.target_apps.length === 0">
        <el-tag>全部</el-tag>
      </template>
      <template v-else>
        <el-tag v-for="(item,key) in filterDataSource(dataSource.target_apps,detailFrom.target_apps)" :key="key">
          {{ item }}
        </el-tag>
      </template>
          </template>
      </el-descriptions-item>
                    <el-descriptions-item label="开始时间">
    {{ formatDate(detailFrom.start_time) }}
</el-descriptions-item>
                    <el-descriptions-item label="结束时间">
    {{ formatDate(detailFrom.end_time) }}
</el-descriptions-item>
                    <el-descriptions-item label="总预算">
    {{ detailFrom.total_budget }}
</el-descriptions-item>
                    <el-descriptions-item label="日预算">
    {{ detailFrom.daily_budget }}
</el-descriptions-item>
                    <el-descriptions-item label="出价金额">
    {{ detailFrom.bid_amount }}
</el-descriptions-item>
                    <el-descriptions-item label="最大展示次数">
    {{ detailFrom.max_impressions }}
</el-descriptions-item>
                    <el-descriptions-item label="最大点击次数">
    {{ detailFrom.max_clicks }}
</el-descriptions-item>
            </el-descriptions>
    </el-drawerdialog>

    <!-- 统计图表组件 -->
    <StatsChart
      v-model="statsChartVisible"
      :target-id="statsChartTargetId"
      :target-type="statsChartTargetType"
    />

  </div>
</template>

<script setup>
import {
    getCampaignDataSource,
  createCampaign,
  deleteCampaign,
  deleteCampaignByIds,
  updateCampaign,
  findCampaign,
  pauseCampaign,
  getCampaignList
} from '@/api/meta/campaign'
// 数组控制组件
import ArrayCtrl from '@/components/arrayCtrl/arrayCtrl.vue'
// 卡片组件
import CampaignCard from './components/CampaignCard.vue'
// 统计图表组件
import StatsChart from '@/components/StatsChart/StatsChart.vue'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict ,filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onMounted } from 'vue'
import { useAppStore } from "@/pinia"
import { Grid, Postcard } from '@element-plus/icons-vue'

import { useIsMobile } from '@/utils/device'
const isMobile = useIsMobile()





defineOptions({
    name: 'Campaign'
})

// 提交按钮loading
const btnLoading = ref(false)
const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

// 视图模式控制
const viewMode = ref('card') // 'table' 或 'card'

// 从本地存储加载视图模式偏好
const loadViewModePreference = () => {
  const savedViewMode = localStorage.getItem('campaign-view-mode')
  if (savedViewMode && ['table', 'card'].includes(savedViewMode)) {
    viewMode.value = savedViewMode
  }
}

// 保存视图模式偏好到本地存储
const saveViewModePreference = (mode) => {
  localStorage.setItem('campaign-view-mode', mode)
}

// 切换视图模式
const switchViewMode = (mode) => {
  viewMode.value = mode
  saveViewModePreference(mode)
  // 清空多选状态，因为不同视图的选择机制可能不同
  multipleSelection.value = []
}

// 卡片选择相关
const isCardSelected = (item) => {
  return multipleSelection.value.some(selected => selected.ID === item.ID)
}

const toggleCardSelection = (item) => {
  const index = multipleSelection.value.findIndex(selected => selected.ID === item.ID)
  if (index > -1) {
    multipleSelection.value.splice(index, 1)
  } else {
    multipleSelection.value.push(item)
  }
}

// 处理卡片选择变化
const handleCardSelectionChange = (campaign, isSelected) => {
  if (isSelected) {
    if (!isCardSelected(campaign)) {
      multipleSelection.value.push(campaign)
    }
  } else {
    const index = multipleSelection.value.findIndex(selected => selected.ID === campaign.ID)
    if (index > -1) {
      multipleSelection.value.splice(index, 1)
    }
  }
}

// 自动化生成的字典（可能为空）以及字段
const billing_typeOptions = ref([])
const plan_statusOptions = ref([])
const formData = ref({
            name: '',
            ad_ids: [],
            bid_type: '',
            target_apps: [],
            start_time: new Date(),
            end_time: new Date(),
            total_budget: 0,
            daily_budget: 0,
            bid_amount: 0,
            max_impressions: undefined,
            max_clicks: undefined,
        })
  const dataSource = ref([])
  const getDataSourceFunc = async()=>{
    const res = await getCampaignDataSource()
    if (res.code === 0) {
      dataSource.value = res.data
    }
  }
  getDataSourceFunc()



// 验证规则
const rule = reactive({
               name : [{
                   required: true,
                   message: '请输入计划名称',
                   trigger: ['input','blur'],
               },
               {
                   whitespace: true,
                   message: '不能只输入空格',
                   trigger: ['input', 'blur'],
              }
              ],
               ad_ids : [{
                   required: true,
                   message: '请选择要投放的广告',
                   trigger: ['input','blur'],
               },
              ],
               bid_type : [{
                   required: true,
                   message: '请选择计费方式',
                   trigger: ['input','blur'],
               },
               {
                   whitespace: true,
                   message: '不能只输入空格',
                   trigger: ['input', 'blur'],
              }
              ],
               start_time : [{
                   required: true,
                   message: '请选择开始时间',
                   trigger: ['input','blur'],
               },
              ],
               end_time : [{
                   required: true,
                   message: '请选择结束时间',
                   trigger: ['input','blur'],
               },
              ],
               total_budget : [{
                   required: true,
                   message: '请输入总预算',
                   trigger: ['input','blur'],
               },
              ],
               bid_amount : [{
                   required: true,
                   message: '请输入出价金额',
                   trigger: ['input','blur'],
               },
              ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(12)
const tableData = ref([])
const searchInfo = ref({})
// 排序
const sortChange = ({ prop, order }) => {
  const sortMap = {
    CreatedAt:"CreatedAt",
    ID:"ID",
            name: 'name',
            start_time: 'start_time',
            end_time: 'end_time',
            total_budget: 'total_budget',
  }

  let sort = sortMap[prop]
  if(!sort){
   sort = prop.replace(/[A-Z]/g, match => `_${match.toLowerCase()}`)
  }

  searchInfo.value.sort = sort
  searchInfo.value.order = order
  getTableData()
}
// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getCampaignList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// 组件挂载时加载视图模式偏好
onMounted(() => {
  loadViewModePreference()
})

// ============== 表格控制部分结束 ===============

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
    billing_typeOptions.value = await getDictFunc('billing_type')
    plan_statusOptions.value = await getDictFunc('plan_status')
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 暂停
const pauseRow = (row) =>{
  let query = {"ids":[row.ID], "stop": row.status == "running" ? true : false}
  pauseCampaign(query).then(res => {
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      getTableData()
    }
  })
}

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteCampaignFunc(row)
        })
    }

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
      const IDs = []
      if (multipleSelection.value.length === 0) {
        ElMessage({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      multipleSelection.value &&
        multipleSelection.value.map(item => {
          IDs.push(item.ID)
        })
      const res = await deleteCampaignByIds({ IDs })
      if (res.code === 0) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        })
        if (tableData.value.length === IDs.length && page.value > 1) {
          page.value--
        }
        getTableData()
      }
      })
    }

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateCampaignFunc = async(row) => {
    const res = await findCampaign({ ID: row.ID })
    type.value = 'update'
    if (res.code === 0) {
        formData.value = res.data
        dialogFormVisible.value = true
    }
}


// 删除行
const deleteCampaignFunc = async (row) => {
    const res = await deleteCampaign({ ID: row.ID })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
            if (tableData.value.length === 1 && page.value > 1) {
            page.value--
        }
        getTableData()
    }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
    type.value = 'create'
    dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
    dialogFormVisible.value = false
    formData.value = {
        name: '',
        ad_ids: [],
        bid_type: '',
        target_apps: [],
        start_time: new Date(),
        end_time: new Date(),
        total_budget: 0,
        daily_budget: 0,
        bid_amount: 0,
        max_impressions: undefined,
        max_clicks: undefined,
        }
}
// 弹窗确定
const enterDialog = async () => {
     btnLoading.value = true
     elFormRef.value?.validate( async (valid) => {
             if (!valid) return btnLoading.value = false
              let res
              switch (type.value) {
                case 'create':
                  res = await createCampaign(formData.value)
                  break
                case 'update':
                  res = await updateCampaign(formData.value)
                  break
                default:
                  res = await createCampaign(formData.value)
                  break
              }
              btnLoading.value = false
              if (res.code === 0) {
                ElMessage({
                  type: 'success',
                  message: '创建/更改成功'
                })
                closeDialog()
                getTableData()
              }
      })
}

const detailFrom = ref({})

// 查看详情控制标记
const detailShow = ref(false)

// 统计图表相关
const statsChartVisible = ref(false)
const statsChartTargetId = ref(0)
const statsChartTargetType = ref(3) // 默认为广告计划类型


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findCampaign({ ID: row.ID })
  if (res.code === 0) {
    detailFrom.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailFrom.value = {}
}

// 打开统计图表
const openStatsChart = (row) => {
  statsChartTargetId.value = row.ID
  statsChartTargetType.value = 3 // 广告计划类型
  statsChartVisible.value = true
}


</script>

<style scoped>
/* 视图切换按钮样式 */
.view-switch-buttons {
  display: inline-flex;
  align-items: center;
}

.view-switch-buttons .el-button-group .el-button.is-active {
  background-color: var(--el-color-primary);
  color: white;
}

/* 卡片容器样式 */
.campaign-card-container {
  width: 100%;
}

.campaign-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

/* 计划名称链接样式 */
.campaign-name-link {
  padding: 0;
  font-weight: 500;
  color: #409EFF;
  text-decoration: none;
}

.campaign-name-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .campaign-card-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .campaign-card-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}
</style>
