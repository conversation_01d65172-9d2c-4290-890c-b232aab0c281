<template>
  <div class="stats-chart-test">
    <h1>统计图表测试页面</h1>
    
    <div class="test-buttons">
      <el-button type="primary" @click="testCampaignChart">
        测试广告计划图表 (ID: 1, Type: 3)
      </el-button>
      
      <el-button type="success" @click="testAdChart">
        测试广告图表 (ID: 1, Type: 2)
      </el-button>
      
      <el-button type="warning" @click="testAppChart">
        测试APP图表 (ID: 1, Type: 1)
      </el-button>
    </div>
    
    <!-- 统计图表组件 -->
    <StatsChart
      v-model="statsChartVisible"
      :target-id="statsChartTargetId"
      :target-type="statsChartTargetType"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import StatsChart from '@/components/StatsChart/StatsChart.vue'

// 响应式数据
const statsChartVisible = ref(false)
const statsChartTargetId = ref(0)
const statsChartTargetType = ref(3)

// 测试广告计划图表
const testCampaignChart = () => {
  statsChartTargetId.value = 1
  statsChartTargetType.value = 3
  statsChartVisible.value = true
}

// 测试广告图表
const testAdChart = () => {
  statsChartTargetId.value = 1
  statsChartTargetType.value = 2
  statsChartVisible.value = true
}

// 测试APP图表
const testAppChart = () => {
  statsChartTargetId.value = 1
  statsChartTargetType.value = 1
  statsChartVisible.value = true
}
</script>

<style scoped>
.stats-chart-test {
  padding: 20px;
}

.test-buttons {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.test-buttons .el-button {
  margin-right: 10px;
}
</style>
