<template>
  <div class="mobile-layout" v-if="isMobile">
    <!-- 主要内容区域 -->
    <router-view />
    
    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
  
  <!-- 非移动端重定向到桌面版 -->
  <div v-else class="desktop-redirect">
    <div class="redirect-content">
      <el-icon class="redirect-icon"><Monitor /></el-icon>
      <h2>请在移动设备上访问</h2>
      <p>此页面专为移动设备优化，请使用手机或平板电脑访问。</p>
      <el-button type="primary" @click="goToDesktop">
        前往桌面版
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useIsMobile } from '@/utils/device'
import BottomNavigation from '@/components/mobile/BottomNavigation.vue'
import { Monitor } from '@element-plus/icons-vue'

defineOptions({
  name: 'MobileLayout'
})

const router = useRouter()
const isMobile = useIsMobile()

const goToDesktop = () => {
  router.push('/dashboard')
}
</script>

<style scoped>
.mobile-layout {
  min-height: 100vh;
  background: #f5f7fa;
}

.desktop-redirect {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f7fa;
}

.redirect-content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 400px;
}

.redirect-icon {
  font-size: 64px;
  color: #409eff;
  margin-bottom: 20px;
}

.redirect-content h2 {
  margin: 0 0 16px 0;
  color: #303133;
}

.redirect-content p {
  margin: 0 0 24px 0;
  color: #606266;
  line-height: 1.6;
}
</style>
