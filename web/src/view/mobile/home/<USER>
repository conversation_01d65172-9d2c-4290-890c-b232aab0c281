<template>
  <div class="mobile-home mobile-content">
    <!-- 顶部欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-header">
        <h2>欢迎回来</h2>
        <p>{{ currentTime }}</p>
      </div>
      <div class="user-avatar">
        <el-avatar :size="50" :src="userInfo.headerImg || ''" />
      </div>
    </div>

    <!-- 快捷功能区域 -->
    <div class="quick-actions">
      <h3>快捷功能</h3>
      <div class="action-grid">
        <div class="action-item" @click="openAdWizard">
          <el-icon class="action-icon">
            <Plus />
          </el-icon>
          <span>快速添加广告</span>
        </div>
        <div class="action-item" @click="viewStats">
          <el-icon class="action-icon">
            <DataAnalysis />
          </el-icon>
          <span>查看统计</span>
        </div>
        <div class="action-item" @click="viewAds">
          <el-icon class="action-icon">
            <Monitor />
          </el-icon>
          <span>我的广告</span>
        </div>
        <div class="action-item" @click="viewCampaigns">
          <el-icon class="action-icon">
            <Operation />
          </el-icon>
          <span>投放计划</span>
        </div>
      </div>
    </div>

    <!-- 数据概览 -->
    <div class="stats-overview">
      <h3>数据概览</h3>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-value">{{ stats.totalAds }}</div>
          <div class="stat-label">总广告数</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ stats.activeCampaigns }}</div>
          <div class="stat-label">活跃计划</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ stats.todayClicks }}</div>
          <div class="stat-label">今日点击</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">¥{{ stats.todaySpend }}</div>
          <div class="stat-label">今日消费</div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <h3>最近活动</h3>
      <div class="activity-list">
        <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
          <div class="activity-icon">
            <el-icon>
              <component :is="activity.icon" />
            </el-icon>
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-time">{{ formatTime(activity.time) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/pinia'
import { formatTimeToStr } from '@/utils/date'
import { 
  Plus, 
  DataAnalysis, 
  Monitor, 
  Operation,
  SuccessFilled,
  Warning,
  InfoFilled
} from '@element-plus/icons-vue'

defineOptions({
  name: 'MobileHome'
})

const router = useRouter()
const userStore = useUserStore()

// 用户信息
const userInfo = computed(() => userStore.userInfo)

// 当前时间
const currentTime = computed(() => {
  const now = new Date()
  const hour = now.getHours()
  let greeting = '早上好'
  if (hour >= 12 && hour < 18) {
    greeting = '下午好'
  } else if (hour >= 18) {
    greeting = '晚上好'
  }
  return `${greeting}，${formatTimeToStr(now, 'MM-dd')}`
})

// 统计数据
const stats = ref({
  totalAds: 0,
  activeCampaigns: 0,
  todayClicks: 0,
  todaySpend: 0
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '广告"春季促销"审核通过',
    time: new Date(Date.now() - 1000 * 60 * 30),
    icon: SuccessFilled
  },
  {
    id: 2,
    title: '投放计划"品牌推广"已启动',
    time: new Date(Date.now() - 1000 * 60 * 60 * 2),
    icon: InfoFilled
  },
  {
    id: 3,
    title: '预算不足提醒',
    time: new Date(Date.now() - 1000 * 60 * 60 * 4),
    icon: Warning
  }
])

// 快捷功能方法
const openAdWizard = () => {
  router.push('/mobile/ad-wizard')
}

const viewStats = () => {
  router.push('/mobile/stats')
}

const viewAds = () => {
  router.push('/mobile/system?tab=ads')
}

const viewCampaigns = () => {
  router.push('/mobile/system?tab=campaigns')
}

// 格式化时间
const formatTime = (time) => {
  const now = new Date()
  const diff = now - time
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return `${days}天前`
  }
}

// 加载数据
const loadStats = async () => {
  // TODO: 调用API获取统计数据
  stats.value = {
    totalAds: 12,
    activeCampaigns: 3,
    todayClicks: 156,
    todaySpend: 89.5
  }
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.mobile-home {
  padding: 16px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.welcome-header h2 {
  margin: 0 0 4px 0;
  font-size: 20px;
  color: #303133;
}

.welcome-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.quick-actions, .stats-overview, .recent-activity {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.quick-actions h3, .stats-overview h3, .recent-activity h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 12px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-item:hover {
  background: #e3f2fd;
  transform: translateY(-2px);
}

.action-icon {
  font-size: 24px;
  color: #409eff;
  margin-bottom: 8px;
}

.action-item span {
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-card {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

.activity-list {
  space-y: 12px;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.activity-icon .el-icon {
  color: #409eff;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 2px;
}

.activity-time {
  font-size: 12px;
  color: #909399;
}
</style>
