<template>
  <div class="mobile-test">
    <h1>移动端测试页面</h1>
    
    <div class="test-section">
      <h2>底部导航测试</h2>
      <p>请检查页面底部是否显示了导航栏</p>
    </div>

    <div class="test-section">
      <h2>路由测试</h2>
      <div class="button-group">
        <el-button @click="goToHome">首页</el-button>
        <el-button @click="goToSystem">系统</el-button>
        <el-button @click="goToSettings">设置</el-button>
        <el-button @click="goToWizard">广告向导</el-button>
      </div>
    </div>

    <div class="test-section">
      <h2>API测试</h2>
      <div class="button-group">
        <el-button @click="testAdAPI" :loading="loading">测试广告API</el-button>
        <el-button @click="testCampaignAPI" :loading="loading">测试投放计划API</el-button>
      </div>
      <div v-if="apiResult" class="api-result">
        <h3>API结果:</h3>
        <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
      </div>
    </div>

    <div class="test-section">
      <h2>设备检测</h2>
      <p>是否为移动设备: {{ isMobile ? '是' : '否' }}</p>
      <p>屏幕宽度: {{ screenWidth }}px</p>
      <p>用户代理: {{ userAgent }}</p>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useIsMobile } from '@/utils/device'
import { getAdList } from '@/api/meta/ad'
import { getCampaignList } from '@/api/meta/campaign'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'MobileTest'
})

const router = useRouter()
const isMobile = useIsMobile()

const loading = ref(false)
const apiResult = ref(null)
const screenWidth = ref(window.innerWidth)
const userAgent = ref(navigator.userAgent)

// 路由测试
const goToHome = () => {
  router.push('/mobile/home')
}

const goToSystem = () => {
  router.push('/mobile/system')
}

const goToSettings = () => {
  router.push('/mobile/settings')
}

const goToWizard = () => {
  router.push('/mobile/ad-wizard')
}

// API测试
const testAdAPI = async () => {
  loading.value = true
  try {
    const response = await getAdList({ page: 1, pageSize: 10 })
    apiResult.value = response
    ElMessage.success('广告API测试成功')
  } catch (error) {
    apiResult.value = { error: error.message }
    ElMessage.error('广告API测试失败')
  } finally {
    loading.value = false
  }
}

const testCampaignAPI = async () => {
  loading.value = true
  try {
    const response = await getCampaignList({ page: 1, pageSize: 10 })
    apiResult.value = response
    ElMessage.success('投放计划API测试成功')
  } catch (error) {
    apiResult.value = { error: error.message }
    ElMessage.error('投放计划API测试失败')
  } finally {
    loading.value = false
  }
}

// 监听屏幕大小变化
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', updateScreenWidth)
})
</script>

<style scoped>
.mobile-test {
  padding: 20px;
  padding-bottom: 80px; /* 为底部导航留出空间 */
  max-width: 100%;
  background: #f5f7fa;
  min-height: 100vh;
}

.test-section {
  background: white;
  padding: 20px;
  margin-bottom: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.test-section h2 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.button-group .el-button {
  flex: 1;
  min-width: 120px;
}

.api-result {
  margin-top: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.api-result h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #606266;
}

.api-result pre {
  margin: 0;
  font-size: 12px;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
}
</style>
