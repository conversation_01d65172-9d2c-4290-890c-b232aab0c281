<template>
  <div class="ad-wizard mobile-content">
    <!-- 顶部导航 -->
    <div class="wizard-header">
      <el-icon class="back-btn" @click="goBack"><ArrowLeft /></el-icon>
      <div class="wizard-title">快速添加广告</div>
      <div class="step-indicator">{{ currentStep }}/4</div>
    </div>

    <!-- 进度条 -->
    <div class="progress-bar">
      <div class="progress-fill" :style="{ width: `${(currentStep / 4) * 100}%` }"></div>
    </div>

    <!-- 步骤内容 -->
    <div class="wizard-content">
      <!-- 第一步：选择广告目标 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="step-title">您希望达到什么效果？</div>
        <div class="step-subtitle">选择最符合您需求的广告目标</div>
        
        <div class="option-cards">
          <div 
            class="option-card"
            :class="{ active: formData.objective === 'brand' }"
            @click="selectObjective('brand')"
          >
            <div class="option-icon">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="option-title">品牌宣传</div>
            <div class="option-desc">提升品牌知名度和曝光度</div>
            <div class="option-type">CPM 计费</div>
          </div>
          
          <div 
            class="option-card"
            :class="{ active: formData.objective === 'traffic' }"
            @click="selectObjective('traffic')"
          >
            <div class="option-icon">
              <el-icon><Position /></el-icon>
            </div>
            <div class="option-title">引流推广</div>
            <div class="option-desc">吸引用户点击访问您的网站</div>
            <div class="option-type">CPC 计费</div>
          </div>
        </div>
      </div>

      <!-- 第二步：选择广告类型和素材 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="step-title">选择广告类型</div>
        <div class="step-subtitle">选择最适合的广告形式</div>
        
        <div class="ad-type-selector">
          <div 
            v-for="type in adTypes" 
            :key="type.id"
            class="type-option"
            :class="{ active: formData.adTypeId === type.id }"
            @click="selectAdType(type.id)"
          >
            <el-icon><component :is="type.icon" /></el-icon>
            <span>{{ type.name }}</span>
          </div>
        </div>

        <div class="media-upload">
          <div class="upload-title">上传广告图片</div>
          <div class="upload-area" @click="selectImage">
            <div v-if="!formData.mediaUrl" class="upload-placeholder">
              <el-icon><Plus /></el-icon>
              <span>点击上传图片</span>
              <div class="upload-tips">建议尺寸：1200x628px</div>
            </div>
            <div v-else class="uploaded-image">
              <img :src="formData.mediaUrl" alt="广告图片" />
              <div class="image-overlay" @click.stop="removeImage">
                <el-icon><Delete /></el-icon>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第三步：填写基础信息 -->
      <div v-if="currentStep === 3" class="step-content">
        <div class="step-title">填写广告信息</div>
        <div class="step-subtitle">完善您的广告内容</div>
        
        <div class="form-section">
          <div class="form-item">
            <label>广告名称</label>
            <el-input 
              v-model="formData.name" 
              placeholder="请输入广告名称"
              maxlength="50"
              show-word-limit
            />
          </div>
          
          <div class="form-item">
            <label>广告标题</label>
            <el-input 
              v-model="formData.title" 
              placeholder="请输入广告标题"
              maxlength="30"
              show-word-limit
            />
          </div>
          
          <div class="form-item">
            <label>广告描述</label>
            <el-input 
              v-model="formData.description" 
              type="textarea"
              placeholder="请输入广告描述"
              :rows="3"
              maxlength="100"
              show-word-limit
            />
          </div>
          
          <div class="form-item">
            <label>点击跳转链接</label>
            <el-input 
              v-model="formData.clickUrl" 
              placeholder="https://example.com"
            />
          </div>
        </div>
      </div>

      <!-- 第四步：设置预算和发布 -->
      <div v-if="currentStep === 4" class="step-content">
        <div class="step-title">设置预算</div>
        <div class="step-subtitle">设置您的广告投放预算</div>
        
        <div class="budget-section">
          <div class="form-item">
            <label>出价金额</label>
            <div class="bid-input">
              <span class="currency">¥</span>
              <el-input 
                v-model="formData.bidAmount" 
                type="number"
                placeholder="0.00"
                step="0.01"
              />
              <span class="bid-type">/ {{ formData.objective === 'brand' ? '千次展示' : '点击' }}</span>
            </div>
          </div>
          
          <div class="form-item">
            <label>总预算</label>
            <div class="budget-input">
              <span class="currency">¥</span>
              <el-input 
                v-model="formData.totalBudget" 
                type="number"
                placeholder="0.00"
                step="0.01"
              />
            </div>
          </div>
          
          <div class="form-item">
            <label>日预算（可选）</label>
            <div class="budget-input">
              <span class="currency">¥</span>
              <el-input 
                v-model="formData.dailyBudget" 
                type="number"
                placeholder="0.00"
                step="0.01"
              />
            </div>
          </div>
        </div>

        <!-- 预览卡片 -->
        <div class="preview-card">
          <div class="preview-title">广告预览</div>
          <div class="ad-preview">
            <div class="preview-image">
              <img v-if="formData.mediaUrl" :src="formData.mediaUrl" alt="广告预览" />
              <div v-else class="no-image">暂无图片</div>
            </div>
            <div class="preview-content">
              <div class="preview-ad-title">{{ formData.title || '广告标题' }}</div>
              <div class="preview-desc">{{ formData.description || '广告描述' }}</div>
              <div class="preview-url">{{ formData.clickUrl || 'https://example.com' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="wizard-footer">
      <el-button 
        v-if="currentStep > 1" 
        @click="prevStep"
        class="footer-btn"
      >
        上一步
      </el-button>
      
      <el-button 
        v-if="currentStep < 4"
        type="primary" 
        @click="nextStep"
        :disabled="!canProceed"
        class="footer-btn"
      >
        下一步
      </el-button>
      
      <el-button 
        v-if="currentStep === 4"
        type="primary" 
        @click="publishAd"
        :loading="publishing"
        class="footer-btn publish-btn"
      >
        发布广告
      </el-button>
    </div>

    <!-- 图片选择器 -->
    <SelectImage 
      v-model="formData.mediaUrl"
      ref="imageSelector"
      file-type="image"
      style="display: none;"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowLeft, 
  Trophy, 
  Position, 
  Picture, 
  VideoCamera,
  Plus,
  Delete
} from '@element-plus/icons-vue'
import SelectImage from '@/components/selectImage/selectImage.vue'
import { createAdWithCampaign } from '@/api/meta/ad'

defineOptions({
  name: 'AdWizard'
})

const router = useRouter()

// 当前步骤
const currentStep = ref(1)
const publishing = ref(false)

// 表单数据
const formData = ref({
  objective: '', // 'brand' | 'traffic'
  adTypeId: null,
  mediaUrl: '',
  name: '',
  title: '',
  description: '',
  clickUrl: '',
  bidAmount: '',
  totalBudget: '',
  dailyBudget: ''
})

// 广告类型选项
const adTypes = ref([
  { id: 1, name: '图片广告', icon: Picture },
  { id: 2, name: '视频广告', icon: VideoCamera }
])

// 图片选择器引用
const imageSelector = ref(null)

// 计算是否可以进行下一步
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 1:
      return formData.value.objective !== ''
    case 2:
      return formData.value.adTypeId && formData.value.mediaUrl
    case 3:
      return formData.value.name && formData.value.title && formData.value.description
    case 4:
      return formData.value.bidAmount && formData.value.totalBudget
    default:
      return false
  }
})

// 选择广告目标
const selectObjective = (objective) => {
  formData.value.objective = objective
}

// 选择广告类型
const selectAdType = (typeId) => {
  formData.value.adTypeId = typeId
}

// 选择图片
const selectImage = () => {
  // 触发图片选择器
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.onchange = (e) => {
    const file = e.target.files[0]
    if (file) {
      // 这里应该上传文件并获取URL
      // 暂时使用本地URL作为示例
      const url = URL.createObjectURL(file)
      formData.value.mediaUrl = url
    }
  }
  input.click()
}

// 移除图片
const removeImage = () => {
  formData.value.mediaUrl = ''
}

// 下一步
const nextStep = () => {
  if (canProceed.value && currentStep.value < 4) {
    currentStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

// 返回
const goBack = () => {
  router.go(-1)
}

// 发布广告
const publishAd = async () => {
  publishing.value = true
  try {
    // 准备API请求数据
    const requestData = {
      objective: formData.value.objective,
      ad_type_id: formData.value.adTypeId,
      media_url: formData.value.mediaUrl,
      name: formData.value.name,
      title: formData.value.title,
      description: formData.value.description,
      click_url: formData.value.clickUrl,
      bid_amount: parseFloat(formData.value.bidAmount),
      total_budget: parseFloat(formData.value.totalBudget),
      daily_budget: formData.value.dailyBudget ? parseFloat(formData.value.dailyBudget) : 0,
      ad_positions: [], // 使用默认广告位
      start_time: new Date().toISOString(),
      end_time: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天后
      max_impressions: 0,
      max_clicks: 0
    }

    const response = await createAdWithCampaign(requestData)

    if (response.code === 0) {
      ElMessage.success('广告发布成功！')
      router.push('/mobile/system?tab=ads')
    } else {
      ElMessage.error(response.msg || '发布失败，请重试')
    }
  } catch (error) {
    console.error('发布广告失败:', error)
    ElMessage.error('发布失败，请重试')
  } finally {
    publishing.value = false
  }
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.ad-wizard {
  background: #f5f7fa;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.wizard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
}

.back-btn {
  font-size: 20px;
  cursor: pointer;
  color: #409eff;
}

.wizard-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.step-indicator {
  font-size: 14px;
  color: #909399;
}

.progress-bar {
  height: 4px;
  background: #e4e7ed;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: #409eff;
  transition: width 0.3s ease;
}

.wizard-content {
  flex: 1;
  padding: 20px 16px;
}

.step-content {
  max-width: 100%;
}

.step-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  text-align: center;
}

.step-subtitle {
  font-size: 14px;
  color: #909399;
  text-align: center;
  margin-bottom: 32px;
}

.option-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.option-card {
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.option-card.active {
  border-color: #409eff;
  background: #f0f9ff;
}

.option-icon {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 12px;
}

.option-title {
  font-size: 18px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 8px;
}

.option-desc {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.option-type {
  font-size: 12px;
  color: #409eff;
  background: #e3f2fd;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
}

.ad-type-selector {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.type-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: white;
  border: 2px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.type-option.active {
  border-color: #409eff;
  background: #f0f9ff;
}

.type-option .el-icon {
  font-size: 24px;
  color: #409eff;
  margin-bottom: 8px;
}

.media-upload {
  background: white;
  border-radius: 12px;
  padding: 20px;
}

.upload-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 12px;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-placeholder {
  text-align: center;
  color: #909399;
}

.upload-placeholder .el-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.upload-tips {
  font-size: 12px;
  margin-top: 4px;
}

.uploaded-image {
  width: 100%;
  height: 120px;
  position: relative;
}

.uploaded-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.image-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.image-overlay .el-icon {
  color: white;
  font-size: 14px;
}

.form-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
}

.form-item {
  margin-bottom: 20px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-item label {
  display: block;
  font-size: 14px;
  color: #303133;
  margin-bottom: 8px;
}

.budget-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
}

.bid-input, .budget-input {
  display: flex;
  align-items: center;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.currency {
  background: #f5f7fa;
  padding: 0 12px;
  height: 32px;
  display: flex;
  align-items: center;
  color: #909399;
  border-right: 1px solid #dcdfe6;
}

.bid-input .el-input, .budget-input .el-input {
  flex: 1;
}

.bid-input :deep(.el-input__wrapper), .budget-input :deep(.el-input__wrapper) {
  border: none;
  box-shadow: none;
}

.bid-type {
  padding: 0 12px;
  color: #909399;
  font-size: 12px;
  background: #f5f7fa;
  border-left: 1px solid #dcdfe6;
}

.preview-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
}

.preview-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 12px;
}

.ad-preview {
  display: flex;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image {
  width: 80px;
  height: 80px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  color: #c0c4cc;
  font-size: 12px;
}

.preview-content {
  flex: 1;
  padding: 12px;
}

.preview-ad-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.preview-desc {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.preview-url {
  font-size: 11px;
  color: #409eff;
}

.wizard-footer {
  padding: 16px;
  background: white;
  border-top: 1px solid #e4e7ed;
  display: flex;
  gap: 12px;
}

.footer-btn {
  flex: 1;
  height: 44px;
}

.publish-btn {
  background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  border: none;
}
</style>
