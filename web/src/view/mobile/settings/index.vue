<template>
  <div class="mobile-settings mobile-content">
    <!-- 用户信息区域 -->
    <div class="user-section">
      <div class="user-info">
        <el-avatar :size="60" :src="userInfo.headerImg || ''" />
        <div class="user-details">
          <div class="username">{{ userInfo.nickName || userInfo.userName }}</div>
          <div class="user-role">{{ userInfo.authority?.authorityName || '普通用户' }}</div>
        </div>
      </div>
      <el-icon class="arrow-icon"><ArrowRight /></el-icon>
    </div>

    <!-- 设置选项 -->
    <div class="settings-sections">
      <!-- 账户设置 -->
      <div class="settings-section">
        <div class="section-title">账户设置</div>
        <div class="setting-item" @click="editProfile">
          <div class="item-left">
            <el-icon><User /></el-icon>
            <span>个人资料</span>
          </div>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>
        </div>
        <div class="setting-item" @click="changePassword">
          <div class="item-left">
            <el-icon><Lock /></el-icon>
            <span>修改密码</span>
          </div>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>
        </div>
        <div class="setting-item" @click="manageApiKeys">
          <div class="item-left">
            <el-icon><Key /></el-icon>
            <span>API密钥</span>
          </div>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>
        </div>
      </div>

      <!-- 应用设置 -->
      <div class="settings-section">
        <div class="section-title">应用设置</div>
        <div class="setting-item">
          <div class="item-left">
            <el-icon><Notification /></el-icon>
            <span>推送通知</span>
          </div>
          <el-switch v-model="settings.notifications" />
        </div>
        <div class="setting-item">
          <div class="item-left">
            <el-icon><Moon /></el-icon>
            <span>深色模式</span>
          </div>
          <el-switch v-model="settings.darkMode" @change="toggleDarkMode" />
        </div>
        <div class="setting-item" @click="selectLanguage">
          <div class="item-left">
            <el-icon><Service /></el-icon>
            <span>语言设置</span>
          </div>
          <div class="item-right">
            <span class="setting-value">{{ currentLanguage }}</span>
            <el-icon class="arrow-icon"><ArrowRight /></el-icon>
          </div>
        </div>
      </div>

      <!-- 数据与隐私 -->
      <div class="settings-section">
        <div class="section-title">数据与隐私</div>
        <div class="setting-item" @click="dataExport">
          <div class="item-left">
            <el-icon><Download /></el-icon>
            <span>数据导出</span>
          </div>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>
        </div>
        <div class="setting-item" @click="privacySettings">
          <div class="item-left">
            <el-icon><Lock /></el-icon>
            <span>隐私设置</span>
          </div>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>
        </div>
        <div class="setting-item" @click="clearCache">
          <div class="item-left">
            <el-icon><Delete /></el-icon>
            <span>清除缓存</span>
          </div>
          <span class="setting-value">{{ cacheSize }}</span>
        </div>
      </div>

      <!-- 帮助与支持 -->
      <div class="settings-section">
        <div class="section-title">帮助与支持</div>
        <div class="setting-item" @click="viewHelp">
          <div class="item-left">
            <el-icon><QuestionFilled /></el-icon>
            <span>帮助中心</span>
          </div>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>
        </div>
        <div class="setting-item" @click="contactSupport">
          <div class="item-left">
            <el-icon><Service /></el-icon>
            <span>联系客服</span>
          </div>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>
        </div>
        <div class="setting-item" @click="feedback">
          <div class="item-left">
            <el-icon><EditPen /></el-icon>
            <span>意见反馈</span>
          </div>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>
        </div>
        <div class="setting-item" @click="aboutApp">
          <div class="item-left">
            <el-icon><InfoFilled /></el-icon>
            <span>关于应用</span>
          </div>
          <div class="item-right">
            <span class="setting-value">v2.8.2</span>
            <el-icon class="arrow-icon"><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 退出登录 -->
    <div class="logout-section">
      <el-button type="danger" plain @click="logout" class="logout-btn">
        退出登录
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/pinia'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User,
  Lock,
  Key,
  Notification,
  Moon,
  Download,
  Delete,
  QuestionFilled,
  Service,
  EditPen,
  InfoFilled,
  ArrowRight
} from '@element-plus/icons-vue'

defineOptions({
  name: 'MobileSettings'
})

const router = useRouter()
const userStore = useUserStore()

// 用户信息
const userInfo = computed(() => userStore.userInfo)

// 设置状态
const settings = ref({
  notifications: true,
  darkMode: false
})

// 当前语言
const currentLanguage = ref('简体中文')

// 缓存大小
const cacheSize = ref('12.5MB')

// 设置方法
const editProfile = () => {
  router.push('/mobile/profile')
}

const changePassword = () => {
  router.push('/mobile/change-password')
}

const manageApiKeys = () => {
  router.push('/mobile/api-keys')
}

const toggleDarkMode = (value) => {
  // TODO: 实现深色模式切换
  ElMessage.success(value ? '已开启深色模式' : '已关闭深色模式')
}

const selectLanguage = () => {
  ElMessageBox.confirm('语言设置功能开发中', '提示', {
    confirmButtonText: '确定',
    showCancelButton: false,
    type: 'info'
  })
}

const dataExport = () => {
  ElMessageBox.confirm('确定要导出您的数据吗？', '数据导出', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    ElMessage.success('数据导出请求已提交')
  })
}

const privacySettings = () => {
  router.push('/mobile/privacy')
}

const clearCache = () => {
  ElMessageBox.confirm('清除缓存后需要重新加载数据，确定继续吗？', '清除缓存', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // TODO: 实现清除缓存逻辑
    cacheSize.value = '0MB'
    ElMessage.success('缓存已清除')
  })
}

const viewHelp = () => {
  router.push('/mobile/help')
}

const contactSupport = () => {
  ElMessage.info('客服功能开发中')
}

const feedback = () => {
  router.push('/mobile/feedback')
}

const aboutApp = () => {
  router.push('/mobile/about')
}

const logout = () => {
  ElMessageBox.confirm('确定要退出登录吗？', '退出登录', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logout()
    router.push('/login')
    ElMessage.success('已退出登录')
  })
}

// 初始化
onMounted(() => {
  // 加载用户设置
  // TODO: 从API获取用户设置
})
</script>

<style scoped>
.mobile-settings {
  background-color: #f5f7fa;
  min-height: 100vh;
  padding: 16px;
}

.user-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
}

.user-details {
  margin-left: 16px;
}

.username {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.user-role {
  font-size: 12px;
  color: #909399;
}

.arrow-icon {
  color: #c0c4cc;
}

.settings-sections {
  space-y: 16px;
}

.settings-section {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  padding: 16px 20px 8px;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  background: #f8f9fa;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-item:hover {
  background-color: #f8f9fa;
}

.item-left {
  display: flex;
  align-items: center;
}

.item-left .el-icon {
  margin-right: 12px;
  color: #409eff;
  font-size: 18px;
}

.item-left span {
  font-size: 14px;
  color: #303133;
}

.item-right {
  display: flex;
  align-items: center;
}

.setting-value {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
}

.logout-section {
  margin-top: 24px;
}

.logout-btn {
  width: 100%;
  height: 44px;
  font-size: 16px;
  border-radius: 8px;
}
</style>
