<template>
  <div class="mobile-system mobile-content">
    <!-- 顶部标签切换 -->
    <div class="tab-header">
      <div 
        v-for="tab in tabs" 
        :key="tab.key"
        class="tab-item"
        :class="{ active: activeTab === tab.key }"
        @click="switchTab(tab.key)"
      >
        {{ tab.label }}
      </div>
    </div>

    <!-- 广告列表 -->
    <div v-if="activeTab === 'ads'" class="content-section">
      <div class="section-header">
        <h3>我的广告</h3>
        <el-button type="primary" size="small" @click="createAd">
          <el-icon><Plus /></el-icon>
          新建
        </el-button>
      </div>
      
      <div class="ad-list">
        <div v-for="ad in adList" :key="ad.id" class="ad-card" @click="viewAdDetail(ad)">
          <div class="ad-media">
            <img v-if="ad.media_url" :src="ad.media_url" alt="广告图片" />
            <div v-else class="no-image">
              <el-icon><Picture /></el-icon>
            </div>
          </div>
          <div class="ad-info">
            <div class="ad-title">{{ ad.title || ad.name }}</div>
            <div class="ad-desc">{{ ad.description }}</div>
            <div class="ad-meta">
              <span class="ad-status" :class="getStatusClass(ad.status)">
                {{ getStatusText(ad.status) }}
              </span>
              <span class="ad-type">{{ getAdTypeText(ad.ad_type_id) }}</span>
            </div>
          </div>
          <div class="ad-actions">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 投放计划列表 -->
    <div v-if="activeTab === 'campaigns'" class="content-section">
      <div class="section-header">
        <h3>投放计划</h3>
        <el-button type="primary" size="small" @click="createCampaign">
          <el-icon><Plus /></el-icon>
          新建
        </el-button>
      </div>
      
      <div class="campaign-list">
        <div v-for="campaign in campaignList" :key="campaign.id" class="campaign-card" @click="viewCampaignDetail(campaign)">
          <div class="campaign-header">
            <div class="campaign-name">{{ campaign.name }}</div>
            <span class="campaign-status" :class="getCampaignStatusClass(campaign.status)">
              {{ getCampaignStatusText(campaign.status) }}
            </span>
          </div>
          <div class="campaign-info">
            <div class="info-row">
              <span class="label">计费方式:</span>
              <span class="value">{{ campaign.bid_type }}</span>
            </div>
            <div class="info-row">
              <span class="label">总预算:</span>
              <span class="value">¥{{ campaign.total_budget }}</span>
            </div>
            <div class="info-row">
              <span class="label">出价:</span>
              <span class="value">¥{{ campaign.bid_amount }}</span>
            </div>
          </div>
          <div class="campaign-actions">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据统计 -->
    <div v-if="activeTab === 'stats'" class="content-section">
      <div class="section-header">
        <h3>数据统计</h3>
        <el-button size="small" @click="refreshStats">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      
      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-title">今日展示</div>
          <div class="stat-value">{{ stats.todayImpressions }}</div>
          <div class="stat-change positive">+12.5%</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">今日点击</div>
          <div class="stat-value">{{ stats.todayClicks }}</div>
          <div class="stat-change positive">+8.3%</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">今日消费</div>
          <div class="stat-value">¥{{ stats.todaySpend }}</div>
          <div class="stat-change negative">-2.1%</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">点击率</div>
          <div class="stat-value">{{ stats.ctr }}%</div>
          <div class="stat-change positive">+0.5%</div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>加载中...</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { getAdList } from '@/api/meta/ad'
import { getCampaignList } from '@/api/meta/campaign'
import { 
  Plus, 
  Picture, 
  ArrowRight, 
  Refresh,
  Loading
} from '@element-plus/icons-vue'

defineOptions({
  name: 'MobileSystem'
})

const router = useRouter()
const route = useRoute()

// 标签页配置
const tabs = [
  { key: 'ads', label: '广告' },
  { key: 'campaigns', label: '计划' },
  { key: 'stats', label: '统计' }
]

// 当前激活的标签
const activeTab = ref('ads')
const loading = ref(false)

// 数据
const adList = ref([])
const campaignList = ref([])
const stats = ref({
  todayImpressions: 1234,
  todayClicks: 156,
  todaySpend: 89.5,
  ctr: 12.6
})

// 切换标签
const switchTab = (tabKey) => {
  activeTab.value = tabKey
  router.replace({ query: { ...route.query, tab: tabKey } })
  loadTabData()
}

// 加载标签数据
const loadTabData = async () => {
  loading.value = true
  try {
    if (activeTab.value === 'ads') {
      await loadAds()
    } else if (activeTab.value === 'campaigns') {
      await loadCampaigns()
    } else if (activeTab.value === 'stats') {
      await loadStats()
    }
  } finally {
    loading.value = false
  }
}

// 加载广告列表
const loadAds = async () => {
  try {
    const res = await getAdList({ page: 1, pageSize: 20 })
    if (res.code === 0) {
      adList.value = res.data.list || []
    }
  } catch (error) {
    console.error('加载广告列表失败:', error)
  }
}

// 加载投放计划列表
const loadCampaigns = async () => {
  try {
    const res = await getCampaignList({ page: 1, pageSize: 20 })
    if (res.code === 0) {
      campaignList.value = res.data.list || []
    }
  } catch (error) {
    console.error('加载投放计划列表失败:', error)
  }
}

// 加载统计数据
const loadStats = async () => {
  // TODO: 调用统计API
  console.log('加载统计数据')
}

// 状态相关方法
const getStatusClass = (status) => {
  const statusMap = {
    1: 'draft',
    2: 'pending', 
    3: 'approved',
    4: 'rejected',
    5: 'published',
    6: 'unpublished'
  }
  return statusMap[status] || 'draft'
}

const getStatusText = (status) => {
  const statusMap = {
    1: '草稿',
    2: '待审核',
    3: '已通过', 
    4: '已拒绝',
    5: '已上架',
    6: '已下架'
  }
  return statusMap[status] || '未知'
}

const getCampaignStatusClass = (status) => {
  return status
}

const getCampaignStatusText = (status) => {
  const statusMap = {
    'draft': '草稿',
    'pending': '待启动',
    'running': '投放中',
    'paused': '已暂停',
    'stopped': '已停止',
    'completed': '已完成'
  }
  return statusMap[status] || '未知'
}

const getAdTypeText = (typeId) => {
  // TODO: 从数据源获取类型名称
  return '图片广告'
}

// 操作方法
const createAd = () => {
  router.push('/mobile/ad-wizard')
}

const createCampaign = () => {
  router.push('/mobile/campaign/create')
}

const viewAdDetail = (ad) => {
  router.push(`/mobile/ad/${ad.ID}`)
}

const viewCampaignDetail = (campaign) => {
  router.push(`/mobile/campaign/${campaign.ID}`)
}

const refreshStats = () => {
  loadStats()
}

// 初始化
onMounted(() => {
  // 从URL参数获取初始标签
  const tabFromQuery = route.query.tab
  if (tabFromQuery && tabs.some(t => t.key === tabFromQuery)) {
    activeTab.value = tabFromQuery
  }
  loadTabData()
})

// 监听路由变化
watch(() => route.query.tab, (newTab) => {
  if (newTab && tabs.some(t => t.key === newTab)) {
    activeTab.value = newTab
    loadTabData()
  }
})
</script>

<style scoped>
.mobile-system {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.tab-header {
  display: flex;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 2px solid transparent;
}

.tab-item.active {
  color: #409eff;
  border-bottom-color: #409eff;
  background: #f0f9ff;
}

.content-section {
  padding: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.ad-list, .campaign-list {
  space-y: 12px;
}

.ad-card, .campaign-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ad-card:hover, .campaign-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.ad-media {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  overflow: hidden;
  margin-right: 12px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ad-media img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  color: #c0c4cc;
  font-size: 24px;
}

.ad-info {
  flex: 1;
}

.ad-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.ad-desc {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ad-meta {
  display: flex;
  gap: 8px;
}

.ad-status, .campaign-status {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  color: white;
}

.ad-status.draft, .campaign-status.draft { background: #909399; }
.ad-status.pending, .campaign-status.pending { background: #e6a23c; }
.ad-status.approved, .campaign-status.running { background: #67c23a; }
.ad-status.rejected, .campaign-status.stopped { background: #f56c6c; }
.ad-status.published, .campaign-status.completed { background: #409eff; }

.ad-type {
  font-size: 11px;
  color: #909399;
}

.ad-actions, .campaign-actions {
  color: #c0c4cc;
}

.campaign-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 8px;
}

.campaign-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.campaign-info {
  width: 100%;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 12px;
}

.info-row .label {
  color: #909399;
}

.info-row .value {
  color: #303133;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stat-card {
  background: white;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-title {
  font-size: 12px;
  color: #909399;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 11px;
}

.stat-change.positive {
  color: #67c23a;
}

.stat-change.negative {
  color: #f56c6c;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #909399;
}

.loading-container .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}
</style>
