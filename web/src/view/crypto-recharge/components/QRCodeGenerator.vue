<template>
  <div class="qr-code-generator">
    <div class="qr-code-container" ref="qrCodeRef">
      <div v-if="!qrCodeGenerated" class="qr-placeholder">
        <el-icon size="48"><Picture /></el-icon>
        <p>生成二维码中...</p>
      </div>
    </div>
    <div class="qr-actions" v-if="qrCodeGenerated">
      <el-button size="small" @click="downloadQR">
        <el-icon><Download /></el-icon>
        下载二维码
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { Picture, Download } from '@element-plus/icons-vue'
import QRCode from 'qrcode'

const props = defineProps({
  text: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    default: 160
  }
})

const qrCodeRef = ref(null)
const qrCodeGenerated = ref(false)

const generateQRCode = async () => {
  if (!props.text || !qrCodeRef.value) return
  
  try {
    qrCodeGenerated.value = false
    
    // 清空容器
    qrCodeRef.value.innerHTML = ''
    
    // 生成二维码
    const canvas = document.createElement('canvas')
    await QRCode.toCanvas(canvas, props.text, {
      width: props.size,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
    
    // 添加到容器
    qrCodeRef.value.appendChild(canvas)
    qrCodeGenerated.value = true
  } catch (error) {
    console.error('生成二维码失败:', error)
  }
}

const downloadQR = () => {
  const canvas = qrCodeRef.value?.querySelector('canvas')
  if (canvas) {
    const link = document.createElement('a')
    link.download = 'wallet-qrcode.png'
    link.href = canvas.toDataURL()
    link.click()
  }
}

watch(() => props.text, generateQRCode, { immediate: true })

onMounted(() => {
  generateQRCode()
})
</script>

<style lang="scss" scoped>
.qr-code-generator {
  text-align: center;

  .qr-code-container {
    width: 160px;
    height: 160px;
    margin: 0 auto 12px;
    border: 2px dashed var(--art-border-color);
    border-radius: var(--art-border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--art-gray-100);
    overflow: hidden;

    canvas {
      border-radius: var(--art-border-radius-sm);
    }

    .qr-placeholder {
      text-align: center;
      color: var(--art-text-gray-500);

      .el-icon {
        margin-bottom: 8px;
      }

      p {
        margin: 0;
        font-size: 0.85rem;
      }
    }
  }

  .qr-actions {
    margin-top: 12px;

    .el-button {
      border-radius: var(--art-border-radius);
    }
  }
}

// 暗色主题适配
:deep(.dark) {
  .qr-code-container {
    background: var(--art-gray-200) !important;
  }
}
</style>
