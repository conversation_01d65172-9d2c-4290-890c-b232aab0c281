<template>
  <div class="recharge-status">
    <el-card class="status-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>充值状态</h3>
          <el-tag :type="getStatusType(status)" size="small">
            {{ getStatusText(status) }}
          </el-tag>
        </div>
      </template>

      <!-- 状态步骤 -->
      <div class="status-steps">
        <el-steps :active="currentStep" finish-status="success" align-center>
          <el-step title="发起充值" :icon="Wallet">
            <template #description>
              <span class="step-desc">选择币种和金额</span>
            </template>
          </el-step>
          <el-step title="等待转账" :icon="Clock">
            <template #description>
              <span class="step-desc">向钱包地址转账</span>
            </template>
          </el-step>
          <el-step title="网络确认" :icon="Loading">
            <template #description>
              <span class="step-desc">区块链网络确认中</span>
            </template>
          </el-step>
          <el-step title="充值完成" :icon="CircleCheck">
            <template #description>
              <span class="step-desc">资金已到账</span>
            </template>
          </el-step>
        </el-steps>
      </div>

      <!-- 详细信息 -->
      <div class="status-details" v-if="rechargeInfo">
        <div class="detail-row">
          <span class="label">订单号：</span>
          <span class="value">{{ rechargeInfo.orderId }}</span>
        </div>
        <div class="detail-row">
          <span class="label">充值金额：</span>
          <span class="value">{{ rechargeInfo.amount }} {{ rechargeInfo.crypto }}</span>
        </div>
        <div class="detail-row">
          <span class="label">网络确认：</span>
          <span class="value">{{ rechargeInfo.confirmations }}/{{ rechargeInfo.requiredConfirmations }}</span>
        </div>
        <div class="detail-row" v-if="rechargeInfo.txHash">
          <span class="label">交易哈希：</span>
          <el-button 
            link 
            type="primary" 
            size="small"
            @click="viewTransaction(rechargeInfo.txHash)"
          >
            {{ rechargeInfo.txHash.substring(0, 20) }}...
          </el-button>
        </div>
        <div class="detail-row" v-if="rechargeInfo.estimatedTime">
          <span class="label">预计到账：</span>
          <span class="value">{{ rechargeInfo.estimatedTime }}</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="status-actions">
        <el-button 
          v-if="status === 'pending'" 
          type="primary" 
          @click="$emit('confirm')"
        >
          我已完成转账
        </el-button>
        <el-button 
          v-if="status === 'confirming'" 
          @click="$emit('refresh')"
        >
          <el-icon><Refresh /></el-icon>
          刷新状态
        </el-button>
        <el-button 
          v-if="status === 'completed'" 
          type="success" 
          @click="$emit('new-recharge')"
        >
          继续充值
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { 
  Wallet, 
  Clock, 
  Loading, 
  CircleCheck, 
  Refresh 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  status: {
    type: String,
    default: 'pending', // pending, confirming, completed, failed
    validator: (value) => ['pending', 'confirming', 'completed', 'failed'].includes(value)
  },
  rechargeInfo: {
    type: Object,
    default: null
  }
})

defineEmits(['confirm', 'refresh', 'new-recharge'])

const currentStep = computed(() => {
  const stepMap = {
    'pending': 1,
    'confirming': 2,
    'completed': 3,
    'failed': 1
  }
  return stepMap[props.status] || 0
})

const getStatusType = (status) => {
  const typeMap = {
    'pending': 'warning',
    'confirming': 'primary',
    'completed': 'success',
    'failed': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'pending': '等待转账',
    'confirming': '确认中',
    'completed': '已完成',
    'failed': '失败'
  }
  return textMap[status] || '未知'
}

const viewTransaction = (txHash) => {
  // 这里可以打开区块链浏览器查看交易
  ElMessage.info(`查看交易：${txHash}`)
}
</script>

<style lang="scss" scoped>
.recharge-status {
  .status-card {
    border: 1px solid var(--art-border-color);
    box-shadow: var(--art-box-shadow-sm);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--art-text-gray-900);
      }
    }

    :deep(.el-card__body) {
      padding: 24px;
    }
  }

  .status-steps {
    margin-bottom: 24px;

    :deep(.el-steps) {
      .el-step__title {
        font-size: 0.9rem;
        font-weight: 500;
      }

      .step-desc {
        font-size: 0.8rem;
        color: var(--art-text-gray-600);
      }

      .el-step__icon {
        width: 32px;
        height: 32px;
      }
    }
  }

  .status-details {
    background: var(--art-gray-100);
    border-radius: var(--art-border-radius);
    padding: 16px;
    margin-bottom: 20px;

    .detail-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-size: 0.9rem;
        color: var(--art-text-gray-600);
        font-weight: 500;
      }

      .value {
        font-size: 0.9rem;
        color: var(--art-text-gray-900);
        font-weight: 600;
      }
    }
  }

  .status-actions {
    text-align: center;

    .el-button {
      border-radius: var(--art-border-radius);
      min-width: 120px;

      &:not(:last-child) {
        margin-right: 12px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .status-steps {
      :deep(.el-steps) {
        .el-step__title {
          font-size: 0.8rem;
        }

        .step-desc {
          font-size: 0.75rem;
        }

        .el-step__icon {
          width: 28px;
          height: 28px;
        }
      }
    }

    .status-details {
      .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
      }
    }

    .status-actions {
      .el-button {
        width: 100%;
        margin-right: 0;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 暗色主题适配
:deep(.dark) {
  .status-details {
    background: var(--art-gray-200) !important;
  }
}
</style>
