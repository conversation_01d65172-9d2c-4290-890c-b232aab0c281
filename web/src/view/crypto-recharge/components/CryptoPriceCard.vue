<template>
  <div 
    class="crypto-price-card"
    :class="{ active: isSelected }"
    @click="$emit('select', crypto)"
  >
    <div class="crypto-icon">
      <img :src="crypto.icon" :alt="crypto.name" />
    </div>
    <div class="crypto-info">
      <div class="crypto-name">{{ crypto.name }}</div>
      <div class="crypto-symbol">{{ crypto.symbol }}</div>
    </div>
    <div class="crypto-price">
      <div class="price">¥{{ crypto.price.toLocaleString() }}</div>
      <div class="change" :class="crypto.change >= 0 ? 'positive' : 'negative'">
        <el-icon v-if="crypto.change >= 0"><ArrowUp /></el-icon>
        <el-icon v-else><ArrowUp /></el-icon>
        {{ crypto.change >= 0 ? '+' : '' }}{{ crypto.change }}%
      </div>
    </div>
  </div>
</template>

<script setup>
import { ArrowUp, Check } from '@element-plus/icons-vue'

defineProps({
  crypto: {
    type: Object,
    required: true
  },
  isSelected: {
    type: Boolean,
    default: false
  }
})

defineEmits(['select'])
</script>

<style lang="scss" scoped>
.crypto-price-card {
  background: var(--art-main-bg-color);
  border: 2px solid var(--art-border-color);
  border-radius: var(--art-border-radius-lg);
  padding: 20px;
  cursor: pointer;
  transition: all var(--art-transition-duration) var(--art-transition-timing);
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: var(--art-box-shadow-xs);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, rgb(var(--art-primary)), rgb(var(--art-secondary)));
    transform: scaleX(0);
    transition: transform var(--art-transition-duration) var(--art-transition-timing);
  }

  &:hover {
    border-color: rgb(var(--art-primary));
    box-shadow: var(--art-box-shadow-sm);
    transform: translateY(-2px);

    &::before {
      transform: scaleX(1);
    }
  }

  &.active {
    border-color: rgb(var(--art-primary));
    background: rgb(var(--art-bg-primary));
    box-shadow: var(--art-box-shadow);

    &::before {
      transform: scaleX(1);
    }
  }

  .crypto-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--art-gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: var(--art-box-shadow-xs);

    img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }
  }

  .crypto-info {
    flex: 1;

    .crypto-name {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--art-text-gray-900);
      margin-bottom: 4px;
    }

    .crypto-symbol {
      font-size: 0.9rem;
      color: var(--art-text-gray-600);
      font-weight: 500;
    }
  }

  .crypto-price {
    text-align: right;

    .price {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--art-text-gray-900);
      margin-bottom: 4px;
    }

    .change {
      font-size: 0.85rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 4px;

      &.positive {
        color: rgb(var(--art-success));

        .el-icon {
          transform: rotate(0deg);
        }
      }

      &.negative {
        color: rgb(var(--art-error));

        .el-icon {
          transform: rotate(180deg);
        }
      }

      .el-icon {
        font-size: 14px;
        transition: transform var(--art-transition-duration);
      }
    }
  }

  .selection-indicator {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    background: rgb(var(--art-primary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    animation: checkmark 0.3s ease-in-out;
  }

  @keyframes checkmark {
    0% {
      transform: scale(0);
      opacity: 0;
    }
    50% {
      transform: scale(1.2);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;

    .crypto-icon {
      width: 40px;
      height: 40px;

      img {
        width: 28px;
        height: 28px;
      }
    }

    .crypto-info {
      .crypto-name {
        font-size: 1rem;
      }

      .crypto-symbol {
        font-size: 0.85rem;
      }
    }

    .crypto-price {
      .price {
        font-size: 1rem;
      }

      .change {
        font-size: 0.8rem;
      }
    }
  }
}

// 暗色主题适配
:deep(.dark) {
  .crypto-price-card {
    &.active {
      background: rgba(var(--art-primary), 0.1);
    }

    .crypto-icon {
      background: var(--art-gray-200) !important;
    }
  }
}
</style>
