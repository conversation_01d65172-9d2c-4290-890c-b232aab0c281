<template>
  <div class="art-layouts" :style="layoutStyle">
    <!-- 水印效果 -->
    <el-watermark
      v-if="config.show_watermark"
      :font="font"
      :z-index="9999"
      :gap="[180, 150]"
      class="absolute inset-0 pointer-events-none"
      :content="userStore.userInfo.nickName"
    />

    <!-- 移动端抽屉导航 -->
    <gva-drawer v-if="device === 'mobile'" />

    <!-- 桌面端侧边栏 - 固定在左侧 -->
    <gva-aside
      v-if="
        device !== 'mobile' && (
          config.side_mode === 'normal' ||
          config.side_mode === 'sidebar'
        )
      "
      class="layout-sidebar"
    />
    <gva-aside
      v-if="config.side_mode === 'combination' && device !== 'mobile'"
      mode="normal"
      class="layout-sidebar"
    />

    <!-- 右侧内容区域 -->
    <div class="art-right-container">
      <!-- 头部导航栏 -->
      <gva-header />

      <!-- 页面内容区域 -->
      <div class="art-page-content">
        <gva-tabs v-if="config.showTabs" />
        <div class="art-content-wrapper" :class="{ 'with-tabs': config.showTabs }">
          <router-view v-if="reloadFlag" v-slot="{ Component, route }">
            <div id="gva-base-load-dom" class="art-page-container">
              <transition
                mode="out-in"
                :name="route.meta.transitionType || config.transition_type"
                appear
              >
                <keep-alive :include="routerStore.keepAliveRouters">
                  <component :is="Component" :key="route.fullPath" />
                </keep-alive>
              </transition>
            </div>
          </router-view>
          <BottomInfo />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import GvaAside from '@/view/layout/aside/index.vue'
  import GvaHeader from '@/view/layout/header/index.vue'
  import GvaDrawer from '@/view/layout/aside/drawerMode.vue'
  import useResponsive from '@/hooks/responsive'
  import GvaTabs from './tabs/index.vue'
  import BottomInfo from '@/components/bottomInfo/bottomInfo.vue'
  import { emitter } from '@/utils/bus.js'
  import { ref, onMounted, nextTick, reactive, watchEffect, computed, onUnmounted } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { useRouterStore } from '@/pinia/modules/router'
  import { useUserStore } from '@/pinia/modules/user'
  import { useAppStore } from '@/pinia'
  import { storeToRefs } from 'pinia'
  import '@/style/transition.scss'

  const appStore = useAppStore()
  const { config, isDark, device } = storeToRefs(appStore)

  defineOptions({
    name: 'GvaLayout'
  })

  useResponsive(true)

  const font = reactive({
    color: 'rgba(0, 0, 0, .15)'
  })

  watchEffect(() => {
    font.color = isDark.value ? 'rgba(255,255,255, .15)' : 'rgba(0, 0, 0, .15)'
  })

  // 动态侧边栏宽度
  const currentSidebarWidth = ref(config.value.layout_side_width || 240) // 默认宽度

  // 布局样式计算
  const layoutStyle = computed(() => {
    return {
      paddingLeft: getPaddingLeft(),
    }
  })

  // 获取左侧内边距
  const getPaddingLeft = () => {
    if (device.value === 'mobile') return '0px'

    switch (config.value.side_mode) {
      case 'normal':
      case 'sidebar':
        return `${currentSidebarWidth.value}px`
      case 'combination':
        return 'calc(var(--art-sidebar-width) + var(--art-sidebar-collapsed-width))'
      case 'head':
        return '0px'
      default:
        return `${currentSidebarWidth.value}px`
    }
  }

  // 监听侧边栏宽度变化
  const handleSidebarWidthChange = (width) => {
    currentSidebarWidth.value = width
  }



  const router = useRouter()
  const route = useRoute()
  const routerStore = useRouterStore()

  onMounted(() => {
    // 挂载一些通用的事件
    emitter.on('reload', reload)
    emitter.on('sidebarWidthChange', handleSidebarWidthChange)
    if (userStore.loadingInstance) {
      userStore.loadingInstance.close()
    }
  })

  onUnmounted(() => {
    emitter.off('reload', reload)
    emitter.off('sidebarWidthChange', handleSidebarWidthChange)
  })

  const userStore = useUserStore()

  const reloadFlag = ref(true)
  let reloadTimer = null
  const reload = async () => {
    if (reloadTimer) {
      window.clearTimeout(reloadTimer)
    }
    reloadTimer = window.setTimeout(async () => {
      if (route.meta.keepAlive) {
        reloadFlag.value = false
        await nextTick()
        reloadFlag.value = true
      } else {
        const title = route.meta.title
        router.push({ name: 'Reload', params: { title } })
      }
    }, 400)
  }
</script>

<style lang="scss" scoped>
@use '@/style/variables.scss' as *;
@use '@/style/mixin.scss' as *;

.art-layouts {
  width: 100vw;
  height: 100vh;
  background-color: var(--art-bg-color);
  color: var(--art-text-gray-700);
  overflow: hidden;
  transition: all var(--art-transition-duration) var(--art-transition-timing);
}

// 左侧侧边栏
.layout-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  height: 100vh;
}

// 右侧内容区域
.art-right-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.art-page-content {
  flex: 1;
  width: 100%;
  height: calc(100vh - var(--art-header-height));
  padding: 0 0.5rem;
  overflow: hidden;
}

.art-content-wrapper {
  height: 100%;
  overflow: auto;

  &.with-tabs {
    height: calc(100% - 2.5rem);
  }

  &:not(.with-tabs) {
    padding-top: 0.25rem;
  }
}

.art-page-container {
  min-height: calc(100% - 3rem);
  background-color: var(--art-bg-color);

  // 页面切换动画优化
  &.fade-enter-active,
  &.fade-leave-active {
    transition: all var(--art-transition-duration) var(--art-transition-timing);
  }

  &.fade-enter-from {
    opacity: 0;
    transform: translateX(10px);
  }

  &.fade-leave-to {
    opacity: 0;
    transform: translateX(-10px);
  }
}

// 响应式适配
@media (max-width: 500px) {
  .art-layouts {
    padding-left: 0 !important;
  }

  .layout-sidebar {
    display: none;
  }

  .art-page-content {
    padding: 0 0.25rem;
  }
}

// 暗色主题适配
.dark {
  .art-layouts {
    background-color: var(--art-bg-color);
    color: var(--art-text-gray-700);
  }

  .art-page-container {
    background-color: var(--art-bg-color);
  }
}
</style>
