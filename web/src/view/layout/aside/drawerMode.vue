<template>
  <el-drawer
    v-model="appStore.drawerVisible"
    direction="ltr"
    :size="280"
    :with-header="false"
    :modal="true"
    :modal-class="'drawer-modal'"
    class="mobile-drawer"
  >
    <div class="h-full bg-white dark:bg-slate-900 flex flex-col">
      <!-- 头部 Logo 区域 -->
      <div class="flex items-center p-4 border-b border-gray-200 dark:border-gray-700">
        <img
          alt="logo"
          class="h-10 bg-white rounded-full mr-3"
          :src="$GIN_VUE_ADMIN.appLogo"
        />
        <div class="font-bold text-lg text-slate-700 dark:text-slate-300">
          {{ $GIN_VUE_ADMIN.appName }}
        </div>
      </div>

      <!-- 导航菜单区域 -->
      <div class="flex-1 overflow-hidden">
        <el-scrollbar class="h-full">
          <div class="p-2">
            <!-- 根据不同的侧边栏模式显示不同的菜单 -->
            <template v-if="config.side_mode === 'combination'">
              <!-- 组合模式：显示左侧菜单 -->
              <el-menu
                :default-active="active"
                class="border-r-0 w-full"
                unique-opened
                @select="selectMenuItem"
              >
                <template v-for="item in routerStore.leftMenu">
                  <aside-component
                    v-if="!item.hidden"
                    :key="item.name"
                    :router-info="item"
                  />
                </template>
              </el-menu>
            </template>
            <template v-else>
              <!-- 普通模式、头部模式、侧边栏模式：显示所有菜单 -->
              <el-menu
                :default-active="active"
                class="border-r-0 w-full"
                unique-opened
                @select="selectMenuItem"
              >
                <template v-for="item in routerStore.asyncRouters[0]?.children || []">
                  <aside-component
                    v-if="!item.hidden"
                    :key="item.name"
                    :router-info="item"
                  />
                </template>
              </el-menu>
            </template>
          </div>
        </el-scrollbar>
      </div>

      <!-- 底部关闭按钮 -->
      <div class="p-4 border-t border-gray-200 dark:border-gray-700">
        <el-button
          type="primary"
          class="w-full"
          @click="appStore.toggleDrawer(false)"
        >
          关闭菜单
        </el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
  import { computed, watchEffect } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useRouterStore } from '@/pinia/modules/router'
  import { useAppStore } from '@/pinia'
  import { storeToRefs } from 'pinia'
  import AsideComponent from './asideComponent/index.vue'

  defineOptions({
    name: 'DrawerMode'
  })

  const route = useRoute()
  const router = useRouter()
  const routerStore = useRouterStore()
  const appStore = useAppStore()
  const { config } = storeToRefs(appStore)

  const active = computed(() => {
    if (route.name === 'Iframe') {
      return decodeURIComponent(route.query.url)
    }
    return route.meta.activeName || route.name
  })

  const selectMenuItem = (index) => {
    // 选择菜单项后关闭抽屉
    appStore.toggleDrawer(false)

    if (index.indexOf('http') > -1) {
      window.open(index)
    } else {
      router.push({ name: index })
    }
  }

  // 监听路由变化，自动关闭抽屉
  watchEffect(() => {
    if (route.path) {
      appStore.toggleDrawer(false)
    }
  })
</script>

<style lang="scss" scoped>
  .mobile-drawer {
    :deep(.el-drawer__body) {
      padding: 0;
    }
  }

  // 自定义抽屉遮罩样式
  :deep(.drawer-modal) {
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
  }

  // 确保菜单项在抽屉中正确显示
  :deep(.el-menu) {
    background-color: transparent;

    .el-menu-item,
    .el-sub-menu__title {
      color: inherit;

      &:hover {
        background-color: rgba(59, 130, 246, 0.1);
      }

      &.is-active {
        background-color: rgba(59, 130, 246, 0.15);
        color: #3b82f6;
      }
    }

    .el-sub-menu .el-menu {
      background-color: rgba(0, 0, 0, 0.02);
    }
  }

  // 暗色模式下的样式调整
  .dark {
    :deep(.el-menu) {
      .el-menu-item,
      .el-sub-menu__title {
        &:hover {
          background-color: rgba(59, 130, 246, 0.2);
        }

        &.is-active {
          background-color: rgba(59, 130, 246, 0.25);
          color: #60a5fa;
        }
      }

      .el-sub-menu .el-menu {
        background-color: rgba(255, 255, 255, 0.02);
      }
    }
  }
</style>
