<template>
  <div class="art-sidebar" :class="sidebarClasses" :style="sidebarStyle">
    <!-- 头部 Logo 区域 -->
    <div class="sidebar-header" @click="toHome">
      <img class="logo" :src="$GIN_VUE_ADMIN.appLogo" alt="logo" />
      <p class="system-name" :class="{ 'collapsed': isCollapse }">
        {{ $GIN_VUE_ADMIN.appName }}
      </p>
    </div>

    <!-- 菜单区域 -->
    <div class="sidebar-menu">
      <el-scrollbar class="menu-scrollbar">
        <el-menu
          :class="menuClasses"
          :collapse="isCollapse"
          :collapse-transition="false"
          :default-active="active"
          :text-color="menuTheme.textColor"
          :unique-opened="true"
          :background-color="menuTheme.background"
          :active-text-color="menuTheme.textActiveColor"
          @select="selectMenuItem"
        >
          <template v-for="item in routerStore.asyncRouters[0]?.children || []">
            <aside-component
              v-if="!item.hidden"
              :key="item.name"
              :router-info="item"
            />
          </template>
        </el-menu>
      </el-scrollbar>
    </div>

    <!-- 折叠按钮 -->
    <!-- <div class="sidebar-toggle" @click="toggleCollapse">
      <el-icon class="toggle-icon">
        <component :is="isCollapse ? 'DArrowRight' : 'DArrowLeft'" />
      </el-icon>
    </div> -->
  </div>
</template>

<script setup>
  import AsideComponent from '@/view/layout/aside/asideComponent/index.vue'
  import { ref, provide, watchEffect, computed, onMounted, onUnmounted } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useRouterStore } from '@/pinia/modules/router'
  import { useAppStore } from '@/pinia'
  import { storeToRefs } from 'pinia'
  import { DArrowLeft, DArrowRight } from '@element-plus/icons-vue'
  import { emitter } from '@/utils/bus.js'

  const appStore = useAppStore()
  const { device, config, isDark } = storeToRefs(appStore)

  defineOptions({
    name: 'GvaAside'
  })

  const route = useRoute()
  const router = useRouter()
  const routerStore = useRouterStore()
  const isCollapse = ref(false)
  const active = ref('')

  // 侧边栏宽度计算
  const layoutSideWidth = computed(() => {
    if (!isCollapse.value) {
      return config.value.layout_side_width || 240
    } else {
      return config.value.layout_side_collapsed_width || 64
    }
  })

  // 侧边栏样式类
  const sidebarClasses = computed(() => ({
    'collapsed': isCollapse.value,
    'dark': isDark.value
  }))

  // 侧边栏样式
  const sidebarStyle = computed(() => ({
    width: layoutSideWidth.value + 'px'
  }))

  // 菜单样式类
  const menuClasses = computed(() => [
    'art-menu',
    `art-menu-${isDark.value ? 'dark' : 'light'}`,
    { 'collapsed': isCollapse.value }
  ])

  // 菜单主题
  const menuTheme = computed(() => ({
    background: isDark.value ? '#1b1c22' : '#ffffff',
    textColor: isDark.value ? '#9a9cae' : '#4b5675',
    textActiveColor: 'var(--main-color)'
  }))

  watchEffect(() => {
    if (route.name === 'Iframe') {
      active.value = decodeURIComponent(route.query.url)
      return
    }
    active.value = route.meta.activeName || route.name
  })

  watchEffect(() => {
    if (device.value === 'mobile') {
      isCollapse.value = true
    } else {
      isCollapse.value = false
    }
  })

  provide('isCollapse', isCollapse)

  const toHome = () => {
    router.push({ path: '/' })
  }

  const selectMenuItem = (index) => {
    const query = {}
    const params = {}
    routerStore.routeMap[index]?.parameters &&
      routerStore.routeMap[index]?.parameters.forEach((item) => {
        if (item.type === 'query') {
          query[item.key] = item.value
        } else {
          params[item.key] = item.value
        }
      })
    if (index === route.name) return
    if (index.indexOf('http://') > -1 || index.indexOf('https://') > -1) {
      if (index === 'Iframe') {
        query.url = decodeURIComponent(index)
        router.push({
          name: 'Iframe',
          query,
          params
        })
        return
      } else {
        window.open(index, '_blank')
        return
      }
    } else {
      router.push({ name: index, query, params })
    }
  }

  const toggleCollapse = () => {
    isCollapse.value = !isCollapse.value
    // 触发布局更新事件
    emitter.emit('sidebarWidthChange', layoutSideWidth.value)
    emitter.emit('sidebarStateChange', isCollapse.value)
  }

  // 监听头部切换事件
  const handleToggleSidebar = (collapsed) => {
    isCollapse.value = collapsed
    emitter.emit('sidebarWidthChange', layoutSideWidth.value)
  }

  onMounted(() => {
    emitter.on('toggleSidebar', handleToggleSidebar)
    // 初始化时发送当前宽度
    emitter.emit('sidebarWidthChange', layoutSideWidth.value)
  })

  onUnmounted(() => {
    emitter.off('toggleSidebar', handleToggleSidebar)
  })

  // 监听宽度变化
  watchEffect(() => {
    emitter.emit('sidebarWidthChange', layoutSideWidth.value)
  })
</script>

<style lang="scss" scoped>
.art-sidebar {
  position: relative;
  height: 100vh;
  background-color: var(--art-main-bg-color);
  border-right: 1px solid var(--art-border-color);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;

  .sidebar-header {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    height: 60px;
    padding: 0 1rem;
    cursor: pointer;
    border-bottom: 1px solid var(--art-border-color);
    transition: all 0.3s ease;

    &:hover {
      background-color: rgba(242, 246, 250, 0.5);
    }

    .logo {
      width: 2rem;
      height: 2rem;
      border-radius: 50%;
      background: white;
      flex-shrink: 0;
    }

    .system-name {
      margin: 0 0 0 0.75rem;
      font-size: 1.125rem;
      font-weight: 600;
      color: #4b5675;
      transition: all 0.3s ease;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      &.collapsed {
        opacity: 0;
        transform: translateX(-10px);
      }
    }
  }

  .sidebar-menu {
    flex: 1;
    overflow: hidden;

    .menu-scrollbar {
      height: 100%;

      :deep(.el-scrollbar__view) {
        height: 100%;
      }
    }

    .art-menu {
      height: 100%;
      border-right: none;
      background-color: transparent;

      &.collapsed {
        :deep(.el-menu-item),
        :deep(.el-submenu__title) {
          padding: 0 calc((64px - 24px) / 2) !important;
        }

        :deep(.el-submenu__title .el-submenu__icon-arrow) {
          display: none;
        }

        :deep(.el-menu-item-group__title) {
          padding: 0;
        }
      }

      :deep(.el-menu-item) {
        height: 3rem;
        line-height: 3rem;
        margin: 0.25rem 0.5rem;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          background-color: rgba(242, 246, 250, 0.7) !important;
        }

        &.is-active {
          background-color: rgba(93, 135, 255, 0.1) !important;
          color: rgb(93, 135, 255) !important;
          font-weight: 500;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 1.5rem;
            background-color: rgb(93, 135, 255);
            border-radius: 0 2px 2px 0;
          }
        }

        .el-icon {
          margin-right: 0.5rem;
          font-size: 1.125rem;
        }
      }

      :deep(.el-submenu) {
        .el-submenu__title {
          height: 3rem;
          line-height: 3rem;
          margin: 0.25rem 0.5rem;
          border-radius: 4px;
          transition: all 0.3s ease;

          &:hover {
            background-color: rgba(242, 246, 250, 0.7) !important;
          }

          .el-icon {
            margin-right: 0.5rem;
            font-size: 1.125rem;
          }
        }

        .el-menu {
          background-color: transparent;

          .el-menu-item {
            margin-left: 1rem;
            margin-right: 0.5rem;
            padding-left: 2.5rem !important;

            &::before {
              left: 1rem;
            }
          }
        }
      }
    }
  }

  .sidebar-toggle {
    position: absolute;
    bottom: 2rem;
    right: 0.5rem;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 2rem;
    height: 2rem;
    background-color: #f9f9f9;
    border: 1px solid #eaebf1;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;

    &:hover {
      background-color: #f1f1f4;
      transform: scale(1.1);
    }

    .toggle-icon {
      font-size: 0.875rem;
      color: #78829d;
      transition: all 0.3s ease;
    }
  }

  &.collapsed {
    .sidebar-toggle {
      right: 0;
      left: 0;
      margin: 0 auto;
    }
  }
}

// 暗色主题适配
:global(.dark) {
  .art-sidebar {
    background-color: #1b1c22 !important;
    border-right-color: #26272f !important;

    .sidebar-header {
      border-bottom-color: #26272f !important;

      &:hover {
        background-color: rgba(51, 63, 85, 0.5) !important;
      }

      .system-name {
        color: #9a9cae !important;
      }
    }

    .art-menu {
      background-color: transparent !important;

      :deep(.el-menu-item) {
        color: #9a9cae !important;

        &:hover {
          background-color: rgba(51, 63, 85, 0.7) !important;
          color: #b5b7c8 !important;
        }

        &.is-active {
          background-color: rgba(93, 135, 255, 0.1) !important;
          color: rgb(93, 135, 255) !important;
        }
      }

      :deep(.el-submenu__title) {
        color: #9a9cae !important;

        &:hover {
          background-color: rgba(51, 63, 85, 0.7) !important;
          color: #b5b7c8 !important;
        }
      }

      :deep(.el-menu) {
        .el-menu-item {
          color: #808290 !important;

          &:hover {
            background-color: rgba(51, 63, 85, 0.7) !important;
            color: #b5b7c8 !important;
          }

          &.is-active {
            background-color: rgba(93, 135, 255, 0.1) !important;
            color: rgb(93, 135, 255) !important;
          }
        }
      }
    }

    .sidebar-toggle {
      background-color: #26272f !important;
      border-color: #363843 !important;

      &:hover {
        background-color: #363843 !important;
      }

      .toggle-icon {
        color: #808290 !important;
      }
    }
  }
}
</style>
