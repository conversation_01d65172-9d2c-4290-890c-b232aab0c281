<!--
    @auther: bypanghu<<EMAIL>>
    @date: 2024/5/7
!-->

<template>
  <div class="art-header-bar">
    <div class="art-header-menu">
      <div class="art-header-left">
        <!-- 系统信息 -->
        <div class="art-header-logo" @click="toHome" v-if="isTopMenu">
          <img class="logo" :src="$GIN_VUE_ADMIN.appLogo" alt="logo" />
          <p v-if="!isMobile">{{ $GIN_VUE_ADMIN.appName }}</p>
        </div>

        <img class="logo-mobile" @click="toHome" :src="$GIN_VUE_ADMIN.appLogo" alt="logo" />

        <!-- 移动端菜单按钮 -->
        <div class="btn-box" v-if="isMobile">
          <div class="btn menu-btn" @click="appStore.toggleDrawer()">
            <el-icon size="20">
              <Menu />
            </el-icon>
          </div>
        </div>

        <!-- 桌面端侧边栏切换按钮 -->
        <div class="btn-box" v-if="!isMobile && showSidebarToggle">
          <div class="btn sidebar-toggle-btn" @click="toggleSidebar">
            <el-icon size="18">
              <component :is="sidebarCollapsed ? 'Expand' : 'Fold'" />
            </el-icon>
          </div>
        </div>

        <!-- 刷新按钮 -->
        <div class="btn-box" v-if="showRefreshButton">
          <div class="btn refresh-btn" @click="reload">
            <el-icon size="18">
              <Refresh />
            </el-icon>
          </div>
        </div>

        <!-- 面包屑 -->
        <div class="art-breadcrumb" v-if="showCrumbs && isLeftMenu">
          <el-breadcrumb>
            <el-breadcrumb-item
              v-for="item in matched.slice(1, matched.length)"
              :key="item.path"
            >
              {{ fmtTitle(item.meta.title, route) }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <!-- 顶部菜单 -->
        <gva-aside
          v-if="config.side_mode === 'head' && !isMobile"
          class="flex-1"
        />
        <gva-aside
          v-if="config.side_mode === 'combination' && !isMobile"
          mode="head"
          class="flex-1"
        />
      </div>

      <div class="art-header-right">
        <!-- 搜索框 -->
        <div class="search-wrap">
          <div class="search-input" @click="openSearchDialog">
            <div class="search-left">
              <el-icon>
                <Search />
              </el-icon>
              <span>搜索</span>
            </div>
            <div class="search-keydown">
              <span>⌘</span>
              <span>K</span>
            </div>
          </div>
        </div>

        <!-- 工具栏 -->
        <tools />

        <!-- 用户头像、菜单 -->
        <div class="user-menu">
          <el-popover
            placement="bottom-end"
            :width="240"
            :hide-after="0"
            :offset="10"
            trigger="hover"
            :show-arrow="false"
            popper-class="art-user-menu-popover"
          >
            <template #reference>
              <div class="user-avatar">
                <CustomPic />
                <span v-show="!isMobile" class="user-name">{{
                  userStore.userInfo.nickName
                }}</span>
                <el-icon class="arrow-icon">
                  <ArrowDown />
                </el-icon>
              </div>
            </template>
            <template #default>
              <div class="user-menu-content">
                <div class="user-info">
                  <CustomPic class="avatar" />
                  <div class="info-text">
                    <span class="name">{{ userStore.userInfo.nickName }}</span>
                    <span class="role">{{ userStore.userInfo.authority.authorityName }}</span>
                  </div>
                </div>
                <ul class="menu-list">
                  <template v-if="userStore.userInfo.authorities">
                    <li
                      v-for="item in userStore.userInfo.authorities.filter(
                        (i) => i.authorityId !== userStore.userInfo.authorityId
                      )"
                      :key="item.authorityId"
                      @click="changeUserAuth(item.authorityId)"
                    >
                      <el-icon class="menu-icon">
                        <Switch />
                      </el-icon>
                      <span class="menu-text">切换为：{{ item.authorityName }}</span>
                    </li>
                  </template>
                  <li @click="toPerson">
                    <el-icon class="menu-icon">
                      <User />
                    </el-icon>
                    <span class="menu-text">个人信息</span>
                  </li>
                  <div class="menu-divider"></div>
                  <div class="logout-btn" @click="userStore.LoginOut">
                    登出
                  </div>
                </ul>
              </div>
            </template>
          </el-popover>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import tools from './tools.vue'
  import CustomPic from '@/components/customPic/index.vue'
  import { useUserStore } from '@/pinia/modules/user'
  import { useRoute, useRouter } from 'vue-router'
  import { useAppStore } from '@/pinia'
  import { storeToRefs } from 'pinia'
  import { computed, ref, onMounted, onUnmounted } from 'vue'
  import { setUserAuthority } from '@/api/user'
  import { fmtTitle } from '@/utils/fmtRouterTitle'
  import gvaAside from '@/view/layout/aside/index.vue'
  import { emitter } from '@/utils/bus.js'
  import {
    Menu,
    Refresh,
    Search,
    ArrowDown,
    User,
    Switch,
    Expand,
    Fold
  } from '@element-plus/icons-vue'

  const userStore = useUserStore()
  const router = useRouter()
  const route = useRoute()
  const appStore = useAppStore()
  const { device, config } = storeToRefs(appStore)

  const isMobile = computed(() => {
    return device.value === 'mobile'
  })

  const isLeftMenu = computed(() =>
    config.value.side_mode === 'normal' || config.value.side_mode === 'sidebar'
  )

  const isTopMenu = computed(() =>
    config.value.side_mode === 'head'
  )

  const showRefreshButton = computed(() => true)
  const showSidebarToggle = computed(() =>
    config.value.side_mode === 'normal' || config.value.side_mode === 'sidebar'
  )
  const showCrumbs = computed(() =>
    config.value.side_mode !== 'head' && config.value.side_mode !== 'combination'
  )

  const matched = computed(() => route.meta.matched)

  // 侧边栏折叠状态
  const sidebarCollapsed = ref(false)

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
    // 触发侧边栏切换事件
    emitter.emit('toggleSidebar', sidebarCollapsed.value)
  }

  // 监听侧边栏状态变化
  const handleSidebarStateChange = (collapsed) => {
    sidebarCollapsed.value = collapsed
  }

  const toHome = () => {
    router.push({ path: '/' })
  }

  const toPerson = () => {
    router.push({ name: 'person' })
  }



  const reload = () => {
    emitter.emit('reload')
  }

  const openSearchDialog = () => {
    // 打开搜索对话框的逻辑
    console.log('Open search dialog')
  }

  const changeUserAuth = async (id) => {
    const res = await setUserAuthority({
      authorityId: id
    })
    if (res.code === 0) {
      window.sessionStorage.setItem('needCloseAll', 'true')
      window.sessionStorage.setItem('needToHome', 'true')
      window.location.reload()
    }
  }

  onMounted(() => {
    emitter.on('sidebarStateChange', handleSidebarStateChange)
  })

  onUnmounted(() => {
    emitter.off('sidebarStateChange', handleSidebarStateChange)
  })
</script>

<style scoped lang="scss">
@use '@/style/variables.scss' as *;
@use '@/style/mixin.scss' as *;

.art-header-bar {
  position: relative;
  width: 100%;
  height: var(--art-header-height);
  background-color: var(--art-main-bg-color);
  border-bottom: 1px solid var(--art-border-color);
  transition: all var(--art-transition-duration) var(--art-transition-timing);
  flex-shrink: 0;
}

.art-header-menu {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 1rem;
  user-select: none;
}

.art-header-left {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex: 1;
  height: 100%;

  .art-header-logo {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    cursor: pointer;
    padding-left: 0.5rem;

    .logo {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: 50%;
      background: white;
    }

    p {
      margin: 0 0.5rem 0 0.75rem;
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--art-text-gray-800);
    }
  }

  .logo-mobile {
    display: none;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    margin-right: 0.5rem;

    @media (max-width: 500px) {
      display: block;
    }
  }

  .btn-box {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 2.875rem;
    height: var(--art-header-height);

    .btn {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      width: 2.375rem;
      height: 2.375rem;
      cursor: pointer;
      border-radius: var(--art-border-radius-sm);
      transition: all var(--art-transition-duration) var(--art-transition-timing);
      color: var(--art-gray-600);

      &:hover {
        background-color: rgba(var(--art-gray-200-rgb), 0.7);
        color: var(--main-color);
      }

      &.menu-btn {
        margin-left: 0.625rem;
      }

      &.refresh-btn:hover {
        .el-icon {
          animation: rotate360 0.5s;
        }
      }
    }
  }

  .art-breadcrumb {
    margin-left: 1rem;

    :deep(.el-breadcrumb__item) {
      .el-breadcrumb__inner {
        color: var(--art-text-gray-600);
        font-weight: 400;
      }

      &:last-child .el-breadcrumb__inner {
        color: var(--art-text-gray-800);
        font-weight: 500;
      }
    }
  }
}

.art-header-right {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  gap: 0.75rem;

  .search-wrap {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-right: 0.75rem;

    .search-input {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      width: 10rem;
      height: 2.25rem;
      padding: 0 0.625rem;
      cursor: pointer;
      border: 1px solid var(--art-border-dashed-color);
      border-radius: calc(var(--custom-radius) / 2 + 2px);
      transition: all var(--art-transition-duration) var(--art-transition-timing);

      &:hover {
        border-color: var(--main-color);
      }

      .search-left {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        gap: 0.625rem;

        .el-icon {
          font-size: 0.875rem;
          color: var(--art-gray-500);
        }

        span {
          font-size: 0.8125rem;
          font-weight: 400;
          color: var(--art-gray-500);
        }
      }

      .search-keydown {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        height: 1.25rem;
        padding: 0 0.375rem;
        gap: 0.125rem;
        color: var(--art-gray-500);
        background-color: var(--art-bg-color);
        border: 1px solid var(--art-border-dashed-color);
        border-radius: 0.25rem;

        span {
          font-size: 0.75rem;
        }
      }
    }
  }

  .user-menu {
    .user-avatar {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      height: var(--art-header-height);
      padding: 0 0.625rem;
      cursor: pointer;
      transition: all var(--art-transition-duration) var(--art-transition-timing);

      &:hover {
        background-color: rgba(var(--art-gray-200-rgb), 0.5);
      }

      .user-name {
        width: 4rem;
        margin: 0 0.5rem;
        font-size: 0.875rem;
        color: var(--art-text-gray-800);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .arrow-icon {
        font-size: 0.75rem;
        color: var(--art-gray-600);
      }
    }
  }
}

// 动画效果
@keyframes rotate360 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式适配
@media (max-width: 500px) {
  .art-header-left {
    .art-header-logo {
      display: none;
    }

    .art-breadcrumb {
      display: none;
    }
  }

  .art-header-right {
    .search-wrap {
      display: none;
    }
  }
}
</style>

<style lang="scss">
// 用户菜单弹出框样式
.art-user-menu-popover {
  padding: 0 !important;
  border: 1px solid var(--art-border-dashed-color) !important;
  border-radius: calc(var(--custom-radius) / 2 + 4px) !important;

  .user-menu-content {
    padding: 0.625rem 1rem;

    .user-info {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      padding: 0 0 0.25rem;

      .avatar {
        width: 2.5rem;
        height: 2.5rem;
        margin-right: 0.625rem;
        border-radius: 50%;
      }

      .info-text {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        width: calc(100% - 3.75rem);

        .name {
          font-size: 0.875rem;
          font-weight: 500;
          color: var(--art-gray-800);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .role {
          margin-top: 0.1875rem;
          font-size: 0.75rem;
          color: var(--art-gray-500);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .menu-list {
      padding: 1rem 0;
      margin-top: 0.625rem;
      border-top: 1px solid var(--art-border-color);

      li {
        display: flex;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        padding: 0.5rem;
        margin-bottom: 0.625rem;
        cursor: pointer;
        user-select: none;
        border-radius: var(--art-border-radius-sm);
        transition: all var(--art-transition-duration) var(--art-transition-timing);

        &:last-of-type {
          margin-bottom: 0;
        }

        &:hover {
          background-color: rgba(var(--art-gray-200-rgb), 0.7);
        }

        .menu-icon {
          width: 1.5625rem;
          font-size: 1rem;
          color: var(--art-text-gray-800);
        }

        .menu-text {
          font-size: 0.875rem;
          color: var(--art-text-gray-800);
        }
      }

      .menu-divider {
        width: 100%;
        height: 1px;
        margin: 0.625rem 0;
        background-color: var(--art-border-color);
      }

      .logout-btn {
        width: 100%;
        padding: 0.4375rem 0;
        margin-top: 1.25rem;
        font-size: 0.8125rem;
        color: var(--art-text-gray-800);
        text-align: center;
        cursor: pointer;
        border: 1px solid var(--art-border-dashed-color);
        border-radius: 0.4375rem;
        transition: all var(--art-transition-duration) var(--art-transition-timing);

        &:hover {
          box-shadow: 0 0 0.625rem rgba(var(--art-gray-300-rgb), 0.7);
        }
      }
    }
  }
}
</style>
