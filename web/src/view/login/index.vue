<template>
  <div class="login-container">
    <div class="login-left">
      <div class="login-form-container">
        <div class="logo-section">
          <img src="/logo.png" alt="Sellora Logo" class="logo-img">
          <span class="logo-text">广告管理系统</span>
        </div>
        <h1 class="welcome-title">欢迎使用</h1>
        <p class="welcome-subtitle">输入用户密码登录.</p>
        <el-form
          ref="loginForm"
          :model="loginFormData"
          :rules="rules"
          @keyup.enter="submitForm"
          class="login-form"
        >
          <el-form-item prop="username" class="form-item-spacing">
            <el-input
              v-model="loginFormData.username"
              placeholder="用户名"
              size="large"
            />
          </el-form-item>
          <el-form-item prop="password" class="form-item-spacing">
            <el-input
              v-model="loginFormData.password"
              type="password"
              placeholder="密码"
              show-password
              size="large"
            />
          </el-form-item>
           <el-form-item v-if="loginFormData.openCaptcha" prop="captcha" class="form-item-spacing">
            <div class="captcha-wrapper">
              <el-input
                v-model="loginFormData.captcha"
                placeholder="请输入验证码"
                size="large"
                class="captcha-input"
              />
              <img
                v-if="picPath"
                :src="picPath"
                alt="Captcha"
                @click="loginVerify"
                class="captcha-img"
              />
            </div>
          </el-form-item>
          <div class="form-options">
            <el-checkbox v-model="loginFormData.rememberMe" label="Remember Me" size="large" />
            <a href="#" class="forgot-password-link">忘记密码?</a>
          </div>
          <el-form-item class="form-item-spacing">
            <el-button type="primary" @click="submitForm" class="login-button" size="large">登录</el-button>
          </el-form-item>
        </el-form>
        <p class="register-link">
          Don't Have An Account? <a href="#">Register Now.</a>
        </p>
      </div>
      <div class="footer-links">
        <span>Copyright © 2025.</span>
        <a href="#">Privacy Policy</a>
      </div>
    </div>
    <div class="login-right">
      <div class="promo-content">
        <img src="@/assets/dashboard.png" alt="Dashboard Preview" class="dashboard-preview-img">
      </div>
    </div>
  </div>
</template>

<script setup>
import { captcha } from '@/api/user'
import { checkDB } from '@/api/initdb'
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/pinia/modules/user'

defineOptions({
  name: 'Login'
})

const router = useRouter()
// 验证函数
const checkUsername = (rule, value, callback) => {
  // Updated to be a generic email check, or keep as is if username is not email
  if (!value) {
    return callback(new Error('请输入用户名'))
  } else {
    // Basic email validation, consider a more robust regex for production
    // const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    // if (!emailRegex.test(value)) {
    //   return callback(new Error('请输入有效的邮箱地址'))
    // }
    callback()
  }
}
const checkPassword = (rule, value, callback) => {
  if (value.length < 6) {
    return callback(new Error('密码长度不能少于6位'))
  } else {
    callback()
  }
}

// 获取验证码
const loginVerify = async () => {
  try {
    const ele = await captcha()
    if (ele.code === 0) {
      rules.captcha = rules.captcha || [] // Ensure captcha rules array exists
      // Clear previous length validation if any
      rules.captcha = rules.captcha.filter(rule => rule.max === undefined && rule.min === undefined);
      rules.captcha.push({
        max: ele.data.captchaLength,
        min: ele.data.captchaLength,
        message: `请输入${ele.data.captchaLength}位验证码`,
        trigger: 'blur'
      })
      picPath.value = ele.data.picPath
      loginFormData.captchaId = ele.data.captchaId
      loginFormData.openCaptcha = ele.data.openCaptcha
    } else {
      ElMessage.error(ele.msg || '获取验证码失败')
    }
  } catch (error) {
    ElMessage.error('获取验证码请求失败')
  }
}
loginVerify()

// 登录相关操作
const loginForm = ref(null)
const picPath = ref('')
const loginFormData = reactive({
  username: '', // Default to empty or a test user
  password: '',
  captcha: '',
  captchaId: '',
  openCaptcha: false, // This will be updated by loginVerify
  rememberMe: false
})
const rules = reactive({
  username: [{ validator: checkUsername, trigger: 'blur', required: true }],
  password: [{ validator: checkPassword, trigger: 'blur', required: true }],
  captcha: [
    {
      message: '验证码格式不正确',
      trigger: 'blur',
      required: true
    }
  ]
})

const userStore = useUserStore()
const login = async () => {
  return await userStore.LoginIn(loginFormData)
}
const submitForm = () => {
  loginForm.value.validate(async (v) => {
    if (!v) {
      ElMessage({
        type: 'error',
        message: '请正确填写登录信息',
        showClose: true
      })
      if (loginFormData.openCaptcha) {
        await loginVerify() // Refresh captcha only if it's enabled and validation fails
      }
      return false
    }

    const flag = await login()
    if (!flag) {
      if (loginFormData.openCaptcha) {
        await loginVerify() // Refresh captcha on login failure if enabled
      }
      return false
    }
    return true
  })
}

// 跳转初始化 (Keep this if it's part of your application's logic)
const checkInit = async () => {
  const res = await checkDB()
  if (res.code === 0) {
    if (res.data?.needInit) {
      userStore.NeedInit()
      await router.push({ name: 'Init' })
    } else {
      ElMessage({
        type: 'info',
        message: '已配置数据库信息，无法初始化'
      })
    }
  }
}

</script>

<style scoped>
.login-container {
  display: flex;
  width: 100vw;
  height: 100vh;
  font-family: 'Inter', sans-serif; /* Using a common sans-serif font */
}

.login-left {
  flex-basis: 50%;
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Pushes footer to bottom */
  align-items: center;
  padding: 40px 60px;
  background-color: #ffffff;
  box-sizing: border-box;
}

.login-form-container {
  width: 100%;
  max-width: 400px; /* Limit form width for better readability */
  display: flex;
  flex-direction: column;
  align-items: center; /* Center content like logo and title */
}

.logo-section {
  display: flex;
  align-items: center;
  margin-bottom: 30px; /* Space below logo */
  align-self: flex-start; /* Align to the left */
}

.logo-img {
  width: 32px; /* Adjust as needed */
  height: 32px;
  margin-right: 10px;
}

.logo-text {
  font-size: 24px;
  font-weight: 600;
  color: #101828; /* Dark grey for text */
}

.welcome-title {
  font-size: 30px;
  font-weight: 600;
  color: #101828;
  margin-bottom: 8px;
  text-align: left;
  width: 100%;
}

.welcome-subtitle {
  font-size: 16px;
  color: #667085; /* Lighter grey for subtitle */
  margin-bottom: 30px;
  text-align: left;
  width: 100%;
}

.login-form {
  width: 100%;
}

.form-item-spacing {
  margin-bottom: 20px;
}

.captcha-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.captcha-input {
  flex-grow: 1;
  margin-right: 10px;
}

.captcha-img {
  width: 120px; /* Adjust as needed */
  height: 40px; /* Should match input height */
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #d0d5dd; /* Light border for captcha image */
}


.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;
}

.forgot-password-link {
  color: #444CE7; /* Primary blue */
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
}

.forgot-password-link:hover {
  text-decoration: underline;
}

.login-button {
  width: 100%;
  background-color: #444CE7 !important; /* Primary blue */
  border-color: #444CE7 !important;
  color: white !important;
  font-weight: 500;
  padding: 12px 0;
}

.login-button:hover {
  background-color: #3941c6 !important; /* Darker blue on hover */
  border-color: #3941c6 !important;
}

.social-login-divider {
  margin: 25px 0;
  text-align: center;
  color: #667085;
  font-size: 14px;
  width: 100%;
  display: flex;
  align-items: center;
}

.social-login-divider::before,
.social-login-divider::after {
  content: '';
  flex-grow: 1;
  height: 1px;
  background-color: #eaecf0; /* Light grey for divider lines */
  margin: 0 10px;
}

.social-login-buttons {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: 15px; /* Space between buttons */
  margin-bottom: 30px;
}

.social-button {
  flex-grow: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  border: 1px solid #d0d5dd !important; /* Light grey border */
  background-color: #ffffff !important;
  color: #344054 !important; /* Dark grey text for social buttons */
  font-weight: 500;
}

.social-button:hover {
  background-color: #f9fafb !important; /* Very light grey on hover */
}

.social-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.register-link {
  font-size: 14px;
  color: #667085;
  text-align: center;
  width: 100%;
}

.register-link a {
  color: #444CE7; /* Primary blue */
  font-weight: 500;
  text-decoration: none;
}

.register-link a:hover {
  text-decoration: underline;
}

.footer-links {
  width: 100%;
  max-width: 400px; /* Match form width */
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #667085;
  padding-bottom: 20px; /* Padding at the very bottom */
}

.footer-links a {
  color: #667085;
  text-decoration: none;
}

.footer-links a:hover {
  text-decoration: underline;
}

.login-right {
  flex-basis: 50%;
  background-color: #444CE7; /* Primary blue */
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px;
  box-sizing: border-box;
  overflow: hidden; /* Hide parts of the image if it's too large */
}

.promo-content {
  color: white;
  text-align: left;
  max-width: 500px; /* Limit width of promo text */
}

.promo-title {
  font-size: 40px; /* Larger title for promo */
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 16px;
}

.promo-subtitle {
  font-size: 18px;
  line-height: 1.5;
  margin-bottom: 40px;
  color: #E0EAFF; /* Lighter blue for subtitle */
}

.dashboard-preview-img {
  width: 100%;
  max-width: 600px; /* Control image size */
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 900px) { /* Adjust breakpoint as needed */
  .login-left {
    flex-basis: 100%;
    padding: 30px;
  }
  .login-right {
    display: none; /* Hide right panel on smaller screens */
  }
  .login-form-container {
    max-width: 100%;
  }
  .footer-links {
    max-width: 100%;
    padding-top: 20px; /* Add some space above footer on mobile */
  }
}

@media (max-width: 480px) {
  .welcome-title {
    font-size: 24px;
  }
  .welcome-subtitle {
    font-size: 14px;
  }
  .promo-title {
    font-size: 30px;
  }
  .promo-subtitle {
    font-size: 16px;
  }
  .social-login-buttons {
    flex-direction: column;
  }
  .form-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  .forgot-password-link {
    align-self: flex-start;
  }
}
</style>
