// 引入样式系统
@use '@/style/variables.scss';
@use '@/style/mixin.scss' as *;
@use '@/style/iconfont.css';
@use "./transition.scss";

// 全局样式重置和基础设置
.html-grey {
  filter: grayscale(100%);
}

.html-weakenss {
  filter: invert(80%);
}

// 色弱模式
.color-weak {
  filter: invert(80%);
  -webkit-filter: invert(80%);
}

// 升级后的组件样式
.gva-table-box {
  background-color: var(--art-main-bg-color);
  border-radius: var(--custom-radius);
  border: 1px solid var(--art-border-color);
  box-shadow: var(--art-card-shadow);
  padding: 1rem;
  margin: 0.5rem 0;
  color: var(--art-text-gray-700);

  .el-table {
    border: 1px solid var(--art-border-color);
    border-radius: var(--art-border-radius-sm);
    margin: -1px;
    border-bottom: none;
  }
}

.gva-btn-list {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 0.75rem;
  gap: 0.5rem;
}

// 进度条样式升级
#nprogress .bar {
  background: var(--main-color) !important;
}

.gva-customer-icon {
  width: 1rem;
  height: 1rem;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px !important;
  height: 0 !important;
}

::-webkit-scrollbar-track {
  background-color: var(--art-gray-100);
}

::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: #cccccc !important;
  transition: all 0.2s;
  -webkit-transition: all 0.2s;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #b0abab !important;
}

::-webkit-scrollbar-button {
  height: 0px;
  width: 0;
}

// 暗色模式滚动条
.dark {
  ::-webkit-scrollbar-track {
    background-color: var(--art-bg-color);
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(var(--art-gray-300-rgb), 0.8) !important;
  }
}

.gva-search-box {
  background-color: var(--art-main-bg-color);
  border-radius: var(--custom-radius);
  border: 1px solid var(--art-border-color);
  box-shadow: var(--art-card-shadow);
  padding: 1rem;
  margin: 0.5rem 0;
  color: var(--art-text-gray-700);
}

.gva-form-box {
  background-color: var(--art-main-bg-color);
  border-radius: var(--custom-radius);
  border: 1px solid var(--art-border-color);
  box-shadow: var(--art-card-shadow);
  padding: 1rem;
  margin: 0.5rem 0;
  color: var(--art-text-gray-700);
}

.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background: var(--el-color-primary-bg) !important;
}

.el-dropdown {
  outline: none;
  * {
    outline: none;
  }
}

// 新增的全局样式
// 页面内容区域
.page-content,
.art-custom-card {
  border: 1px solid var(--art-card-border) !important;
  border-radius: var(--custom-radius) !important;
  background-color: var(--art-main-bg-color);
}

// 盒子模式样式
[data-box-mode='border-mode'] {
  .page-content,
  .art-custom-card,
  .art-table-card {
    border: 1px solid var(--art-card-border) !important;
  }

  .layout-sidebar {
    border-right: 1px solid var(--art-card-border) !important;
  }
}

[data-box-mode='shadow-mode'] {
  .page-content,
  .art-custom-card,
  .art-table-card {
    box-shadow: var(--art-card-shadow) !important;
    border: 1px solid rgba(var(--art-gray-300-rgb), 0.3) !important;
  }

  .layout-sidebar {
    border-right: 1px solid rgba(var(--art-gray-300-rgb), 0.4) !important;
  }
}

// 表格卡片样式
.art-table-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-top: 15px;
  border-radius: calc(var(--custom-radius) / 2 + 2px) !important;

  .el-card__body {
    height: 100%;
    overflow: hidden;
  }
}

// 按钮样式增强
.el-btn-red {
  color: rgb(var(--art-danger)) !important;

  &:hover {
    opacity: 0.9;
  }

  &:active {
    opacity: 0.7;
  }
}

// 全屏元素样式
.el-full-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 500;
  margin-top: 0;
  padding: 15px;
  box-sizing: border-box;
  background-color: var(--art-main-bg-color);

  .art-table-full-screen {
    height: 100% !important;
  }
}

// 移动端适配
@media (max-width: 500px) {
  * {
    cursor: default !important;
  }

  .el-col2 {
    margin-top: 15px;
  }
  .gva-table-box {
    padding-left: 2px;
    padding-right: 2px;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .gva-table-box .gva-btn-list{
    padding: 1rem;
  }

  .el-pagination .el-select {
        width: 50px !important;
  }

 .el-date-range-picker .el-picker-panel__body {
        min-width: 80%;
    }
    .el-date-range-picker__content{
        width: 80% !important;
    }
    .el-date-range-picker{
        width: 80% !important;
    }
    .el-date-range-picker__content{
        margin: 0px;
        padding: 1px;
    }
    .el-date-range-picker__content.is-left{
        padding-bottom: 0px;
    }
    .el-date-range-picker__content.is-right {
        padding-top: 0px;
    }
    .el-date-table th{
        padding: 0px;
    }
    .el-date-table td{
        padding: 0px;
    }
}

// 语言切换下拉菜单样式
.langDropDownStyle {
  .is-selected {
    background-color: rgba(var(--art-gray-200-rgb), 0.8) !important;
  }

  .lang-btn-item {
    .el-dropdown-menu__item {
      padding-left: 13px !important;
      padding-right: 6px !important;
      margin-bottom: 3px !important;
    }

    &:last-child {
      .el-dropdown-menu__item {
        margin-bottom: 0 !important;
      }
    }

    .menu-txt {
      min-width: 60px;
      display: block;
    }

    i {
      font-size: 10px;
      margin-left: 10px;
    }
  }
}

// 移动端表格吸顶样式
.mobile-sticky-header :deep(.el-table__header-wrapper) {
  position: sticky;
  top: 0;
  z-index: 999;
  background-color: var(--el-bg-color);
}

.mobile-sticky-header :deep(.el-table__header) {
  background-color: var(--el-bg-color);
}

.mobile-sticky-header :deep(.el-table__header th) {
  background-color: var(--el-bg-color);
}
