import { ref, computed } from 'vue'

/**
 * 设备检测工具类
 * 提供统一的移动端检测功能
 */

// 移动端断点，与 Tailwind CSS 的 md 断点一致
const MOBILE_BREAKPOINT = 768

// 全局响应式状态
const screenWidth = ref(window.innerWidth)
const isMobileDevice = ref(window.innerWidth < MOBILE_BREAKPOINT)

/**
 * 检测当前设备是否为移动端
 * @returns {boolean} 是否为移动端
 */
export function checkIsMobile() {
  return window.innerWidth < MOBILE_BREAKPOINT
}

/**
 * 响应式的移动端检测
 * @returns {Ref<boolean>} 响应式的移动端状态
 */
export function useIsMobile() {
  return computed(() => isMobileDevice.value)
}

/**
 * 获取当前屏幕宽度
 * @returns {Ref<number>} 响应式的屏幕宽度
 */
export function useScreenWidth() {
  return computed(() => screenWidth.value)
}

/**
 * 更新设备状态
 * 内部方法，用于响应窗口大小变化
 */
function updateDeviceStatus() {
  screenWidth.value = window.innerWidth
  isMobileDevice.value = window.innerWidth < MOBILE_BREAKPOINT
}

/**
 * 初始化设备检测
 * 添加窗口大小变化监听器
 */
export function initDeviceDetection() {
  // 初始检测
  updateDeviceStatus()

  // 添加窗口大小变化监听
  window.addEventListener('resize', updateDeviceStatus)

  // 返回清理函数
  return () => {
    window.removeEventListener('resize', updateDeviceStatus)
  }
}

/**
 * 防抖版本的设备检测更新
 * @param {number} delay 防抖延迟时间，默认100ms
 */
export function initDeviceDetectionWithDebounce(delay = 100) {
  let timeoutId = null

  const debouncedUpdate = () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    timeoutId = setTimeout(updateDeviceStatus, delay)
  }

  // 初始检测
  updateDeviceStatus()

  // 添加防抖的窗口大小变化监听
  window.addEventListener('resize', debouncedUpdate)

  // 返回清理函数
  return () => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }
    window.removeEventListener('resize', debouncedUpdate)
  }
}

/**
 * 根据不同断点判断设备类型
 * @param {number} customBreakpoint 自定义断点，默认使用 MOBILE_BREAKPOINT
 * @returns {string} 设备类型: 'mobile' | 'tablet' | 'desktop'
 */
export function getDeviceType(customBreakpoint = MOBILE_BREAKPOINT) {
  const width = window.innerWidth

  if (width < customBreakpoint) {
    return 'mobile'
  } else if (width < 1024) {
    return 'tablet'
  } else {
    return 'desktop'
  }
}

/**
 * 响应式的设备类型检测
 * @param {number} customBreakpoint 自定义断点
 * @returns {ComputedRef<string>} 响应式的设备类型
 */
export function useDeviceType(customBreakpoint = MOBILE_BREAKPOINT) {
  return computed(() => {
    const width = screenWidth.value

    if (width < customBreakpoint) {
      return 'mobile'
    } else if (width < 1024) {
      return 'tablet'
    } else {
      return 'desktop'
    }
  })
}

// 默认导出主要的检测函数
export default {
  checkIsMobile,
  useIsMobile,
  useScreenWidth,
  initDeviceDetection,
  initDeviceDetectionWithDebounce,
  getDeviceType,
  useDeviceType,
  MOBILE_BREAKPOINT
}
