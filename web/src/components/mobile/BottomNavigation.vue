<template>
  <div class="bottom-navigation" v-if="isMobile">
    <div class="nav-container">
      <div 
        v-for="tab in tabs" 
        :key="tab.name"
        class="nav-item"
        :class="{ active: activeTab === tab.name }"
        @click="switchTab(tab.name)"
      >
        <el-icon class="nav-icon">
          <component :is="tab.icon" />
        </el-icon>
        <span class="nav-label">{{ tab.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useIsMobile } from '@/utils/device'
import { 
  House, 
  Monitor, 
  Setting 
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const isMobile = useIsMobile()

// 定义底部导航标签
const tabs = [
  {
    name: 'home',
    label: '首页',
    icon: House,
    path: '/mobile/home'
  },
  {
    name: 'ad',
    label: '系统',
    icon: Monitor,
    path: '/layout/dashboard'
  },
  {
    name: 'person',
    label: '我的',
    icon: Setting,
    path: '/layout/person'
  }
]

// 当前激活的标签
const activeTab = computed(() => {
  const currentPath = route.path
  if (currentPath.includes('/home')) return 'home'
  if (currentPath.includes('/ad')) return 'ad'
  if (currentPath.includes('/person')) return 'person'
  return 'ad'
})

// 切换标签
const switchTab = (tabName) => {
  const tab = tabs.find(t => t.name === tabName)
  if (tab && route.path !== tab.path) {
    router.push(tab.path)
  }
}
</script>

<style scoped>
.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #e4e7ed;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.nav-container {
  display: flex;
  height: 60px;
  max-width: 100%;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px 4px;
}

.nav-item:hover {
  background-color: #f5f7fa;
}

.nav-item.active {
  color: #409eff;
}

.nav-icon {
  font-size: 20px;
  margin-bottom: 2px;
}

.nav-label {
  font-size: 12px;
  line-height: 1;
}

/* 为底部导航留出空间 */
:global(.mobile-content) {
  padding-bottom: 70px;
}
</style>
