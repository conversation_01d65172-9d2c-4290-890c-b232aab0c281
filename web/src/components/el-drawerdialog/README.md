# el-drawerdialog 组件

一个响应式的对话框组件，在桌面端显示为居中的 dialog，在移动端显示为侧边的 drawer。

## 功能特性

- **响应式显示**：自动根据屏幕尺寸切换显示模式
- **API 兼容**：完全兼容 el-drawer 的所有属性和事件
- **布局优化**：桌面端表单左右布局，移动端上下布局
- **底部按钮**：按钮固定在底部，样式统一

## 使用方法

### 基础用法

```vue
<template>
  <el-drawerdialog 
    v-model="dialogVisible" 
    title="对话框标题"
    width="600px"
    size="50%"
  >
    <p>这里是内容区域</p>
    
    <template #footer>
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </template>
  </el-drawerdialog>
</template>

<script setup>
import { ref } from 'vue'

const dialogVisible = ref(false)

const handleConfirm = () => {
  // 处理确认逻辑
  dialogVisible.value = false
}
</script>
```

### 表单用法

```vue
<template>
  <el-drawerdialog 
    v-model="formDialogVisible" 
    title="编辑信息"
    width="700px"
    size="60%"
  >
    <el-form :model="formData" label-width="120px" :rules="rules" ref="formRef">
      <el-form-item label="姓名:" prop="name">
        <el-input v-model="formData.name" placeholder="请输入姓名" />
      </el-form-item>
      
      <el-form-item label="邮箱:" prop="email">
        <el-input v-model="formData.email" placeholder="请输入邮箱" />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="formDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">提交</el-button>
    </template>
  </el-drawerdialog>
</template>
```

## 属性说明

### 基础属性（兼容 el-drawer）

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | Boolean | false | 是否显示对话框 |
| title | String | '' | 对话框标题 |
| size | String/Number | '30%' | 移动端 drawer 的大小 |
| direction | String | 'rtl' | drawer 的方向 |
| showClose | Boolean | true | 是否显示关闭按钮 |
| beforeClose | Function | - | 关闭前的回调 |
| destroyOnClose | Boolean | false | 关闭时销毁内容 |

### 桌面端特有属性（el-dialog）

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| width | String/Number | '50%' | 桌面端 dialog 的宽度 |
| fullscreen | Boolean | false | 是否全屏显示 |
| top | String | '15vh' | dialog 距离顶部的距离 |
| draggable | Boolean | false | 是否可拖拽 |
| alignCenter | Boolean | true | 是否居中对齐 |

## 事件说明

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| open | 对话框打开时触发 | - |
| opened | 对话框打开动画结束时触发 | - |
| close | 对话框关闭时触发 | - |
| closed | 对话框关闭动画结束时触发 | - |

## 插槽说明

| 插槽名 | 说明 |
|--------|------|
| default | 对话框内容 |
| header | 自定义头部内容 |
| footer | 底部按钮区域 |

## 响应式断点

- **桌面端**：屏幕宽度 ≥ 768px，显示为 el-dialog
- **移动端**：屏幕宽度 < 768px，显示为 el-drawer

## 样式特性

### 桌面端（el-dialog）
- 表单使用左右布局（标签在左，控件在右）
- 标签宽度固定为 120px，右对齐
- 底部按钮区域有边框分隔

### 移动端（el-drawer）
- 表单保持上下布局（标签在上，控件在下）
- 底部按钮固定在底部
- 内容区域可滚动

## 注意事项

1. 组件已全局注册，可直接使用 `<el-drawerdialog>`
2. 建议在 footer 插槽中放置操作按钮
3. 表单验证和提交逻辑需要自行实现
4. 组件会自动监听窗口大小变化，动态切换显示模式
