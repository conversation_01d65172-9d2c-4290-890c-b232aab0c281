<template>
  <!-- 桌面端：使用 el-dialog -->
  <el-dialog
    v-if="!isMobile"
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="dialogWidth"
    :show-close="showClose"
    :before-close="beforeClose"
    :destroy-on-close="destroyOnClose"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :modal="modal"
    :modal-class="modalClass"
    :z-index="zIndex"
    :draggable="draggable"
    :overflow="overflow"
    :fullscreen="fullscreen"
    :lock-scroll="lockScroll"
    :custom-class="customClass"
    :open-delay="openDelay"
    :close-delay="closeDelay"
    :top="top"
    :modal-append-to-body="modalAppendToBody"
    :append-to-body="appendToBody"
    :center="center"
    :align-center="alignCenter"
    class="el-drawerdialog-dialog"
    @open="$emit('open')"
    @opened="$emit('opened')"
    @close="$emit('close')"
    @closed="$emit('closed')"
  >
    <!-- 如果有 header 插槽，显示自定义头部，否则显示标题 -->
    <template #header v-if="$slots.header">
      <slot name="header"></slot>
    </template>

    <div class="el-drawerdialog-content">
      <slot></slot>
    </div>

    <!-- 底部按钮区域 -->
    <template #footer>
      <div class="el-drawerdialog-footer" v-if="$slots.footer">
        <slot name="footer"></slot>
      </div>
    </template>
  </el-dialog>

  <!-- 移动端：使用 el-drawer -->
  <el-drawer
    v-else
    v-model="drawerVisible"
    :title="dialogTitle"
    :size="size"
    :direction="direction"
    :show-close="showClose"
    :before-close="beforeClose"
    :destroy-on-close="destroyOnClose"
    :modal="modal"
    :modal-class="modalClass"
    :z-index="zIndex"
    :lock-scroll="lockScroll"
    :custom-class="customClass"
    :open-delay="openDelay"
    :close-delay="closeDelay"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    class="el-drawerdialog-drawer"
    @open="$emit('open')"
    @opened="$emit('opened')"
    @close="$emit('close')"
    @closed="$emit('closed')"
  >
    <!-- 如果有 header 插槽，显示自定义头部，否则显示标题 -->
    <template #header v-if="$slots.header">
      <slot name="header"></slot>
    </template>

    <div class="el-drawerdialog-content">
      <slot></slot>
    </div>

    <!-- 底部按钮区域 -->
    <div class="el-drawerdialog-footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

defineOptions({
  name: 'ElDrawerdialog'
})

// Props - 兼容 el-drawer 的所有属性
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  size: {
    type: [String, Number],
    default: '30%'
  },
  direction: {
    type: String,
    default: 'rtl'
  },
  showClose: {
    type: Boolean,
    default: true
  },
  beforeClose: {
    type: Function
  },
  destroyOnClose: {
    type: Boolean,
    default: false
  },
  modal: {
    type: Boolean,
    default: true
  },
  modalClass: {
    type: String,
    default: ''
  },
  zIndex: {
    type: Number
  },
  lockScroll: {
    type: Boolean,
    default: true
  },
  customClass: {
    type: String,
    default: ''
  },
  openDelay: {
    type: Number,
    default: 0
  },
  closeDelay: {
    type: Number,
    default: 0
  },
  closeOnClickModal: {
    type: Boolean,
    default: true
  },
  closeOnPressEscape: {
    type: Boolean,
    default: true
  },
  // dialog 特有属性
  width: {
    type: [String, Number],
    default: '50%'
  },
  fullscreen: {
    type: Boolean,
    default: false
  },
  top: {
    type: String,
    default: '15vh'
  },
  modalAppendToBody: {
    type: Boolean,
    default: true
  },
  appendToBody: {
    type: Boolean,
    default: false
  },
  center: {
    type: Boolean,
    default: false
  },
  alignCenter: {
    type: Boolean,
    default: true
  },
  draggable: {
    type: Boolean,
    default: false
  },
  overflow: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'open', 'opened', 'close', 'closed'])

// 响应式检测
const isMobile = ref(false)

const checkDevice = () => {
  // 使用更合适的移动端断点，与 Tailwind CSS 的 md 断点一致
  isMobile.value = window.innerWidth < 768
}

onMounted(() => {
  checkDevice()
  window.addEventListener('resize', checkDevice)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkDevice)
})

// 控制显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const drawerVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 计算 dialog 宽度
const dialogWidth = computed(() => {
  if (typeof props.width === 'string') {
    return props.width
  }
  return `${props.width}px`
})

// 计算标题 - 如果没有 header 插槽，使用 title 属性
const dialogTitle = computed(() => {
  return props.title
})
</script>

<style lang="scss" scoped>
// 桌面端 dialog 样式
:deep(.el-drawerdialog-dialog) {
  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid var(--el-border-color-light);
    background-color: var(--el-bg-color);
  }

  .el-drawerdialog-content {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
  }

  .el-drawerdialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }

  // 表单左右布局样式
  .el-form {
    .el-form-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 18px;

      .el-form-item__label {
        flex: 0 0 120px;
        text-align: right;
        padding-right: 12px;
        line-height: 32px;
        white-space: nowrap;
        font-weight: 500;
      }

      .el-form-item__content {
        flex: 1;
        min-width: 0;
      }

      // 处理复选框组的对齐
      .el-checkbox-group {
        line-height: 32px;
      }

      // 处理文本域的对齐
      .el-textarea {
        .el-textarea__inner {
          min-height: 80px;
        }
      }
    }
  }
}

// 移动端 drawer 样式
:deep(.el-drawerdialog-drawer) {
  .el-drawer__body {
    padding: 0;
    display: flex;
    flex-direction: column;
  }

  .el-drawerdialog-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
  }

  .el-drawerdialog-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--el-border-color-light);
    background-color: var(--el-bg-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    flex-shrink: 0;
  }

  // 移动端表单保持上下布局
  .el-form {
    .el-form-item {
      margin-bottom: 18px;

      .el-form-item__label {
        margin-bottom: 8px;
        line-height: 1.4;
      }
    }
  }
}
</style>
