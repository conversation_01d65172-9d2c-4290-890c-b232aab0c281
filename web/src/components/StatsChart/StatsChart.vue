<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="stats-chart-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>
      
      <!-- 图表内容 -->
      <div v-else class="charts-wrapper">
        <!-- 默认图表：广告展示数、点击数 -->
        <div class="chart-item">
          <h3>广告展示数 & 点击数</h3>
          <div ref="defaultChartRef" class="chart-container"></div>
        </div>
        
        <!-- APP类型额外图表：日活用户数 -->
        <div v-if="targetType === 1" class="chart-item">
          <h3>日活用户数</h3>
          <div ref="dauChartRef" class="chart-container"></div>
        </div>
        
        <!-- 广告计划类型图表：计划及关联广告数据 -->
        <div v-if="targetType === 3" class="chart-item">
          <h3>广告计划统计（含关联广告）</h3>
          <div ref="campaignChartRef" class="chart-container"></div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { getDashubList } from '@/api/meta/dashub'
import { findCampaign } from '@/api/meta/campaign'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  targetId: {
    type: Number,
    required: true
  },
  targetType: {
    type: Number,
    required: true,
    validator: (value) => [1, 2, 3].includes(value)
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = ref(false)
const loading = ref(false)
const dialogTitle = ref('')

// 图表引用
const defaultChartRef = ref(null)
const dauChartRef = ref(null)
const campaignChartRef = ref(null)

// 图表实例
let defaultChart = null
let dauChart = null
let campaignChart = null

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    initDialog()
  }
})

// 监听 visible 变化
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

// 初始化对话框
const initDialog = async () => {
  setDialogTitle()
  await loadChartData()
}

// 设置对话框标题
const setDialogTitle = () => {
  const typeMap = {
    1: 'APP',
    2: '广告',
    3: '广告计划'
  }
  dialogTitle.value = `${typeMap[props.targetType]}统计图表 (ID: ${props.targetId})`
}

// 加载图表数据
const loadChartData = async () => {
  loading.value = true
  try {
    // 根据不同类型加载不同的图表数据
    if (props.targetType === 1) {
      // APP类型：加载日活用户数 + 默认图表
      await Promise.all([
        loadDefaultCharts(),
        loadDAUChart()
      ])
    } else if (props.targetType === 2) {
      // 广告类型：只加载默认图表
      await loadDefaultCharts()
    } else if (props.targetType === 3) {
      // 广告计划类型：加载计划及关联广告数据
      await loadCampaignCharts()
    }
  } catch (error) {
    console.error('加载图表数据失败:', error)
    ElMessage.error('加载图表数据失败')
  } finally {
    loading.value = false
  }
}

// 加载默认图表（广告展示数、点击数）
const loadDefaultCharts = async () => {
  const [impressionsData, clicksData] = await Promise.all([
    fetchDashubData(2001), // DASHUB_AD_IMPRESSIONS
    fetchDashubData(2002)  // DASHUB_AD_CLICKS
  ])
  
  await nextTick()
  renderDefaultChart(impressionsData, clicksData)
}

// 加载日活用户数图表
const loadDAUChart = async () => {
  const dauData = await fetchDashubData(1001) // DASHUB_DAILY_ACTIVE_USERS
  
  await nextTick()
  renderDAUChart(dauData)
}

// 加载广告计划图表
const loadCampaignCharts = async () => {
  try {
    // 1. 先加载广告计划自身的数据
    const [campaignImpressions, campaignClicks] = await Promise.all([
      fetchDashubData(2001), // 广告展示数
      fetchDashubData(2002)  // 广告点击数
    ])
    
    // 2. 获取广告计划关联的广告ID列表
    const campaignData = await findCampaign({ ID: props.targetId })
    let adIds = []
    if (campaignData.code === 0 && campaignData.data.ad_ids) {
      try {
        // 处理可能是字符串或已经是数组的情况
        if (typeof campaignData.data.ad_ids === 'string') {
          adIds = JSON.parse(campaignData.data.ad_ids)
        } else if (Array.isArray(campaignData.data.ad_ids)) {
          adIds = campaignData.data.ad_ids
        }
      } catch (e) {
        console.warn('解析广告ID列表失败:', e)
      }
    }
    
    // 3. 加载每个关联广告的数据
    const adDataPromises = []
    for (const adId of adIds) {
      adDataPromises.push(
        Promise.all([
          fetchDashubData(2001, 2, adId), // 广告展示数
          fetchDashubData(2002, 2, adId)  // 广告点击数
        ])
      )
    }
    
    const adDataResults = await Promise.all(adDataPromises)
    
    await nextTick()
    renderCampaignChart(campaignImpressions, campaignClicks, adDataResults, adIds)
  } catch (error) {
    console.error('加载广告计划图表数据失败:', error)
    throw error
  }
}

// 获取 Dashub 数据
const fetchDashubData = async (kind, targetType = props.targetType, target = props.targetId) => {
  try {
    const params = {
      kind,
      target_type: targetType,
      target,
      page: 1,
      pageSize: 100 // 获取足够多的数据点
    }
    
    const response = await getDashubList(params)
    if (response.code === 0) {
      return response.data.list || []
    } else {
      console.warn(`获取数据失败 (kind: ${kind}):`, response.msg)
      return []
    }
  } catch (error) {
    console.error(`获取数据异常 (kind: ${kind}):`, error)
    return []
  }
}

// 渲染默认图表
const renderDefaultChart = (impressionsData, clicksData) => {
  if (!defaultChartRef.value) return
  
  if (defaultChart) {
    defaultChart.dispose()
  }
  
  defaultChart = echarts.init(defaultChartRef.value)
  
  // 处理数据
  const dates = [...new Set([
    ...impressionsData.map(item => item.day),
    ...clicksData.map(item => item.day)
  ])].sort()

  // 如果没有数据，显示提示
  if (dates.length === 0) {
    const option = {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center'
      }
    }
    defaultChart.setOption(option)
    return
  }

  const impressions = dates.map(date => {
    const item = impressionsData.find(d => d.day === date)
    return item ? Number(item.nums) || 0 : 0
  })

  const clicks = dates.map(date => {
    const item = clicksData.find(d => d.day === date)
    return item ? Number(item.nums) || 0 : 0
  })
  
  const option = {
    title: {
      text: '广告展示数 & 点击数趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['展示数', '点击数']
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '展示数',
        type: 'line',
        data: impressions,
        smooth: true,
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '点击数',
        type: 'line',
        data: clicks,
        smooth: true,
        itemStyle: { color: '#67C23A' }
      }
    ]
  }
  
  defaultChart.setOption(option)
}

// 渲染日活用户数图表
const renderDAUChart = (dauData) => {
  if (!dauChartRef.value) return
  
  if (dauChart) {
    dauChart.dispose()
  }
  
  dauChart = echarts.init(dauChartRef.value)
  
  const dates = dauData.map(item => item.day).sort()

  // 如果没有数据，显示提示
  if (dates.length === 0) {
    const option = {
      title: {
        text: '暂无日活数据',
        left: 'center',
        top: 'center'
      }
    }
    dauChart.setOption(option)
    return
  }

  const values = dates.map(date => {
    const item = dauData.find(d => d.day === date)
    return item ? Number(item.nums) || 0 : 0
  })
  
  const option = {
    title: {
      text: '日活用户数趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '日活用户数',
        type: 'line',
        data: values,
        smooth: true,
        itemStyle: { color: '#E6A23C' },
        areaStyle: {
          opacity: 0.3
        }
      }
    ]
  }
  
  dauChart.setOption(option)
}

// 渲染广告计划图表
const renderCampaignChart = (campaignImpressions, campaignClicks, adDataResults, adIds) => {
  if (!campaignChartRef.value) return
  
  if (campaignChart) {
    campaignChart.dispose()
  }
  
  campaignChart = echarts.init(campaignChartRef.value)
  
  // 收集所有日期
  const allDates = new Set()
  campaignImpressions.forEach(item => allDates.add(item.day))
  campaignClicks.forEach(item => allDates.add(item.day))
  
  adDataResults.forEach(([impressions, clicks]) => {
    impressions.forEach(item => allDates.add(item.day))
    clicks.forEach(item => allDates.add(item.day))
  })
  
  const dates = [...allDates].sort()

  // 如果没有数据，显示提示
  if (dates.length === 0) {
    const option = {
      title: {
        text: '暂无广告计划数据',
        left: 'center',
        top: 'center'
      }
    }
    campaignChart.setOption(option)
    return
  }

  // 准备系列数据
  const series = []
  
  // 广告计划展示数
  const campaignImpressionsData = dates.map(date => {
    const item = campaignImpressions.find(d => d.day === date)
    return item ? Number(item.nums) || 0 : 0
  })
  series.push({
    name: '计划-展示数',
    type: 'line',
    data: campaignImpressionsData,
    smooth: true,
    itemStyle: { color: '#409EFF' }
  })

  // 广告计划点击数
  const campaignClicksData = dates.map(date => {
    const item = campaignClicks.find(d => d.day === date)
    return item ? Number(item.nums) || 0 : 0
  })
  series.push({
    name: '计划-点击数',
    type: 'line',
    data: campaignClicksData,
    smooth: true,
    itemStyle: { color: '#67C23A' }
  })
  
  // 每个关联广告的数据
  const colors = ['#E6A23C', '#F56C6C', '#909399', '#C0C4CC']
  adDataResults.forEach(([impressions, clicks], index) => {
    const adId = adIds[index]
    const color = colors[index % colors.length]
    
    // 广告展示数
    const adImpressionsData = dates.map(date => {
      const item = impressions.find(d => d.day === date)
      return item ? Number(item.nums) || 0 : 0
    })
    series.push({
      name: `广告${adId}-展示数`,
      type: 'line',
      data: adImpressionsData,
      smooth: true,
      itemStyle: { color: color },
      lineStyle: { type: 'dashed' }
    })

    // 广告点击数
    const adClicksData = dates.map(date => {
      const item = clicks.find(d => d.day === date)
      return item ? Number(item.nums) || 0 : 0
    })
    series.push({
      name: `广告${adId}-点击数`,
      type: 'line',
      data: adClicksData,
      smooth: true,
      itemStyle: { color: color },
      lineStyle: { type: 'dotted' }
    })
  })
  
  const option = {
    title: {
      text: '广告计划及关联广告统计'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: series.map(s => s.name),
      type: 'scroll'
    },
    xAxis: {
      type: 'category',
      data: dates
    },
    yAxis: {
      type: 'value'
    },
    series
  }
  
  campaignChart.setOption(option)
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  
  // 销毁图表实例
  if (defaultChart) {
    defaultChart.dispose()
    defaultChart = null
  }
  if (dauChart) {
    dauChart.dispose()
    dauChart = null
  }
  if (campaignChart) {
    campaignChart.dispose()
    campaignChart = null
  }
}
</script>

<style scoped>
.stats-chart-container {
  min-height: 400px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  font-size: 16px;
  color: #666;
}

.loading-container .el-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.charts-wrapper {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.chart-item h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.chart-container {
  width: 100%;
  height: 400px;
}
</style>
