# 统计图表功能演示脚本

## 演示步骤

### 1. 功能概述
- 为广告计划的"计划名称"添加了点击统计图表功能
- 点击后在弹出层中展示数据统计图表
- 支持多种数据类型和图表展示

### 2. 测试页面演示
1. 打开测试页面：`http://localhost:8082/#/test/stats-chart`
2. 点击"测试广告计划图表"按钮
3. 查看弹出的统计图表，包含：
   - 广告展示数趋势
   - 广告点击数趋势
   - 如果是广告计划，还会显示关联广告的数据

### 3. 实际使用演示
1. 登录系统后进入广告计划管理页面
2. 在表格中找到"计划名称"列
3. 点击任意计划名称（现在是蓝色链接样式）
4. 弹出统计图表对话框
5. 查看图表数据和趋势

### 4. 功能特点展示
- **独立组件**: 可在其他页面重复使用
- **最小侵入**: 对原有代码修改很少
- **多类型支持**: 支持APP、广告、广告计划三种类型
- **数据合并**: 广告计划类型会合并显示关联广告数据
- **响应式设计**: 适配不同屏幕尺寸

### 5. 技术实现亮点
- 使用 ECharts 实现专业的数据可视化
- 通过 API 动态获取统计数据
- 智能处理广告计划的关联数据
- 优雅的错误处理和加载状态

## 测试数据说明

已在数据库中插入测试数据：
- 广告计划ID=1的统计数据（3天的展示数和点击数）
- 广告ID=1的统计数据（用于测试广告类型图表）
- 数据涵盖2025-06-15到2025-06-17三天

## 使用场景

1. **广告主**: 查看广告计划的投放效果
2. **运营人员**: 分析广告数据趋势
3. **决策者**: 基于数据做出优化决策

## 扩展可能

1. 添加更多图表类型（饼图、柱状图等）
2. 支持时间范围选择
3. 添加数据导出功能
4. 支持实时数据更新
5. 添加数据对比功能
