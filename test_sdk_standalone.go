package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
)

// 测试SDK接口的独立脚本

type GetAdsRequest struct {
	PackageId    string `json:"package_id,omitempty"`
	AdPositionId int    `json:"ad_position_id"`
	AdTypeId     int    `json:"ad_type_id"`
}

type AdEventRequest struct {
	Events []AdEvent `json:"events"`
}

type AdEvent struct {
	PackageId    string `json:"package_id"`
	CampaignId   int    `json:"campaign_id"`
	AdId         int    `json:"ad_id"`
	ActionType   int    `json:"action_type"`
	AdPositionId int    `json:"ad_position_id"`
}

func main() {
	baseURL := "http://localhost:8888"
	
	// 测试1: 获取指定APP的广告列表
	fmt.Println("=== 测试1: 获取指定APP的广告列表 ===")
	testGetAdsWithApp(baseURL)
	
	// 测试2: 获取所有APP的广告列表（不指定package_id）
	fmt.Println("\n=== 测试2: 获取所有APP的广告列表 ===")
	testGetAdsWithoutApp(baseURL)
	
	// 测试3: 上报广告事件
	fmt.Println("\n=== 测试3: 上报广告事件 ===")
	testReportEvents(baseURL)
}

func testGetAdsWithApp(baseURL string) {
	req := GetAdsRequest{
		PackageId:    "com.example.testapp",
		AdPositionId: 1,
		AdTypeId:     1,
	}
	
	jsonData, _ := json.Marshal(req)
	resp, err := http.Post(baseURL+"/sdk/ads", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)
	
	fmt.Printf("响应状态: %d\n", resp.StatusCode)
	fmt.Printf("响应内容: %+v\n", result)
}

func testGetAdsWithoutApp(baseURL string) {
	req := GetAdsRequest{
		// 不设置PackageId，获取所有APP的广告
		AdPositionId: 1,
		AdTypeId:     1,
	}
	
	jsonData, _ := json.Marshal(req)
	resp, err := http.Post(baseURL+"/sdk/ads", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)
	
	fmt.Printf("响应状态: %d\n", resp.StatusCode)
	fmt.Printf("响应内容: %+v\n", result)
}

func testReportEvents(baseURL string) {
	req := AdEventRequest{
		Events: []AdEvent{
			{
				PackageId:    "com.example.testapp",
				CampaignId:   1,
				AdId:         1,
				ActionType:   1, // 展示
				AdPositionId: 1,
			},
			{
				PackageId:    "com.example.testapp",
				CampaignId:   1,
				AdId:         1,
				ActionType:   2, // 点击
				AdPositionId: 1,
			},
		},
	}
	
	jsonData, _ := json.Marshal(req)
	resp, err := http.Post(baseURL+"/sdk/report", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()
	
	var result map[string]interface{}
	json.NewDecoder(resp.Body).Decode(&result)
	
	fmt.Printf("响应状态: %d\n", resp.StatusCode)
	fmt.Printf("响应内容: %+v\n", result)
}
